#!/usr/bin/env python3
"""
Demo script for Phase 11.2 Electronic Bank Reconciliation functionality
Demonstrates the comprehensive bank reconciliation features including automatic matching,
manual matching, and reconciliation workflow.
"""

import csv
import os
import tempfile
from datetime import datetime, timedelta

from model.account import Account
from model.database import Database
from model.transaction import TransactionManager
from utils.bank_reconciliation_manager import BankReconciliationManager
from utils.database_migration import DatabaseMigration


def get_existing_company_or_create_temp():
    """Get an existing company database or create a temporary one for demo"""
    print("Looking for existing companies...")

    # Get all company databases (similar to client_frame.py logic)
    companies = []
    excluded_files = ['users.db', 'settings.db']
    db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

    for db_file in db_files:
        try:
            # Extract company name from filename
            company_name = db_file.replace('.db', '').replace('_', ' ').title()
            companies.append({
                'name': company_name,
                'db_path': db_file
            })
        except Exception as e:
            print(f"Error loading company {db_file}: {e}")

    if companies:
        # Use the first existing company
        company = companies[0]
        db_path = company['db_path']
        print(f"Using existing company: {company['name']}")

        # Run database migration to ensure all new fields are present
        print("Running database migration...")
        DatabaseMigration.migrate_company_database(db_path)

        # Get existing accounts or create demo accounts
        account_manager = Account(db_path)
        accounts = account_manager.get_all_accounts()

        if accounts:
            # Use existing accounts
            checking_account = next((acc for acc in accounts if acc['type'] in ['checking', 'bank']), accounts[0])
            checking_account_id = checking_account['id']
            print(f"Using existing account: {checking_account['name']}")

            # Try to find a second account for savings
            savings_account = next((acc for acc in accounts if acc['id'] != checking_account_id), None)
            savings_account_id = savings_account['id'] if savings_account else None

        else:
            # Create demo accounts in existing company
            checking_account_id = account_manager.create_account(
                name="Demo Checking Account",
                account_type="checking",
                currency="USD",
                opening_balance=5000.00,
                description="Demo checking account for reconciliation"
            )

            savings_account_id = account_manager.create_account(
                name="Demo Savings Account",
                account_type="savings",
                currency="USD",
                opening_balance=10000.00,
                description="Demo savings account"
            )
            print("Created demo accounts in existing company")

        return db_path, checking_account_id, savings_account_id, False  # False = not temporary

    else:
        # Create temporary database for demo
        print("No existing companies found. Creating temporary demo database...")
        import tempfile

        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        db_path = temp_db.name

        # Initialize temporary database
        db_manager = Database()

        # Create basic database structure
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Create basic tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL,
                description TEXT,
                created_date TEXT,
                classification TEXT,
                account_number TEXT,
                parent_id INTEGER,
                is_active INTEGER DEFAULT 1
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                currency_code TEXT DEFAULT 'USD',
                exchange_rate REAL DEFAULT 1.0,
                base_amount REAL,
                import_hash TEXT,
                bank_reference TEXT,
                import_source TEXT,
                import_date TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)

        # Insert default category
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("General", "expense"))

        conn.commit()
        conn.close()

        # Run database migration to add reconciliation tables
        print("Running database migration...")
        DatabaseMigration.migrate_company_database(db_path)

        # Add sample accounts
        account_manager = Account(db_path)
        checking_account_id = account_manager.create_account(
            name="Demo Checking Account",
            account_type="checking",
            currency="USD",
            opening_balance=5000.00,
            description="Demo checking account for reconciliation"
        )

        savings_account_id = account_manager.create_account(
            name="Demo Savings Account",
            account_type="savings",
            currency="USD",
            opening_balance=10000.00,
            description="Demo savings account"
        )

        print("Created temporary demo database with sample accounts")
        return db_path, checking_account_id, savings_account_id, True  # True = temporary


def create_sample_book_transactions(db_path, account_id):
    """Create sample book transactions for reconciliation"""
    print("Creating sample book transactions...")
    
    transaction_manager = TransactionManager(db_path)
    
    # Sample transactions for the past month
    base_date = datetime.now() - timedelta(days=30)
    
    transactions = [
        {
            'date': (base_date + timedelta(days=1)).strftime('%Y-%m-%d'),
            'amount': 2500.00,
            'description': 'Monthly Salary Deposit',
            'type': 'income'
        },
        {
            'date': (base_date + timedelta(days=3)).strftime('%Y-%m-%d'),
            'amount': 125.50,
            'description': 'Grocery Store Purchase',
            'type': 'expense'
        },
        {
            'date': (base_date + timedelta(days=5)).strftime('%Y-%m-%d'),
            'amount': 1200.00,
            'description': 'Office Rent Payment',
            'type': 'expense'
        },
        {
            'date': (base_date + timedelta(days=7)).strftime('%Y-%m-%d'),
            'amount': 75.25,
            'description': 'Utility Bill Payment',
            'type': 'expense'
        },
        {
            'date': (base_date + timedelta(days=10)).strftime('%Y-%m-%d'),
            'amount': 1500.00,
            'description': 'Client Payment Received',
            'type': 'income'
        },
        {
            'date': (base_date + timedelta(days=12)).strftime('%Y-%m-%d'),
            'amount': 89.99,
            'description': 'Office Supplies',
            'type': 'expense'
        },
        {
            'date': (base_date + timedelta(days=15)).strftime('%Y-%m-%d'),
            'amount': 45.00,
            'description': 'Gas Station',
            'type': 'expense'
        },
        {
            'date': (base_date + timedelta(days=18)).strftime('%Y-%m-%d'),
            'amount': 3000.00,
            'description': 'Consulting Fee Received',
            'type': 'income'
        },
        {
            'date': (base_date + timedelta(days=20)).strftime('%Y-%m-%d'),
            'amount': 250.00,
            'description': 'Insurance Payment',
            'type': 'expense'
        },
        {
            'date': (base_date + timedelta(days=25)).strftime('%Y-%m-%d'),
            'amount': 156.78,
            'description': 'Internet Service',
            'type': 'expense'
        }
    ]
    
    transaction_ids = []
    for trans in transactions:
        trans_id = transaction_manager.add_transaction(
            date=trans['date'],
            amount=trans['amount'],
            account_id=account_id,
            description=trans['description'],
            type_name=trans['type']
        )
        transaction_ids.append(trans_id)
    
    print(f"Created {len(transaction_ids)} book transactions")
    return transaction_ids


def create_sample_bank_statement(base_date):
    """Create a sample bank statement CSV file"""
    print("Creating sample bank statement...")
    
    # Create temporary CSV file
    bank_statement_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
    csv_writer = csv.writer(bank_statement_file)
    
    # Write header
    csv_writer.writerow(["Date", "Description", "Amount", "Balance"])
    
    # Sample bank statement transactions (some match book transactions, some don't)
    running_balance = 5000.00
    
    transactions = [
        {
            'date': (base_date + timedelta(days=1)).strftime('%d/%m/%Y'),
            'description': 'DIRECT CREDIT - Monthly Salary Deposit',
            'amount': 2500.00
        },
        {
            'date': (base_date + timedelta(days=3)).strftime('%d/%m/%Y'),
            'description': 'EFTPOS - Grocery Store Purchase',
            'amount': -125.50
        },
        {
            'date': (base_date + timedelta(days=4)).strftime('%d/%m/%Y'),
            'description': 'BANK FEE - Monthly Account Fee',
            'amount': -15.00
        },
        {
            'date': (base_date + timedelta(days=5)).strftime('%d/%m/%Y'),
            'description': 'ONLINE TRANSFER - Office Rent Payment',
            'amount': -1200.00
        },
        {
            'date': (base_date + timedelta(days=7)).strftime('%d/%m/%Y'),
            'description': 'DIRECT DEBIT - Utility Bill Payment',
            'amount': -75.25
        },
        {
            'date': (base_date + timedelta(days=10)).strftime('%d/%m/%Y'),
            'description': 'DEPOSIT - Client Payment Received',
            'amount': 1500.00
        },
        {
            'date': (base_date + timedelta(days=12)).strftime('%d/%m/%Y'),
            'description': 'EFTPOS - Office Supplies',
            'amount': -89.99
        },
        {
            'date': (base_date + timedelta(days=14)).strftime('%d/%m/%Y'),
            'description': 'ATM WITHDRAWAL',
            'amount': -100.00
        },
        {
            'date': (base_date + timedelta(days=15)).strftime('%d/%m/%Y'),
            'description': 'EFTPOS - Gas Station',
            'amount': -45.00
        },
        {
            'date': (base_date + timedelta(days=18)).strftime('%d/%m/%Y'),
            'description': 'WIRE TRANSFER - Consulting Fee Received',
            'amount': 3000.00
        },
        {
            'date': (base_date + timedelta(days=20)).strftime('%d/%m/%Y'),
            'description': 'AUTOMATIC PAYMENT - Insurance Payment',
            'amount': -250.00
        },
        {
            'date': (base_date + timedelta(days=22)).strftime('%d/%m/%Y'),
            'description': 'INTEREST PAYMENT',
            'amount': 12.50
        },
        {
            'date': (base_date + timedelta(days=25)).strftime('%d/%m/%Y'),
            'description': 'DIRECT DEBIT - Internet Service',
            'amount': -156.78
        }
    ]
    
    for trans in transactions:
        running_balance += trans['amount']
        csv_writer.writerow([
            trans['date'],
            trans['description'],
            trans['amount'],
            f"{running_balance:.2f}"
        ])
    
    bank_statement_file.close()
    
    print(f"Created bank statement with {len(transactions)} transactions")
    print(f"Final statement balance: ${running_balance:.2f}")
    
    return bank_statement_file.name, running_balance


def demo_reconciliation_workflow(db_path, account_id):
    """Demonstrate the complete reconciliation workflow"""
    print("\n" + "="*60)
    print("DEMO: Complete Bank Reconciliation Workflow")
    print("="*60)
    
    reconciliation_manager = BankReconciliationManager(db_path)
    
    # Calculate statement date (end of last month)
    base_date = datetime.now() - timedelta(days=30)
    statement_date = (base_date + timedelta(days=25)).strftime('%Y-%m-%d')
    
    # Create sample bank statement
    bank_statement_file, statement_balance = create_sample_bank_statement(base_date)
    
    try:
        # Step 1: Clean up any existing pending reconciliations and start new one
        print(f"\n1. Starting reconciliation for account {account_id}")
        print(f"   Statement Date: {statement_date}")
        print(f"   Statement Balance: ${statement_balance:.2f}")

        # Check for existing pending reconciliations and cancel them
        try:
            existing_sessions = reconciliation_manager.reconciliation_session.get_all_reconciliation_sessions(
                account_id=account_id, status='pending'
            )

            if existing_sessions:
                print(f"   Found {len(existing_sessions)} existing pending reconciliation(s). Cancelling them...")
                for session in existing_sessions:
                    reconciliation_manager.cancel_reconciliation(session['id'], "Cancelled for demo")
                print(f"   ✓ Cancelled existing reconciliation sessions")
        except Exception as e:
            print(f"   Warning: Could not check for existing sessions: {str(e)}")

        session_id = reconciliation_manager.start_reconciliation(
            account_id, statement_date, statement_balance, "demo_user"
        )
        
        print(f"   ✓ Reconciliation session created: {session_id}")
        
        # Step 2: Upload bank statement
        print(f"\n2. Uploading bank statement from: {bank_statement_file}")
        
        upload_result = reconciliation_manager.upload_bank_statement(
            session_id, bank_statement_file
        )
        
        print(f"   ✓ Uploaded {upload_result['transactions_added']} bank transactions")
        
        # Step 3: Get initial dashboard
        print(f"\n3. Getting reconciliation dashboard...")
        
        dashboard = reconciliation_manager.get_reconciliation_dashboard(session_id)
        
        print(f"   Account: {dashboard['session']['account_name']}")
        print(f"   Statement Balance: ${dashboard['session']['statement_balance']:.2f}")
        print(f"   Book Balance: ${dashboard['session']['book_balance']:.2f}")
        print(f"   Difference: ${dashboard['session']['difference']:.2f}")
        print(f"   Bank Transactions: {dashboard['summary']['bank_transactions']['total']}")
        print(f"   Unmatched Bank: {dashboard['summary']['bank_transactions']['unmatched']}")
        print(f"   Unreconciled Book: {dashboard['summary']['book_transactions']['total']}")
        
        # Step 4: Auto-match transactions
        print(f"\n4. Running automatic transaction matching...")
        
        auto_match_result = reconciliation_manager.auto_match_transactions(session_id, 0.8)
        
        print(f"   ✓ Automatically matched {auto_match_result['matches_made']} transactions")
        
        # Step 5: Get updated dashboard
        print(f"\n5. Getting updated reconciliation status...")
        
        dashboard = reconciliation_manager.get_reconciliation_dashboard(session_id)
        
        print(f"   Matched Bank Transactions: {dashboard['summary']['bank_transactions']['matched']}")
        print(f"   Unmatched Bank Transactions: {dashboard['summary']['bank_transactions']['unmatched']}")
        print(f"   Average Match Confidence: {dashboard['summary']['match_statistics']['avg_confidence']:.2f}")
        
        # Step 6: Show unmatched transactions
        if dashboard['unmatched_bank_transactions']:
            print(f"\n6. Unmatched Bank Transactions:")
            for trans in dashboard['unmatched_bank_transactions'][:5]:  # Show first 5
                print(f"   - {trans['date']}: {trans['description'][:40]}... (${trans['amount']:.2f})")
        
        if dashboard['unreconciled_book_transactions']:
            print(f"\n   Unreconciled Book Transactions:")
            for trans in dashboard['unreconciled_book_transactions'][:5]:  # Show first 5
                print(f"   - {trans['date']}: {trans['description'][:40]}... (${trans['amount']:.2f})")
        
        # Step 7: Generate reconciliation report
        print(f"\n7. Generating reconciliation report...")
        
        report = reconciliation_manager.get_reconciliation_report(session_id)
        
        print(f"   Report generated at: {report['generated_date']}")
        print(f"   Total matches: {report['summary']['matches']['total']}")
        print(f"   Reconciliation difference: ${report['reconciliation_difference']:.2f}")
        
        # Step 8: Demonstrate manual matching (if there are unmatched transactions)
        if dashboard['unmatched_bank_transactions'] and dashboard['unreconciled_book_transactions']:
            print(f"\n8. Demonstrating manual matching...")
            
            bank_trans = dashboard['unmatched_bank_transactions'][0]
            book_trans = dashboard['unreconciled_book_transactions'][0]
            
            print(f"   Manually matching:")
            print(f"   Bank: {bank_trans['description'][:30]}... (${bank_trans['amount']:.2f})")
            print(f"   Book: {book_trans['description'][:30]}... (${book_trans['amount']:.2f})")
            
            # Find potential matches
            potential_matches = reconciliation_manager.find_potential_matches(
                session_id, bank_trans['id']
            )
            
            if potential_matches:
                print(f"   Found {len(potential_matches)} potential matches")
                best_match = potential_matches[0]
                print(f"   Best match confidence: {best_match['confidence']:.2f}")
                
                # Create manual match
                success = reconciliation_manager.create_manual_match(
                    session_id, bank_trans['id'], best_match['transaction']['id'],
                    "Demo manual match"
                )
                
                if success:
                    print(f"   ✓ Manual match created successfully")
        
        # Step 9: Try to complete reconciliation
        print(f"\n9. Attempting to complete reconciliation...")
        
        try:
            completion_result = reconciliation_manager.complete_reconciliation(session_id)
            print(f"   ✓ Reconciliation completed successfully!")
            print(f"   Completion date: {completion_result['completion_date']}")
        except Exception as e:
            print(f"   ⚠ Cannot complete reconciliation: {str(e)}")
            print(f"   This is expected if there are still unmatched transactions")
            
            # Cancel the reconciliation instead
            print(f"\n   Cancelling reconciliation for demo purposes...")
            cancel_success = reconciliation_manager.cancel_reconciliation(
                session_id, "Demo cancellation"
            )
            
            if cancel_success:
                print(f"   ✓ Reconciliation cancelled successfully")
        
        print(f"\n" + "="*60)
        print("RECONCILIATION WORKFLOW DEMO COMPLETED!")
        print("="*60)
        print("Key features demonstrated:")
        print("- Reconciliation session management")
        print("- Bank statement upload and processing")
        print("- Automatic transaction matching with confidence scoring")
        print("- Manual transaction matching capabilities")
        print("- Comprehensive reconciliation reporting")
        print("- Reconciliation completion and cancellation workflows")
        
    finally:
        # Clean up
        if os.path.exists(bank_statement_file):
            os.unlink(bank_statement_file)


def demo_matching_algorithms(db_path, account_id):
    """Demonstrate the transaction matching algorithms"""
    print("\n" + "="*60)
    print("DEMO: Transaction Matching Algorithms")
    print("="*60)
    
    from model.bank_reconciliation import BankReconciliationSession
    from model.bank_statement_transaction import BankStatementTransaction
    from utils.transaction_matcher import TransactionMatcher
    
    matcher = TransactionMatcher(db_path)
    bank_trans_manager = BankStatementTransaction(db_path)
    reconciliation_session = BankReconciliationSession(db_path)
    
    # Create a test reconciliation session
    session_id = reconciliation_session.create_reconciliation_session(
        account_id, "2024-01-31", 1000.00, "demo_user"
    )
    
    # Add a bank transaction
    bank_trans_id = bank_trans_manager.add_bank_statement_transaction(
        session_id, "2024-01-15", "Grocery Store Purchase", -125.50, "REF001"
    )
    
    bank_transaction = bank_trans_manager.get_transaction_by_id(bank_trans_id)
    
    print(f"Finding matches for bank transaction:")
    print(f"  Date: {bank_transaction['date']}")
    print(f"  Description: {bank_transaction['description']}")
    print(f"  Amount: ${bank_transaction['amount']:.2f}")
    
    # Find potential matches
    potential_matches = matcher.find_matches_for_bank_transaction(bank_transaction, session_id)
    
    print(f"\nFound {len(potential_matches)} potential matches:")
    
    for i, match in enumerate(potential_matches[:5], 1):  # Show top 5 matches
        trans = match['transaction']
        print(f"  {i}. Confidence: {match['confidence']:.3f} | Type: {match['match_type']}")
        print(f"     Date: {trans['date']} | Amount: ${trans['amount']:.2f}")
        print(f"     Description: {trans['description'][:50]}...")
        print()
    
    # Demonstrate matching statistics
    if potential_matches:
        print("Matching Algorithm Details:")
        print(f"- Date tolerance: {matcher.date_tolerance_days} days")
        print(f"- Amount tolerance: {matcher.amount_tolerance_percent * 100}%")
        print(f"- High confidence threshold: {matcher.high_confidence_threshold}")
        print(f"- Medium confidence threshold: {matcher.medium_confidence_threshold}")
        print(f"- Low confidence threshold: {matcher.low_confidence_threshold}")


def cleanup_demo_files(db_path, is_temporary):
    """Clean up demo files"""
    print("\n" + "="*60)
    print("Cleaning up demo files...")

    if is_temporary and os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed temporary demo database: {db_path}")
    elif not is_temporary:
        print("Demo used existing company database - no cleanup needed")


def main():
    """Main demo function"""
    print("="*60)
    print("PHASE 11.2 ELECTRONIC BANK RECONCILIATION DEMO")
    print("="*60)
    print("This demo showcases the comprehensive bank reconciliation functionality:")
    print("- Intelligent automatic transaction matching")
    print("- Manual transaction matching interface")
    print("- Comprehensive reconciliation workflow")
    print("- Advanced matching algorithms with confidence scoring")
    print("- Reconciliation reporting and audit trails")
    print("\nNOTE: This demo will use an existing company if available,")
    print("or create a temporary database for demonstration purposes.")

    try:
        # Get existing company or create temporary database
        db_path, checking_account_id, savings_account_id, is_temporary = get_existing_company_or_create_temp()

        # Create sample book transactions
        create_sample_book_transactions(db_path, checking_account_id)

        # Demonstrate reconciliation workflow
        demo_reconciliation_workflow(db_path, checking_account_id)

        # Demonstrate matching algorithms
        demo_matching_algorithms(db_path, checking_account_id)

        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("All bank reconciliation features have been demonstrated.")
        print("The system provides:")
        print("- Professional-grade reconciliation capabilities")
        print("- Intelligent transaction matching")
        print("- Comprehensive audit trails")
        print("- Flexible manual override options")

        # Handle cleanup based on whether we used temporary or existing database
        if is_temporary:
            keep_files = input("\nKeep temporary demo files for inspection? (y/n): ").lower().strip()
            if keep_files != 'y':
                cleanup_demo_files(db_path, is_temporary)
            else:
                print(f"\nTemporary demo database kept: {db_path}")
        else:
            print(f"\nDemo used existing company database: {db_path}")
            print("Demo transactions and reconciliation data have been added to your existing company.")

    except Exception as e:
        print(f"\nDemo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
