import tkinter as tk
from tkinter import messagebox, ttk as tkinter_ttk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from datetime import datetime, date
import sqlite3

from model.currency import Currency


class CurrencyManagementFrame(ttk.Frame):
    """Frame for managing currencies and exchange rates"""

    def __init__(self, parent, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = "Currency Management"

        # Create currency model
        self.currency_model = Currency(db_path)

        # Variables
        self.selected_currency = None
        self.selected_rate = None

        self.create_widgets()
        self.load_currencies()
        self.load_exchange_rates()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Create notebook for different sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, pady=(0, 20))

        # Create tabs
        self.create_currencies_tab()
        self.create_exchange_rates_tab()
        self.create_conversion_tab()
        self.create_gains_losses_tab()

    def create_currencies_tab(self):
        """Create currencies management tab"""
        currencies_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(currencies_frame, text="Currencies")

        # Left side - Currency list
        left_frame = ttk.Frame(currencies_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))

        ttk.Label(left_frame, text="Available Currencies", font=("Segoe UI", 12, "bold")).pack(anchor="w", pady=(0, 10))

        # Currency treeview
        columns = ("Code", "Name", "Symbol", "Decimals", "Base", "Active")
        self.currency_tree = ttk.Treeview(left_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.currency_tree.heading(col, text=col)
            self.currency_tree.column(col, width=80)

        self.currency_tree.pack(fill="both", expand=True)
        self.currency_tree.bind("<<TreeviewSelect>>", self.on_currency_select)

        # Currency buttons
        currency_buttons_frame = ttk.Frame(left_frame)
        currency_buttons_frame.pack(fill="x", pady=(10, 0))

        ttk.Button(currency_buttons_frame, text="Add Currency", command=self.add_currency, bootstyle=PRIMARY).pack(side="left", padx=(0, 5))
        ttk.Button(currency_buttons_frame, text="Edit Currency", command=self.edit_currency, bootstyle=INFO).pack(side="left", padx=(0, 5))
        ttk.Button(currency_buttons_frame, text="Set as Base", command=self.set_base_currency, bootstyle=SUCCESS).pack(side="left", padx=(0, 5))
        ttk.Button(currency_buttons_frame, text="Deactivate", command=self.deactivate_currency, bootstyle=WARNING).pack(side="left")

        # Right side - Currency form
        right_frame = ttk.LabelFrame(currencies_frame, text="Currency Details", padding=20)
        right_frame.pack(side="right", fill="y", padx=(10, 0))

        # Currency form
        self.currency_code_var = tk.StringVar()
        self.currency_name_var = tk.StringVar()
        self.currency_symbol_var = tk.StringVar()
        self.currency_decimals_var = tk.IntVar(value=2)
        self.currency_is_base_var = tk.BooleanVar()
        self.currency_is_active_var = tk.BooleanVar(value=True)

        ttk.Label(right_frame, text="Currency Code:").grid(row=0, column=0, sticky="w", pady=5)
        ttk.Entry(right_frame, textvariable=self.currency_code_var, width=20).grid(row=0, column=1, sticky="ew", pady=5)

        ttk.Label(right_frame, text="Currency Name:").grid(row=1, column=0, sticky="w", pady=5)
        ttk.Entry(right_frame, textvariable=self.currency_name_var, width=20).grid(row=1, column=1, sticky="ew", pady=5)

        ttk.Label(right_frame, text="Symbol:").grid(row=2, column=0, sticky="w", pady=5)
        ttk.Entry(right_frame, textvariable=self.currency_symbol_var, width=20).grid(row=2, column=1, sticky="ew", pady=5)

        ttk.Label(right_frame, text="Decimal Places:").grid(row=3, column=0, sticky="w", pady=5)
        ttk.Spinbox(right_frame, from_=0, to=6, textvariable=self.currency_decimals_var, width=18).grid(row=3, column=1, sticky="ew", pady=5)

        ttk.Checkbutton(right_frame, text="Base Currency", variable=self.currency_is_base_var).grid(row=4, column=0, columnspan=2, sticky="w", pady=5)
        ttk.Checkbutton(right_frame, text="Active", variable=self.currency_is_active_var).grid(row=5, column=0, columnspan=2, sticky="w", pady=5)

        # Form buttons
        form_buttons_frame = ttk.Frame(right_frame)
        form_buttons_frame.grid(row=6, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(form_buttons_frame, text="Save", command=self.save_currency, bootstyle=PRIMARY).pack(side="left", padx=(0, 5))
        ttk.Button(form_buttons_frame, text="Clear", command=self.clear_currency_form, bootstyle=SECONDARY).pack(side="left")

        right_frame.columnconfigure(1, weight=1)

    def create_exchange_rates_tab(self):
        """Create exchange rates management tab"""
        rates_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(rates_frame, text="Exchange Rates")

        # Top section - Add/Edit rates
        top_frame = ttk.LabelFrame(rates_frame, text="Exchange Rate Entry", padding=15)
        top_frame.pack(fill="x", pady=(0, 20))

        # Rate form
        self.from_currency_var = tk.StringVar()
        self.to_currency_var = tk.StringVar()
        self.rate_var = tk.DoubleVar()
        self.rate_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.rate_source_var = tk.StringVar(value="manual")

        # Form layout
        form_frame = ttk.Frame(top_frame)
        form_frame.pack(fill="x")

        ttk.Label(form_frame, text="From:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        from_combo = ttk.Combobox(form_frame, textvariable=self.from_currency_var, width=10, state="readonly")
        from_combo.grid(row=0, column=1, padx=(0, 10))

        ttk.Label(form_frame, text="To:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        to_combo = ttk.Combobox(form_frame, textvariable=self.to_currency_var, width=10, state="readonly")
        to_combo.grid(row=0, column=3, padx=(0, 10))

        ttk.Label(form_frame, text="Rate:").grid(row=0, column=4, sticky="w", padx=(0, 5))
        ttk.Entry(form_frame, textvariable=self.rate_var, width=12).grid(row=0, column=5, padx=(0, 10))

        ttk.Label(form_frame, text="Date:").grid(row=0, column=6, sticky="w", padx=(0, 5))
        ttk.Entry(form_frame, textvariable=self.rate_date_var, width=12).grid(row=0, column=7, padx=(0, 10))

        ttk.Button(form_frame, text="Add Rate", command=self.add_exchange_rate, bootstyle=PRIMARY).grid(row=0, column=8, padx=(10, 0))

        # Store combo references for updating
        self.from_currency_combo = from_combo
        self.to_currency_combo = to_combo

        # Bottom section - Rates list
        bottom_frame = ttk.Frame(rates_frame)
        bottom_frame.pack(fill="both", expand=True)

        ttk.Label(bottom_frame, text="Current Exchange Rates", font=("Segoe UI", 12, "bold")).pack(anchor="w", pady=(0, 10))

        # Exchange rates treeview
        rate_columns = ("From", "To", "Rate", "Date", "Source")
        self.rates_tree = ttk.Treeview(bottom_frame, columns=rate_columns, show="headings", height=10)

        for col in rate_columns:
            self.rates_tree.heading(col, text=col)
            self.rates_tree.column(col, width=100)

        self.rates_tree.pack(fill="both", expand=True)
        self.rates_tree.bind("<<TreeviewSelect>>", self.on_rate_select)

        # Rate buttons
        rate_buttons_frame = ttk.Frame(bottom_frame)
        rate_buttons_frame.pack(fill="x", pady=(10, 0))

        ttk.Button(rate_buttons_frame, text="Update Rates", command=self.update_rates_from_api, bootstyle=INFO).pack(side="left", padx=(0, 5))
        ttk.Button(rate_buttons_frame, text="Delete Rate", command=self.delete_exchange_rate, bootstyle=DANGER).pack(side="left")

    def create_conversion_tab(self):
        """Create currency conversion tab"""
        conversion_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(conversion_frame, text="Currency Converter")

        # Conversion calculator
        calc_frame = ttk.LabelFrame(conversion_frame, text="Currency Converter", padding=20)
        calc_frame.pack(fill="x", pady=(0, 20))

        # Conversion form
        self.conv_amount_var = tk.DoubleVar(value=1.0)
        self.conv_from_var = tk.StringVar()
        self.conv_to_var = tk.StringVar()
        self.conv_result_var = tk.StringVar()

        ttk.Label(calc_frame, text="Amount:").grid(row=0, column=0, sticky="w", padx=(0, 10), pady=10)
        ttk.Entry(calc_frame, textvariable=self.conv_amount_var, width=15).grid(row=0, column=1, padx=(0, 10), pady=10)

        ttk.Label(calc_frame, text="From:").grid(row=0, column=2, sticky="w", padx=(0, 10), pady=10)
        conv_from_combo = ttk.Combobox(calc_frame, textvariable=self.conv_from_var, width=10, state="readonly")
        conv_from_combo.grid(row=0, column=3, padx=(0, 10), pady=10)

        ttk.Label(calc_frame, text="To:").grid(row=0, column=4, sticky="w", padx=(0, 10), pady=10)
        conv_to_combo = ttk.Combobox(calc_frame, textvariable=self.conv_to_var, width=10, state="readonly")
        conv_to_combo.grid(row=0, column=5, padx=(0, 10), pady=10)

        ttk.Button(calc_frame, text="Convert", command=self.convert_currency, bootstyle=PRIMARY).grid(row=0, column=6, padx=(10, 0), pady=10)

        # Result
        result_frame = ttk.Frame(calc_frame)
        result_frame.grid(row=1, column=0, columnspan=7, pady=(20, 0))

        ttk.Label(result_frame, text="Result:", font=("Segoe UI", 12, "bold")).pack(side="left")
        result_label = ttk.Label(result_frame, textvariable=self.conv_result_var, font=("Segoe UI", 14, "bold"), foreground="blue")
        result_label.pack(side="left", padx=(10, 0))

        # Store combo references
        self.conv_from_combo = conv_from_combo
        self.conv_to_combo = conv_to_combo

    def create_gains_losses_tab(self):
        """Create gains/losses tracking tab"""
        gains_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(gains_frame, text="Gains & Losses")

        # Controls
        controls_frame = ttk.Frame(gains_frame)
        controls_frame.pack(fill="x", pady=(0, 20))

        ttk.Button(controls_frame, text="Calculate Unrealized Gains/Losses", 
                  command=self.calculate_unrealized_gains, bootstyle=INFO).pack(side="left", padx=(0, 10))

        ttk.Button(controls_frame, text="Refresh", command=self.load_gains_losses, bootstyle=SECONDARY).pack(side="left")

        # Gains/Losses display
        gains_columns = ("Account", "Currency", "Amount", "Base Amount", "Rate", "Gain/Loss", "Type", "Date")
        self.gains_tree = ttk.Treeview(gains_frame, columns=gains_columns, show="headings", height=12)

        for col in gains_columns:
            self.gains_tree.heading(col, text=col)
            self.gains_tree.column(col, width=100)

        self.gains_tree.pack(fill="both", expand=True)

    def load_currencies(self):
        """Load currencies into the treeview"""
        # Clear existing items
        for item in self.currency_tree.get_children():
            self.currency_tree.delete(item)

        # Load currencies
        currencies = self.currency_model.get_all_currencies(active_only=False)
        for currency in currencies:
            values = (
                currency['code'],
                currency['name'],
                currency['symbol'],
                currency['decimal_places'],
                "Yes" if currency['is_base_currency'] else "No",
                "Yes" if currency['is_active'] else "No"
            )
            self.currency_tree.insert("", "end", values=values)

        # Update combo boxes
        active_currencies = [c['code'] for c in currencies if c['is_active']]
        
        if hasattr(self, 'from_currency_combo'):
            self.from_currency_combo['values'] = active_currencies
            self.to_currency_combo['values'] = active_currencies
            self.conv_from_combo['values'] = active_currencies
            self.conv_to_combo['values'] = active_currencies

    def load_exchange_rates(self):
        """Load exchange rates into the treeview"""
        # Clear existing items
        for item in self.rates_tree.get_children():
            self.rates_tree.delete(item)

        # Load rates from database
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT from_currency_code, to_currency_code, rate, rate_date, source
                FROM exchange_rates 
                WHERE is_active = 1
                ORDER BY rate_date DESC, from_currency_code, to_currency_code
            ''')

            for row in cursor.fetchall():
                self.rates_tree.insert("", "end", values=row)

        except sqlite3.Error as e:
            messagebox.showerror("Error", f"Failed to load exchange rates: {str(e)}")
        finally:
            if conn:
                conn.close()

    def load_gains_losses(self):
        """Load gains/losses into the treeview"""
        # Clear existing items
        for item in self.gains_tree.get_children():
            self.gains_tree.delete(item)

        # Load gains/losses from database
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT a.name, cgl.currency_code, cgl.original_amount, 
                       cgl.base_amount, cgl.exchange_rate, cgl.gain_loss_amount,
                       cgl.gain_loss_type, cgl.calculation_date
                FROM currency_gains_losses cgl
                JOIN accounts a ON cgl.account_id = a.id
                ORDER BY cgl.calculation_date DESC
            ''')

            for row in cursor.fetchall():
                # Format the values for display
                formatted_row = (
                    row[0],  # Account name
                    row[1],  # Currency
                    f"{row[2]:.2f}",  # Original amount
                    f"{row[3]:.2f}",  # Base amount
                    f"{row[4]:.6f}",  # Exchange rate
                    f"{row[5]:.2f}",  # Gain/Loss
                    row[6],  # Type
                    row[7]   # Date
                )
                self.gains_tree.insert("", "end", values=formatted_row)

        except sqlite3.Error as e:
            messagebox.showerror("Error", f"Failed to load gains/losses: {str(e)}")
        finally:
            if conn:
                conn.close()

    def on_currency_select(self, event):
        """Handle currency selection"""
        selection = self.currency_tree.selection()
        if selection:
            item = self.currency_tree.item(selection[0])
            values = item['values']
            
            self.currency_code_var.set(values[0])
            self.currency_name_var.set(values[1])
            self.currency_symbol_var.set(values[2])
            self.currency_decimals_var.set(values[3])
            self.currency_is_base_var.set(values[4] == "Yes")
            self.currency_is_active_var.set(values[5] == "Yes")

    def on_rate_select(self, event):
        """Handle exchange rate selection"""
        selection = self.rates_tree.selection()
        if selection:
            item = self.rates_tree.item(selection[0])
            values = item['values']
            
            self.from_currency_var.set(values[0])
            self.to_currency_var.set(values[1])
            self.rate_var.set(float(values[2]))
            self.rate_date_var.set(values[3])
            self.rate_source_var.set(values[4])

    def add_currency(self):
        """Add a new currency"""
        self.clear_currency_form()

    def edit_currency(self):
        """Edit selected currency"""
        if not self.currency_tree.selection():
            messagebox.showwarning("Warning", "Please select a currency to edit")

    def save_currency(self):
        """Save currency"""
        try:
            code = self.currency_code_var.get().strip().upper()
            name = self.currency_name_var.get().strip()
            symbol = self.currency_symbol_var.get().strip()
            decimals = self.currency_decimals_var.get()
            is_base = self.currency_is_base_var.get()

            if not code or not name or not symbol:
                messagebox.showerror("Error", "Please fill in all required fields")
                return

            if self.currency_model.add_currency(code, name, symbol, decimals, is_base):
                messagebox.showinfo("Success", "Currency saved successfully")
                self.load_currencies()
                self.clear_currency_form()
            else:
                messagebox.showerror("Error", "Failed to save currency")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save currency: {str(e)}")

    def clear_currency_form(self):
        """Clear currency form"""
        self.currency_code_var.set("")
        self.currency_name_var.set("")
        self.currency_symbol_var.set("")
        self.currency_decimals_var.set(2)
        self.currency_is_base_var.set(False)
        self.currency_is_active_var.set(True)

    def set_base_currency(self):
        """Set selected currency as base"""
        selection = self.currency_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a currency")
            return

        item = self.currency_tree.item(selection[0])
        currency_code = item['values'][0]

        if self.currency_model.set_base_currency(currency_code):
            messagebox.showinfo("Success", f"{currency_code} set as base currency")
            self.load_currencies()
        else:
            messagebox.showerror("Error", "Failed to set base currency")

    def deactivate_currency(self):
        """Deactivate selected currency"""
        messagebox.showinfo("Info", "Currency deactivation not implemented yet")

    def add_exchange_rate(self):
        """Add exchange rate"""
        try:
            from_curr = self.from_currency_var.get()
            to_curr = self.to_currency_var.get()
            rate = self.rate_var.get()
            rate_date = datetime.strptime(self.rate_date_var.get(), "%Y-%m-%d").date()

            if not from_curr or not to_curr:
                messagebox.showerror("Error", "Please select both currencies")
                return

            if rate <= 0:
                messagebox.showerror("Error", "Rate must be greater than 0")
                return

            if self.currency_model.add_exchange_rate(from_curr, to_curr, rate, rate_date):
                messagebox.showinfo("Success", "Exchange rate added successfully")
                self.load_exchange_rates()
                self.rate_var.set(0.0)
            else:
                messagebox.showerror("Error", "Failed to add exchange rate")

        except ValueError:
            messagebox.showerror("Error", "Invalid date format. Use YYYY-MM-DD")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add exchange rate: {str(e)}")

    def delete_exchange_rate(self):
        """Delete selected exchange rate"""
        messagebox.showinfo("Info", "Exchange rate deletion not implemented yet")

    def update_rates_from_api(self):
        """Update rates from external API"""
        if self.currency_model.update_exchange_rates_from_api():
            messagebox.showinfo("Success", "Exchange rates updated from API")
            self.load_exchange_rates()
        else:
            messagebox.showinfo("Info", "API update not available yet")

    def convert_currency(self):
        """Convert currency amount"""
        try:
            amount = self.conv_amount_var.get()
            from_curr = self.conv_from_var.get()
            to_curr = self.conv_to_var.get()

            if not from_curr or not to_curr:
                messagebox.showerror("Error", "Please select both currencies")
                return

            converted = self.currency_model.convert_amount(amount, from_curr, to_curr)
            formatted_result = self.currency_model.format_currency_amount(converted, to_curr)
            
            self.conv_result_var.set(formatted_result)

        except Exception as e:
            messagebox.showerror("Error", f"Conversion failed: {str(e)}")

    def calculate_unrealized_gains(self):
        """Calculate unrealized gains/losses"""
        try:
            total_gain_loss = self.currency_model.calculate_unrealized_gains_losses()
            messagebox.showinfo("Success", f"Unrealized gains/losses calculated. Total: {total_gain_loss:.2f}")
            self.load_gains_losses()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to calculate gains/losses: {str(e)}")
