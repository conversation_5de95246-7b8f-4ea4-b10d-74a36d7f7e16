import os
import unittest

import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from model.invoice_template import InvoiceTemplate


class TestInvoiceTemplate(unittest.TestCase):
    """Test cases for the InvoiceTemplate model"""

    def setUp(self):
        """Set up test environment"""
        # Create a test database
        self.db_path = "tests/test_invoice_template.db"
        
        # Remove the test database if it exists
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
            
        # Create the template model
        self.template_model = InvoiceTemplate(self.db_path)

    def tearDown(self):
        """Clean up after tests"""
        # Close database connections
        self.template_model = None
        
        # Remove the test database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def test_create_default_template(self):
        """Test creating a default template"""
        # Verify a default template was created during initialization
        templates = self.template_model.get_all_templates()
        
        # There should be at least one template
        self.assertGreaterEqual(len(templates), 1)
        
        # Get the default template
        default_template = self.template_model.get_default_template()
        
        # Verify the default template exists
        self.assertIsNotNone(default_template)
        self.assertEqual(default_template["is_default"], 1)

    def test_add_template(self):
        """Test adding a new template"""
        # Add a new template
        template_data = {
            "name": "Test Template",
            "company_name": "Test Company",
            "company_address": "123 Test St",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.example.com",
            "logo_path": "",
            "primary_color": "#FF0000",
            "secondary_color": "#00FF00",
            "font_family": "Arial",
            "show_logo": 1,
            "show_paid_stamp": 1,
            "notes": "Test notes",
            "terms": "Test terms",
            "is_default": 0
        }
        
        template_id = self.template_model.add_template(template_data)
        
        # Verify the template was added
        self.assertIsNotNone(template_id)
        
        # Get the template
        template = self.template_model.get_template(template_id)
        
        # Verify the template data
        self.assertEqual(template["name"], "Test Template")
        self.assertEqual(template["company_name"], "Test Company")
        self.assertEqual(template["company_address"], "123 Test St")
        self.assertEqual(template["company_phone"], "************")
        self.assertEqual(template["company_email"], "<EMAIL>")
        self.assertEqual(template["company_website"], "www.example.com")
        self.assertEqual(template["primary_color"], "#FF0000")
        self.assertEqual(template["secondary_color"], "#00FF00")
        self.assertEqual(template["font_family"], "Arial")
        self.assertEqual(template["show_logo"], 1)
        self.assertEqual(template["show_paid_stamp"], 1)
        self.assertEqual(template["notes"], "Test notes")
        self.assertEqual(template["terms"], "Test terms")
        self.assertEqual(template["is_default"], 0)

    def test_get_template(self):
        """Test retrieving a template"""
        # Add a template
        template_data = {
            "name": "Test Template",
            "company_name": "Test Company",
            "company_address": "123 Test St",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.example.com",
            "logo_path": "",
            "primary_color": "#FF0000",
            "secondary_color": "#00FF00",
            "font_family": "Arial",
            "show_logo": 1,
            "show_paid_stamp": 1,
            "notes": "Test notes",
            "terms": "Test terms",
            "is_default": 0
        }
        
        template_id = self.template_model.add_template(template_data)
        
        # Get the template
        template = self.template_model.get_template(template_id)
        
        # Verify the template data
        self.assertEqual(template["id"], template_id)
        self.assertEqual(template["name"], "Test Template")
        self.assertEqual(template["company_name"], "Test Company")

    def test_get_all_templates(self):
        """Test retrieving all templates"""
        # Add another template
        template_data = {
            "name": "Another Template",
            "company_name": "Another Company",
            "company_address": "456 Test Ave",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.another.com",
            "logo_path": "",
            "primary_color": "#0000FF",
            "secondary_color": "#FFFF00",
            "font_family": "Times New Roman",
            "show_logo": 0,
            "show_paid_stamp": 0,
            "notes": "More test notes",
            "terms": "More test terms",
            "is_default": 0
        }
        
        self.template_model.add_template(template_data)
        
        # Get all templates
        templates = self.template_model.get_all_templates()
        
        # There should be at least 2 templates (default + new one)
        self.assertGreaterEqual(len(templates), 2)
        
        # Verify the template names
        template_names = [template["name"] for template in templates]
        self.assertIn("Another Template", template_names)

    def test_update_template(self):
        """Test updating a template"""
        # Add a template
        template_data = {
            "name": "Test Template",
            "company_name": "Test Company",
            "company_address": "123 Test St",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.example.com",
            "logo_path": "",
            "primary_color": "#FF0000",
            "secondary_color": "#00FF00",
            "font_family": "Arial",
            "show_logo": 1,
            "show_paid_stamp": 1,
            "notes": "Test notes",
            "terms": "Test terms",
            "is_default": 0
        }
        
        template_id = self.template_model.add_template(template_data)
        
        # Update the template
        updated_data = {
            "name": "Updated Template",
            "company_name": "Updated Company",
            "company_address": "789 Update Rd",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.updated.com",
            "logo_path": "logo.png",
            "primary_color": "#000000",
            "secondary_color": "#FFFFFF",
            "font_family": "Courier",
            "show_logo": 0,
            "show_paid_stamp": 0,
            "notes": "Updated notes",
            "terms": "Updated terms",
            "is_default": 0
        }
        
        result = self.template_model.update_template(template_id, updated_data)
        
        # Verify the update was successful
        self.assertTrue(result)
        
        # Get the updated template
        template = self.template_model.get_template(template_id)
        
        # Verify the template data was updated
        self.assertEqual(template["name"], "Updated Template")
        self.assertEqual(template["company_name"], "Updated Company")
        self.assertEqual(template["company_address"], "789 Update Rd")
        self.assertEqual(template["company_phone"], "************")
        self.assertEqual(template["company_email"], "<EMAIL>")
        self.assertEqual(template["company_website"], "www.updated.com")
        self.assertEqual(template["logo_path"], "logo.png")
        self.assertEqual(template["primary_color"], "#000000")
        self.assertEqual(template["secondary_color"], "#FFFFFF")
        self.assertEqual(template["font_family"], "Courier")
        self.assertEqual(template["show_logo"], 0)
        self.assertEqual(template["show_paid_stamp"], 0)
        self.assertEqual(template["notes"], "Updated notes")
        self.assertEqual(template["terms"], "Updated terms")

    def test_delete_template(self):
        """Test deleting a template"""
        # Add a template
        template_data = {
            "name": "Test Template",
            "company_name": "Test Company",
            "company_address": "123 Test St",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.example.com",
            "logo_path": "",
            "primary_color": "#FF0000",
            "secondary_color": "#00FF00",
            "font_family": "Arial",
            "show_logo": 1,
            "show_paid_stamp": 1,
            "notes": "Test notes",
            "terms": "Test terms",
            "is_default": 0
        }
        
        template_id = self.template_model.add_template(template_data)
        
        # Delete the template
        result = self.template_model.delete_template(template_id)
        
        # Verify the deletion was successful
        self.assertTrue(result)
        
        # Try to get the deleted template
        template = self.template_model.get_template(template_id)
        
        # Verify the template is gone
        self.assertIsNone(template)

    def test_set_default_template(self):
        """Test setting a template as default"""
        # Add a template
        template_data = {
            "name": "Test Template",
            "company_name": "Test Company",
            "company_address": "123 Test St",
            "company_phone": "************",
            "company_email": "<EMAIL>",
            "company_website": "www.example.com",
            "logo_path": "",
            "primary_color": "#FF0000",
            "secondary_color": "#00FF00",
            "font_family": "Arial",
            "show_logo": 1,
            "show_paid_stamp": 1,
            "notes": "Test notes",
            "terms": "Test terms",
            "is_default": 0
        }
        
        template_id = self.template_model.add_template(template_data)
        
        # Get the current default template
        old_default = self.template_model.get_default_template()
        old_default_id = old_default["id"]
        
        # Set the new template as default
        result = self.template_model.set_default_template(template_id)
        
        # Verify the operation was successful
        self.assertTrue(result)
        
        # Get the new default template
        new_default = self.template_model.get_default_template()
        
        # Verify the new template is the default
        self.assertEqual(new_default["id"], template_id)
        
        # Get the old default template
        old_template = self.template_model.get_template(old_default_id)
        
        # Verify the old default is no longer default
        self.assertEqual(old_template["is_default"], 0)

    def test_unset_default_template(self):
        """Test unsetting the default template"""
        # Unset the default template
        result = self.template_model.unset_default_template()
        
        # Verify the operation was successful
        self.assertTrue(result)
        
        # Try to get the default template
        default_template = self.template_model.get_default_template()
        
        # Verify there is no default template
        self.assertIsNone(default_template)


if __name__ == '__main__':
    unittest.main()
