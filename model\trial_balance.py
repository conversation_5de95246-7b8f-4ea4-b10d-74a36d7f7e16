import sqlite3
from datetime import datetime

from model.account import Account
from model.base_model import BaseModel


class TrialBalance(BaseModel):
    """Model for generating trial balance reports"""

    def __init__(self, db_path):
        super().__init__(db_path)

    def table_name(self):
        return "trial_balance_temp"  # This is a virtual table for reporting

    def fields(self):
        return ['account_number', 'account_name', 'classification', 'debit_balance', 'credit_balance']

    def primary_key(self):
        return 'account_number'

    def generate_trial_balance(self, as_of_date=None):
        """Generate trial balance as of a specific date"""
        if as_of_date is None:
            as_of_date = datetime.now().strftime("%Y-%m-%d")

        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get all accounts with their balances
            query = '''
            SELECT
                a.id,
                a.account_number,
                a.name as account_name,
                a.classification,
                a.type as account_type,
                a.current_balance,
                COALESCE(
                    (SELECT SUM(
                        CASE
                            WHEN t.type = 'income' OR t.type = 'initial' THEN t.amount
                            WHEN t.type = 'expense' THEN -t.amount
                            ELSE 0
                        END
                    )
                    FROM transactions t
                    WHERE t.account_id = a.id
                    AND t.date <= ?), 0
                ) as calculated_balance
            FROM accounts a
            WHERE a.is_active = 1
            ORDER BY a.account_number, a.name
            '''

            cursor.execute(query, (as_of_date,))
            accounts = cursor.fetchall()

            trial_balance_data = []
            total_debits = 0.0
            total_credits = 0.0

            for account in accounts:
                account_id, account_number, account_name, classification, account_type, current_balance, calculated_balance = account

                # Use calculated balance based on transactions up to the date
                balance = calculated_balance + (account[4] if account[4] else 0)  # opening_balance would be at index 4 if we selected it

                # Determine if this account normally has a debit or credit balance
                if classification in ['Asset', 'Expense']:
                    # Debit accounts
                    debit_balance = balance if balance > 0 else 0
                    credit_balance = abs(balance) if balance < 0 else 0
                else:
                    # Credit accounts (Liability, Equity, Revenue)
                    credit_balance = balance if balance > 0 else 0
                    debit_balance = abs(balance) if balance < 0 else 0

                trial_balance_data.append({
                    'account_number': account_number or '',
                    'account_name': account_name,
                    'classification': classification or 'Unknown',
                    'account_type': account_type,
                    'debit_balance': debit_balance,
                    'credit_balance': credit_balance,
                    'balance': balance
                })

                total_debits += debit_balance
                total_credits += credit_balance

            # Add totals row
            trial_balance_data.append({
                'account_number': '',
                'account_name': 'TOTALS',
                'classification': '',
                'account_type': '',
                'debit_balance': total_debits,
                'credit_balance': total_credits,
                'balance': total_debits - total_credits
            })

            return {
                'as_of_date': as_of_date,
                'accounts': trial_balance_data,
                'total_debits': total_debits,
                'total_credits': total_credits,
                'difference': total_debits - total_credits,
                'is_balanced': abs(total_debits - total_credits) < 0.01
            }

        except sqlite3.Error as e:
            print(f"Error generating trial balance: {e}")
            raise
        finally:
            conn.close()

    def generate_trial_balance_by_classification(self, as_of_date=None):
        """Generate trial balance grouped by account classification"""
        trial_balance = self.generate_trial_balance(as_of_date)

        # Group by classification
        classifications = {}
        for account in trial_balance['accounts']:
            if account['account_name'] == 'TOTALS':
                continue

            classification = account['classification']
            if classification not in classifications:
                classifications[classification] = {
                    'accounts': [],
                    'total_debits': 0.0,
                    'total_credits': 0.0
                }

            classifications[classification]['accounts'].append(account)
            classifications[classification]['total_debits'] += account['debit_balance']
            classifications[classification]['total_credits'] += account['credit_balance']

        return {
            'as_of_date': trial_balance['as_of_date'],
            'classifications': classifications,
            'total_debits': trial_balance['total_debits'],
            'total_credits': trial_balance['total_credits'],
            'difference': trial_balance['difference'],
            'is_balanced': trial_balance['is_balanced']
        }

    def validate_trial_balance(self, as_of_date=None):
        """Validate that the trial balance is in balance"""
        trial_balance = self.generate_trial_balance(as_of_date)

        validation_result = {
            'is_balanced': trial_balance['is_balanced'],
            'total_debits': trial_balance['total_debits'],
            'total_credits': trial_balance['total_credits'],
            'difference': trial_balance['difference'],
            'as_of_date': trial_balance['as_of_date']
        }

        if not trial_balance['is_balanced']:
            validation_result['errors'] = [
                f"Trial balance is out of balance by ${abs(trial_balance['difference']):.2f}",
                f"Total debits: ${trial_balance['total_debits']:.2f}",
                f"Total credits: ${trial_balance['total_credits']:.2f}"
            ]
        else:
            validation_result['message'] = "Trial balance is in balance"

        return validation_result

    def get_account_activity_summary(self, account_id, start_date=None, end_date=None):
        """Get activity summary for a specific account"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Build date filter
            date_filter = ""
            params = [account_id]

            if start_date and end_date:
                date_filter = "AND t.date BETWEEN ? AND ?"
                params.extend([start_date, end_date])
            elif start_date:
                date_filter = "AND t.date >= ?"
                params.append(start_date)
            elif end_date:
                date_filter = "AND t.date <= ?"
                params.append(end_date)

            query = f'''
            SELECT
                COUNT(*) as transaction_count,
                SUM(CASE WHEN t.type = 'income' OR t.type = 'initial' THEN t.amount ELSE 0 END) as total_debits,
                SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END) as total_credits,
                MIN(t.date) as first_transaction_date,
                MAX(t.date) as last_transaction_date
            FROM transactions t
            WHERE t.account_id = ? {date_filter}
            '''

            cursor.execute(query, params)
            result = cursor.fetchone()

            if result:
                return {
                    'transaction_count': result[0],
                    'total_debits': result[1] or 0.0,
                    'total_credits': result[2] or 0.0,
                    'net_change': (result[1] or 0.0) - (result[2] or 0.0),
                    'first_transaction_date': result[3],
                    'last_transaction_date': result[4]
                }
            else:
                return {
                    'transaction_count': 0,
                    'total_debits': 0.0,
                    'total_credits': 0.0,
                    'net_change': 0.0,
                    'first_transaction_date': None,
                    'last_transaction_date': None
                }

        except sqlite3.Error as e:
            print(f"Error getting account activity: {e}")
            raise
        finally:
            conn.close()

    def generate_aged_trial_balance(self, as_of_date=None, aging_periods=None):
        """Generate an aged trial balance (useful for accounts receivable/payable)"""
        if aging_periods is None:
            aging_periods = [30, 60, 90, 120]  # Default aging periods in days

        trial_balance = self.generate_trial_balance(as_of_date)

        # For now, return the regular trial balance
        # In a full implementation, this would analyze transaction dates
        # and age the balances accordingly
        return trial_balance

    def export_trial_balance_to_csv(self, file_path, as_of_date=None):
        """Export trial balance to CSV file"""
        import csv

        trial_balance = self.generate_trial_balance(as_of_date)

        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Account Number', 'Account Name', 'Classification', 'Debit Balance', 'Credit Balance']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            writer.writerow({
                'Account Number': '',
                'Account Name': f'Trial Balance as of {trial_balance["as_of_date"]}',
                'Classification': '',
                'Debit Balance': '',
                'Credit Balance': ''
            })
            writer.writerow({})  # Empty row

            for account in trial_balance['accounts']:
                writer.writerow({
                    'Account Number': account['account_number'],
                    'Account Name': account['account_name'],
                    'Classification': account['classification'],
                    'Debit Balance': f"${account['debit_balance']:.2f}" if account['debit_balance'] > 0 else '',
                    'Credit Balance': f"${account['credit_balance']:.2f}" if account['credit_balance'] > 0 else ''
                })

        return file_path
