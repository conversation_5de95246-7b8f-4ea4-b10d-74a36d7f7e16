import hashlib
import sqlite3
from datetime import datetime, timedelta
from difflib import <PERSON>quenceMatcher


class DuplicateDetector:
    """
    Advanced duplicate detection for bank statement imports
    Uses multiple algorithms to identify potential duplicates
    """
    
    def __init__(self, db_path):
        self.db_path = db_path
    
    def generate_transaction_hash(self, date, amount, description, account_id):
        """
        Generate a unique hash for a transaction based on key fields
        """
        # Normalize the data
        normalized_date = str(date)
        normalized_amount = f"{float(amount):.2f}"
        normalized_description = str(description).strip().lower()
        normalized_account = str(account_id)
        
        # Create hash string
        hash_string = f"{normalized_date}|{normalized_amount}|{normalized_description}|{normalized_account}"
        
        # Generate SHA-256 hash
        return hashlib.sha256(hash_string.encode('utf-8')).hexdigest()
    
    def check_exact_duplicate(self, date, amount, description, account_id):
        """
        Check for exact duplicates using transaction hash
        """
        transaction_hash = self.generate_transaction_hash(date, amount, description, account_id)
        
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, date, amount, description, import_hash
                FROM transactions 
                WHERE import_hash = ? AND account_id = ?
            ''', (transaction_hash, account_id))
            
            duplicates = cursor.fetchall()
            return len(duplicates) > 0, duplicates
            
        except sqlite3.Error as e:
            raise Exception(f"Error checking for exact duplicates: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def check_fuzzy_duplicates(self, date, amount, description, account_id, 
                              date_tolerance_days=2, amount_tolerance_percent=0.01,
                              description_similarity_threshold=0.8):
        """
        Check for potential duplicates using fuzzy matching
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Convert date to datetime for comparison
            if isinstance(date, str):
                target_date = datetime.strptime(date, '%Y-%m-%d')
            else:
                target_date = date
            
            # Calculate date range
            start_date = (target_date - timedelta(days=date_tolerance_days)).strftime('%Y-%m-%d')
            end_date = (target_date + timedelta(days=date_tolerance_days)).strftime('%Y-%m-%d')
            
            # Calculate amount range
            target_amount = float(amount)
            amount_tolerance = target_amount * amount_tolerance_percent
            min_amount = target_amount - amount_tolerance
            max_amount = target_amount + amount_tolerance
            
            cursor.execute('''
                SELECT id, date, amount, description
                FROM transactions 
                WHERE account_id = ? 
                AND date BETWEEN ? AND ?
                AND amount BETWEEN ? AND ?
            ''', (account_id, start_date, end_date, min_amount, max_amount))
            
            potential_duplicates = []
            target_description = str(description).strip().lower()
            
            for row in cursor.fetchall():
                transaction_id, trans_date, trans_amount, trans_description = row
                
                # Calculate description similarity
                trans_desc_normalized = str(trans_description).strip().lower()
                similarity = SequenceMatcher(None, target_description, trans_desc_normalized).ratio()
                
                if similarity >= description_similarity_threshold:
                    potential_duplicates.append({
                        'id': transaction_id,
                        'date': trans_date,
                        'amount': trans_amount,
                        'description': trans_description,
                        'similarity_score': similarity,
                        'date_diff': abs((datetime.strptime(trans_date, '%Y-%m-%d') - target_date).days),
                        'amount_diff': abs(float(trans_amount) - target_amount)
                    })
            
            return len(potential_duplicates) > 0, potential_duplicates
            
        except sqlite3.Error as e:
            raise Exception(f"Error checking for fuzzy duplicates: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def check_all_duplicates(self, date, amount, description, account_id):
        """
        Check for both exact and fuzzy duplicates
        Returns a comprehensive duplicate report
        """
        duplicate_report = {
            'has_exact_duplicates': False,
            'has_fuzzy_duplicates': False,
            'exact_duplicates': [],
            'fuzzy_duplicates': [],
            'recommendation': 'import'  # 'import', 'skip', 'review'
        }
        
        # Check exact duplicates
        has_exact, exact_dups = self.check_exact_duplicate(date, amount, description, account_id)
        duplicate_report['has_exact_duplicates'] = has_exact
        duplicate_report['exact_duplicates'] = exact_dups
        
        # Check fuzzy duplicates
        has_fuzzy, fuzzy_dups = self.check_fuzzy_duplicates(date, amount, description, account_id)
        duplicate_report['has_fuzzy_duplicates'] = has_fuzzy
        duplicate_report['fuzzy_duplicates'] = fuzzy_dups
        
        # Determine recommendation
        if has_exact:
            duplicate_report['recommendation'] = 'skip'
        elif has_fuzzy and len(fuzzy_dups) > 0:
            # If high similarity score, recommend review
            max_similarity = max([dup['similarity_score'] for dup in fuzzy_dups])
            if max_similarity > 0.9:
                duplicate_report['recommendation'] = 'review'
            else:
                duplicate_report['recommendation'] = 'import'
        
        return duplicate_report
    
    def batch_check_duplicates(self, transactions, account_id):
        """
        Check duplicates for a batch of transactions
        Returns a list of duplicate reports for each transaction
        """
        duplicate_reports = []
        
        for transaction in transactions:
            date = transaction.get('date')
            amount = transaction.get('amount')
            description = transaction.get('description', '')
            
            report = self.check_all_duplicates(date, amount, description, account_id)
            report['transaction_data'] = transaction
            duplicate_reports.append(report)
        
        return duplicate_reports
    
    def get_duplicate_statistics(self, duplicate_reports):
        """
        Generate statistics from duplicate reports
        """
        stats = {
            'total_transactions': len(duplicate_reports),
            'exact_duplicates': 0,
            'fuzzy_duplicates': 0,
            'recommended_imports': 0,
            'recommended_skips': 0,
            'recommended_reviews': 0
        }
        
        for report in duplicate_reports:
            if report['has_exact_duplicates']:
                stats['exact_duplicates'] += 1
            if report['has_fuzzy_duplicates']:
                stats['fuzzy_duplicates'] += 1
            
            if report['recommendation'] == 'import':
                stats['recommended_imports'] += 1
            elif report['recommendation'] == 'skip':
                stats['recommended_skips'] += 1
            elif report['recommendation'] == 'review':
                stats['recommended_reviews'] += 1
        
        return stats
    
    def update_transaction_hash(self, transaction_id, date, amount, description, account_id):
        """
        Update the import hash for an existing transaction
        """
        transaction_hash = self.generate_transaction_hash(date, amount, description, account_id)
        
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE transactions 
                SET import_hash = ?
                WHERE id = ?
            ''', (transaction_hash, transaction_id))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            raise Exception(f"Error updating transaction hash: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def bulk_update_transaction_hashes(self, account_id=None):
        """
        Update import hashes for all existing transactions
        Useful for migrating existing data
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get transactions to update
            if account_id:
                cursor.execute('''
                    SELECT id, date, amount, description, account_id
                    FROM transactions 
                    WHERE account_id = ? AND (import_hash IS NULL OR import_hash = '')
                ''', (account_id,))
            else:
                cursor.execute('''
                    SELECT id, date, amount, description, account_id
                    FROM transactions 
                    WHERE import_hash IS NULL OR import_hash = ''
                ''')
            
            transactions = cursor.fetchall()
            updated_count = 0
            
            for transaction in transactions:
                trans_id, date, amount, description, acc_id = transaction
                transaction_hash = self.generate_transaction_hash(date, amount, description, acc_id)
                
                cursor.execute('''
                    UPDATE transactions 
                    SET import_hash = ?
                    WHERE id = ?
                ''', (transaction_hash, trans_id))
                
                updated_count += 1
            
            conn.commit()
            return updated_count
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error bulk updating transaction hashes: {str(e)}")
        finally:
            if conn:
                conn.close()
