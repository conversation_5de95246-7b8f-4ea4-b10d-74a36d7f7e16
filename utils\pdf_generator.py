import os
import tempfile
from datetime import datetime
from io import BytesIO

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.platypus import (SimpleDocTemplate, Paragraph, Spacer, Table,
                               TableStyle, Image)


class InvoicePDFGenerator:
    """Utility class for generating PDF invoices using ReportLab"""
    
    def __init__(self, template, invoice_data, output_path=None):
        """Initialize the PDF generator
        
        Args:
            template (dict): Invoice template data
            invoice_data (dict): Invoice data including items
            output_path (str, optional): Path to save the PDF. If None, a temporary file is created.
        """
        self.template = template
        self.invoice_data = invoice_data
        self.output_path = output_path
        
        # Set default styles
        self.styles = getSampleStyleSheet()
        self.primary_color = self._hex_to_rgb(template.get('primary_color', '#3498db'))
        self.secondary_color = self._hex_to_rgb(template.get('secondary_color', '#2c3e50'))
        self.font_family = template.get('font_family', 'Helvetica')
        
        # Create custom styles
        self._create_custom_styles()
    
    def _hex_to_rgb(self, hex_color):
        """Convert hex color to RGB tuple
        
        Args:
            hex_color (str): Hex color code (e.g., '#3498db')
            
        Returns:
            tuple: RGB tuple (e.g., (52, 152, 219))
        """
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16)/255 for i in (0, 2, 4))
    
    def _create_custom_styles(self):
        """Create custom paragraph styles for the invoice"""
        # Title style
        self.title_style = ParagraphStyle(
            'InvoiceTitle',
            parent=self.styles['Heading1'],
            fontName=f'{self.font_family}-Bold',
            fontSize=16,
            textColor=self.secondary_color,
            spaceAfter=12
        )
        
        # Heading style
        self.heading_style = ParagraphStyle(
            'InvoiceHeading',
            parent=self.styles['Heading2'],
            fontName=f'{self.font_family}-Bold',
            fontSize=14,
            textColor=self.primary_color,
            spaceAfter=6
        )
        
        # Normal text style
        self.normal_style = ParagraphStyle(
            'InvoiceNormal',
            parent=self.styles['Normal'],
            fontName=self.font_family,
            fontSize=10,
            textColor=self.secondary_color
        )
        
        # Small text style
        self.small_style = ParagraphStyle(
            'InvoiceSmall',
            parent=self.styles['Normal'],
            fontName=self.font_family,
            fontSize=8,
            textColor=self.secondary_color
        )
        
        # Bold text style
        self.bold_style = ParagraphStyle(
            'InvoiceBold',
            parent=self.styles['Normal'],
            fontName=f'{self.font_family}-Bold',
            fontSize=10,
            textColor=self.secondary_color
        )
    
    def generate_pdf(self):
        """Generate the PDF invoice
        
        Returns:
            str: Path to the generated PDF file
        """
        # Create a file to write the PDF to
        if self.output_path:
            output_file = self.output_path
        else:
            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            output_file = temp_file.name
            temp_file.close()
        
        # Create the PDF document
        doc = SimpleDocTemplate(
            output_file,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # Create the content for the PDF
        elements = []
        
        # Add header with company info and logo
        elements.extend(self._create_header())
        
        # Add invoice information
        elements.extend(self._create_invoice_info())
        
        # Add client information
        elements.extend(self._create_client_info())
        
        # Add invoice items
        elements.extend(self._create_items_table())
        
        # Add totals
        elements.extend(self._create_totals())
        
        # Add payment information
        if self.template.get('show_payment_info'):
            elements.extend(self._create_payment_info())
        
        # Add footer
        elements.extend(self._create_footer())
        
        # Build the PDF
        doc.build(elements)
        
        return output_file
    
    def _create_header(self):
        """Create the header section with company info and logo
        
        Returns:
            list: List of flowable elements for the header
        """
        elements = []
        
        # Create a table for the header with two columns (company info and logo)
        company_info = []
        
        # Company name
        company_info.append(Paragraph(self.template.get('company_name', ''), self.title_style))
        
        # Company address
        if self.template.get('company_address'):
            company_info.append(Paragraph(self.template.get('company_address', ''), self.normal_style))
        
        # Company contact info
        contact_info = []
        if self.template.get('company_phone'):
            contact_info.append(f"Phone: {self.template.get('company_phone')}")
        if self.template.get('company_email'):
            contact_info.append(f"Email: {self.template.get('company_email')}")
        if self.template.get('company_website'):
            contact_info.append(f"Web: {self.template.get('company_website')}")
        if self.template.get('company_tax_id'):
            contact_info.append(f"Tax ID: {self.template.get('company_tax_id')}")
        
        if contact_info:
            company_info.append(Paragraph("<br/>".join(contact_info), self.normal_style))
        
        # Logo
        logo_data = None
        if self.template.get('logo_path') and os.path.exists(self.template.get('logo_path')):
            try:
                logo_data = Image(self.template.get('logo_path'))
                logo_data.drawHeight = 0.75 * inch
                logo_data.drawWidth = 1.5 * inch
            except Exception:
                logo_data = None
        
        # Create header table
        if logo_data:
            header_data = [[company_info, logo_data]]
            header_table = Table(header_data, colWidths=[4.5*inch, 2*inch])
            header_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('ALIGN', (0, 0), (0, 0), 'LEFT'),
                ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ]))
        else:
            header_data = [[company_info]]
            header_table = Table(header_data, colWidths=[6.5*inch])
        
        elements.append(header_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_invoice_info(self):
        """Create the invoice information section
        
        Returns:
            list: List of flowable elements for the invoice info
        """
        elements = []
        
        # Invoice title
        elements.append(Paragraph("INVOICE", self.title_style))
        elements.append(Spacer(1, 10))
        
        # Invoice details
        invoice_info = []
        invoice_info.append([Paragraph("Invoice Number:", self.bold_style), 
                            Paragraph(self.invoice_data.get('invoice_number', ''), self.normal_style)])
        invoice_info.append([Paragraph("Issue Date:", self.bold_style), 
                            Paragraph(self.invoice_data.get('issue_date', ''), self.normal_style)])
        invoice_info.append([Paragraph("Due Date:", self.bold_style), 
                            Paragraph(self.invoice_data.get('due_date', ''), self.normal_style)])
        invoice_info.append([Paragraph("Status:", self.bold_style), 
                            Paragraph(self.invoice_data.get('status', '').upper(), self.normal_style)])
        
        # Create invoice info table
        invoice_table = Table(invoice_info, colWidths=[1.5*inch, 5*inch])
        invoice_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]))
        
        elements.append(invoice_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_client_info(self):
        """Create the client information section
        
        Returns:
            list: List of flowable elements for the client info
        """
        elements = []
        
        # Client title
        elements.append(Paragraph("BILL TO", self.heading_style))
        elements.append(Spacer(1, 5))
        
        # Client details
        client_info = []
        
        # Client name and company
        client_name = self.invoice_data.get('client_name', '')
        client_company = self.invoice_data.get('client_company', '')
        if client_company:
            client_info.append(Paragraph(f"{client_name}<br/>{client_company}", self.normal_style))
        else:
            client_info.append(Paragraph(client_name, self.normal_style))
        
        # Client address if available
        client = self.invoice_data.get('client', {})
        if client.get('address'):
            address_parts = []
            if client.get('address'):
                address_parts.append(client.get('address'))
            if client.get('city') or client.get('state') or client.get('zip_code'):
                city_state_zip = []
                if client.get('city'):
                    city_state_zip.append(client.get('city'))
                if client.get('state'):
                    city_state_zip.append(client.get('state'))
                if client.get('zip_code'):
                    city_state_zip.append(client.get('zip_code'))
                address_parts.append(", ".join(city_state_zip))
            if client.get('country'):
                address_parts.append(client.get('country'))
            
            client_info.append(Paragraph("<br/>".join(address_parts), self.normal_style))
        
        # Client contact info
        contact_info = []
        if client.get('email'):
            contact_info.append(f"Email: {client.get('email')}")
        if client.get('phone'):
            contact_info.append(f"Phone: {client.get('phone')}")
        if client.get('tax_id'):
            contact_info.append(f"Tax ID: {client.get('tax_id')}")
        
        if contact_info:
            client_info.append(Paragraph("<br/>".join(contact_info), self.normal_style))
        
        # Add client info to elements
        for info in client_info:
            elements.append(info)
            elements.append(Spacer(1, 3))
        
        elements.append(Spacer(1, 10))
        
        return elements
    
    def _create_items_table(self):
        """Create the invoice items table
        
        Returns:
            list: List of flowable elements for the items table
        """
        elements = []
        
        # Table header
        table_header = [
            Paragraph("Description", self.bold_style),
            Paragraph("Quantity", self.bold_style),
            Paragraph("Unit Price", self.bold_style),
            Paragraph("Tax Rate", self.bold_style),
            Paragraph("Amount", self.bold_style)
        ]
        
        # Table data
        table_data = [table_header]
        
        # Add items
        for item in self.invoice_data.get('items', []):
            row = [
                Paragraph(item.get('description', ''), self.normal_style),
                Paragraph(f"{item.get('quantity', 0)}", self.normal_style),
                Paragraph(f"${item.get('unit_price', 0):.2f}", self.normal_style),
                Paragraph(f"{item.get('tax_rate', 0)}%", self.normal_style),
                Paragraph(f"${item.get('amount', 0):.2f}", self.normal_style)
            ]
            table_data.append(row)
        
        # Create table
        items_table = Table(table_data, colWidths=[3*inch, 0.75*inch, 1*inch, 0.75*inch, 1*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.primary_color),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), f'{self.font_family}-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
            ('TOPPADDING', (0, 0), (-1, 0), 6),
            ('GRID', (0, 0), (-1, -1), 0.5, self.secondary_color),
        ]))
        
        elements.append(items_table)
        elements.append(Spacer(1, 10))
        
        return elements
    
    def _create_totals(self):
        """Create the invoice totals section
        
        Returns:
            list: List of flowable elements for the totals
        """
        elements = []
        
        # Totals data
        totals_data = [
            ['', Paragraph("Subtotal:", self.bold_style), 
             Paragraph(f"${self.invoice_data.get('subtotal', 0):.2f}", self.normal_style)],
            ['', Paragraph("Tax:", self.bold_style), 
             Paragraph(f"${self.invoice_data.get('tax_amount', 0):.2f}", self.normal_style)],
            ['', Paragraph("Total:", self.bold_style), 
             Paragraph(f"${self.invoice_data.get('total_amount', 0):.2f}", self.bold_style)]
        ]
        
        # Create totals table
        totals_table = Table(totals_data, colWidths=[4.5*inch, 1*inch, 1*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('ALIGN', (2, 0), (2, -1), 'RIGHT'),
            ('LINEABOVE', (1, -1), (2, -1), 1, self.primary_color),
            ('LINEBELOW', (1, -1), (2, -1), 1, self.primary_color),
        ]))
        
        elements.append(totals_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_payment_info(self):
        """Create the payment information section
        
        Returns:
            list: List of flowable elements for the payment info
        """
        elements = []
        
        # Payment info title
        elements.append(Paragraph("PAYMENT INFORMATION", self.heading_style))
        elements.append(Spacer(1, 5))
        
        # Payment info text
        payment_info = self.template.get('payment_info', '')
        if payment_info:
            elements.append(Paragraph(payment_info, self.normal_style))
            elements.append(Spacer(1, 10))
        
        # Notes
        notes = self.invoice_data.get('notes', '')
        if notes:
            elements.append(Paragraph("Notes:", self.bold_style))
            elements.append(Spacer(1, 3))
            elements.append(Paragraph(notes, self.normal_style))
            elements.append(Spacer(1, 10))
        
        # Terms
        terms = self.invoice_data.get('terms', '')
        if terms:
            elements.append(Paragraph("Terms and Conditions:", self.bold_style))
            elements.append(Spacer(1, 3))
            elements.append(Paragraph(terms, self.normal_style))
            elements.append(Spacer(1, 10))
        
        return elements
    
    def _create_footer(self):
        """Create the footer section
        
        Returns:
            list: List of flowable elements for the footer
        """
        elements = []
        
        # Footer text
        footer_text = self.template.get('footer_text', '')
        if footer_text:
            elements.append(Paragraph(footer_text, self.small_style))
        
        # Generated date
        generated_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        elements.append(Paragraph(f"Generated on: {generated_date}", self.small_style))
        
        return elements
