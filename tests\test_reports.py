import os
import unittest
import sqlite3
import tkinter as tk
import datetime

from model.report_framework import ReportManager
from model.transaction_reports import TransactionRegisterReport, TransactionSummaryReport, CustomTransactionReport


class TestReports(unittest.TestCase):
    def setUp(self):
        # Create a test database
        self.db_path = "test_reports.db"
        
        # Create database schema
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create accounts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL
            )
        """)
        
        # Create categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        # Create transactions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Insert test data
        
        # Add accounts
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?)",
            ("Checking", "checking", "USD", 1000.00, 1500.00)
        )
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?)",
            ("Savings", "savings", "USD", 5000.00, 5500.00)
        )
        
        # Add categories
        cursor.execute(
            "INSERT INTO categories (name, type) VALUES (?, ?)",
            ("Salary", "income")
        )
        cursor.execute(
            "INSERT INTO categories (name, type) VALUES (?, ?)",
            ("Groceries", "expense")
        )
        cursor.execute(
            "INSERT INTO categories (name, type) VALUES (?, ?)",
            ("Rent", "expense")
        )
        cursor.execute(
            "INSERT INTO categories (name, type) VALUES (?, ?)",
            ("Interest", "income")
        )
        
        # Add transactions
        transactions = [
            # Checking account transactions
            (datetime.date.today().strftime("%Y-%m-%d"), 1000.00, 1, "Salary payment", 1, "income", 0),
            (datetime.date.today().strftime("%Y-%m-%d"), 200.00, 1, "Grocery shopping", 2, "expense", 0),
            (datetime.date.today().strftime("%Y-%m-%d"), 800.00, 1, "Rent payment", 3, "expense", 0),
            ((datetime.date.today() - datetime.timedelta(days=7)).strftime("%Y-%m-%d"), 500.00, 1, "Bonus", 1, "income", 0),
            
            # Savings account transactions
            (datetime.date.today().strftime("%Y-%m-%d"), 50.00, 2, "Interest", 4, "income", 0),
            ((datetime.date.today() - datetime.timedelta(days=30)).strftime("%Y-%m-%d"), 450.00, 2, "Deposit", None, "income", 0),
        ]
        
        for transaction in transactions:
            cursor.execute(
                """INSERT INTO transactions 
                   (date, amount, account_id, description, category_id, type, reconciled) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                transaction
            )
        
        conn.commit()
        conn.close()
        
        # Create report manager
        self.report_manager = ReportManager(self.db_path)
        self.report_manager.register_report(TransactionRegisterReport, "transaction_register")
        self.report_manager.register_report(TransactionSummaryReport, "transaction_summary")
        self.report_manager.register_report(CustomTransactionReport, "custom_transaction")
    
    def tearDown(self):
        # Remove test database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        # Remove cache directory if it exists
        cache_dir = os.path.join(os.path.dirname(self.db_path), "reports", "cache")
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                os.remove(os.path.join(cache_dir, file))
            os.rmdir(cache_dir)
    
    def test_transaction_register_report(self):
        """Test transaction register report"""
        # Create report
        report = self.report_manager.get_report("transaction_register")
        
        # Set parameters
        today = datetime.date.today()
        report.set_parameter("date_from", (today - datetime.timedelta(days=30)).strftime("%Y-%m-%d"))
        report.set_parameter("date_to", today.strftime("%Y-%m-%d"))
        
        # Generate report
        results = report.generate()
        
        # Verify results
        self.assertIsNotNone(results)
        self.assertEqual(len(results), 6)  # All transactions
        
        # Test with account filter
        report.set_parameter("account_id", 1)  # Checking account
        results = report.generate()
        
        self.assertEqual(len(results), 4)  # Only checking account transactions
        
        # Test with category filter
        report.set_parameter("account_id", None)  # Clear account filter
        report.set_parameter("category_id", 1)  # Salary category
        results = report.generate()
        
        self.assertEqual(len(results), 2)  # Only salary transactions
        
        # Test with transaction type filter
        report.set_parameter("category_id", None)  # Clear category filter
        report.set_parameter("transaction_type", "income")  # Income transactions
        results = report.generate()
        
        self.assertEqual(len(results), 4)  # Only income transactions
    
    def test_transaction_summary_report(self):
        """Test transaction summary report"""
        # Create report
        report = self.report_manager.get_report("transaction_summary")
        
        # Set parameters
        today = datetime.date.today()
        report.set_parameter("date_from", (today - datetime.timedelta(days=30)).strftime("%Y-%m-%d"))
        report.set_parameter("date_to", today.strftime("%Y-%m-%d"))
        
        # Generate report
        results = report.generate()
        
        # Verify results
        self.assertIsNotNone(results)
        
        # Check that we have summary rows for each category
        category_types = [(row["category_name"], row["type"]) for row in results]
        self.assertIn(("Salary", "income"), category_types)
        self.assertIn(("Groceries", "expense"), category_types)
        self.assertIn(("Rent", "expense"), category_types)
        self.assertIn(("Interest", "income"), category_types)
        
        # Test with account filter
        report.set_parameter("account_id", 1)  # Checking account
        results = report.generate()
        
        # Check that we only have checking account categories
        category_types = [(row["category_name"], row["type"]) for row in results]
        self.assertIn(("Salary", "income"), category_types)
        self.assertIn(("Groceries", "expense"), category_types)
        self.assertIn(("Rent", "expense"), category_types)
        self.assertNotIn(("Interest", "income"), category_types)
    
    def test_custom_transaction_report(self):
        """Test custom transaction report"""
        # Create report
        report = self.report_manager.get_report("custom_transaction")
        
        # Set parameters
        today = datetime.date.today()
        report.set_parameter("date_from", (today - datetime.timedelta(days=30)).strftime("%Y-%m-%d"))
        report.set_parameter("date_to", today.strftime("%Y-%m-%d"))
        
        # Test with grouping by category
        report.set_parameter("group_by", ["category"])
        results = report.generate()
        
        # Verify results
        self.assertIsNotNone(results)
        
        # Check that we have summary rows for each category
        categories = [row["category_name"] for row in results if "category_name" in row]
        self.assertIn("Salary", categories)
        self.assertIn("Groceries", categories)
        self.assertIn("Rent", categories)
        self.assertIn("Interest", categories)
        
        # Test with grouping by type
        report.set_parameter("group_by", ["type"])
        results = report.generate()
        
        # Check that we have summary rows for each type
        types = [row["type"] for row in results if "type" in row]
        self.assertIn("income", types)
        self.assertIn("expense", types)
        
        # Test with no grouping (detailed report)
        report.set_parameter("group_by", [])
        results = report.generate()
        
        # Check that we have all transactions
        self.assertEqual(len(results), 6)
    
    def test_report_caching(self):
        """Test report caching"""
        # Create report
        report = self.report_manager.get_report("transaction_register")
        
        # Set parameters
        today = datetime.date.today()
        report.set_parameter("date_from", (today - datetime.timedelta(days=30)).strftime("%Y-%m-%d"))
        report.set_parameter("date_to", today.strftime("%Y-%m-%d"))
        
        # Generate report
        results1 = report.generate()
        
        # Get cache key
        cache_key = report.get_cache_key()
        
        # Check that cache file exists
        cache_file = os.path.join(os.path.dirname(self.db_path), "reports", "cache", f"{cache_key}.json")
        self.assertTrue(os.path.exists(cache_file))
        
        # Generate report again (should use cache)
        results2 = report.generate()
        
        # Verify results are the same
        self.assertEqual(len(results1), len(results2))
        
        # Modify a parameter to invalidate cache
        report.set_parameter("account_id", 1)
        
        # Generate report again (should not use cache)
        results3 = report.generate()
        
        # Verify results are different
        self.assertNotEqual(len(results1), len(results3))


if __name__ == "__main__":
    unittest.main()
