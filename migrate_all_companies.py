#!/usr/bin/env python3
"""
Comprehensive Company Database Migration Script
Ensures ALL companies have ALL features including:
- Split Transactions (Phase 11.3)
- Electronic Bank Reconciliation (Phase 11.2)
- Bank Statement Import (Phase 11.1)
- Multi-currency Support
- All other features
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_migration import DatabaseMigration


def get_all_company_databases():
    """Get all company database files"""
    excluded_files = ['users.db', 'settings.db']
    db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]
    return db_files


def check_feature_availability(db_path, feature_name, table_names):
    """Check if a specific feature is available in a database"""
    try:
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        missing_tables = []
        for table_name in table_names:
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            
            if not cursor.fetchone():
                missing_tables.append(table_name)
        
        conn.close()
        return len(missing_tables) == 0, missing_tables
        
    except Exception as e:
        print(f"Error checking {feature_name} in {db_path}: {e}")
        return False, []


def verify_all_features(db_path):
    """Verify all features are available in a database"""
    features = {
        'Split Transactions': ['split_transactions', 'split_transaction_lines'],
        'Bank Reconciliation': ['bank_reconciliation_sessions', 'bank_statement_transactions'],
        'Bank Import': ['bank_format_templates', 'bank_import_sessions'],
        'Multi-Currency': [],  # Check for currency columns in transactions
        'Basic Tables': ['accounts', 'categories', 'transactions', 'metadata']
    }
    
    results = {}
    
    for feature_name, table_names in features.items():
        if feature_name == 'Multi-Currency':
            # Special check for multi-currency columns
            try:
                conn = sqlite3.connect(db_path, timeout=30)
                cursor = conn.cursor()
                
                # Check if transactions table has currency columns
                cursor.execute("PRAGMA table_info(transactions)")
                columns = [row[1] for row in cursor.fetchall()]
                
                has_currency = all(col in columns for col in ['currency_code', 'exchange_rate', 'base_amount'])
                results[feature_name] = (has_currency, [] if has_currency else ['currency columns'])
                
                conn.close()
            except Exception as e:
                results[feature_name] = (False, [f"Error: {e}"])
        else:
            available, missing = check_feature_availability(db_path, feature_name, table_names)
            results[feature_name] = (available, missing)
    
    return results


def migrate_single_company(db_path):
    """Migrate a single company database"""
    print(f"\n🔧 Migrating {db_path}...")
    
    # Check current state
    print("   📋 Checking current features...")
    before_results = verify_all_features(db_path)
    
    missing_features = []
    for feature_name, (available, missing) in before_results.items():
        if not available:
            missing_features.append(feature_name)
            if missing:
                print(f"      ❌ {feature_name}: Missing {', '.join(missing)}")
            else:
                print(f"      ❌ {feature_name}: Not available")
        else:
            print(f"      ✅ {feature_name}: Available")
    
    if not missing_features:
        print("   ✅ All features already available!")
        return True
    
    # Perform migration
    print("   🚀 Running migration...")
    try:
        success = DatabaseMigration.migrate_company_database(db_path)
        if not success:
            print("   ❌ Migration failed!")
            return False
        
        # Verify migration
        print("   🔍 Verifying migration...")
        after_results = verify_all_features(db_path)
        
        all_good = True
        for feature_name, (available, missing) in after_results.items():
            if not available:
                print(f"      ❌ {feature_name}: Still missing {', '.join(missing) if missing else 'features'}")
                all_good = False
            else:
                print(f"      ✅ {feature_name}: Now available")
        
        if all_good:
            print("   🎉 Migration completed successfully!")
        else:
            print("   ⚠️  Migration completed with some issues")
        
        return all_good
        
    except Exception as e:
        print(f"   ❌ Migration error: {e}")
        return False


def main():
    """Main migration function"""
    print("=" * 70)
    print("COMPREHENSIVE COMPANY DATABASE MIGRATION")
    print("=" * 70)
    print("This script ensures ALL companies have ALL features:")
    print("• Split Transactions (Phase 11.3)")
    print("• Electronic Bank Reconciliation (Phase 11.2)")
    print("• Bank Statement Import (Phase 11.1)")
    print("• Multi-currency Support")
    print("• All other features")
    print()
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("❌ Error: Please run this script from the Cashbook application directory")
        print("   (The directory containing main.py)")
        return
    
    # Get all company databases
    print("🔍 Scanning for company databases...")
    db_files = get_all_company_databases()
    
    if not db_files:
        print("✅ No company databases found")
        return
    
    print(f"📊 Found {len(db_files)} company database(s):")
    for db_file in db_files:
        company_name = db_file.replace('.db', '').replace('_', ' ').title()
        print(f"   • {company_name} ({db_file})")
    
    print()
    print("🔍 Checking feature availability across all companies...")
    
    # Check all databases
    companies_needing_migration = []
    companies_up_to_date = []
    
    for db_file in db_files:
        company_name = db_file.replace('.db', '').replace('_', ' ').title()
        print(f"\n📋 Checking {company_name}...")
        
        results = verify_all_features(db_file)
        missing_features = []
        
        for feature_name, (available, missing) in results.items():
            if not available:
                missing_features.append(feature_name)
                if missing:
                    print(f"   ❌ {feature_name}: Missing {', '.join(missing)}")
                else:
                    print(f"   ❌ {feature_name}: Not available")
            else:
                print(f"   ✅ {feature_name}: Available")
        
        if missing_features:
            companies_needing_migration.append((db_file, company_name, missing_features))
        else:
            companies_up_to_date.append((db_file, company_name))
    
    print("\n" + "=" * 50)
    print("FEATURE AVAILABILITY SUMMARY")
    print("=" * 50)
    print(f"✅ Companies up-to-date: {len(companies_up_to_date)}")
    print(f"🔧 Companies needing migration: {len(companies_needing_migration)}")
    
    if companies_up_to_date:
        print("\n✅ Up-to-date companies:")
        for db_file, company_name in companies_up_to_date:
            print(f"   • {company_name}")
    
    if companies_needing_migration:
        print("\n🔧 Companies needing migration:")
        for db_file, company_name, missing_features in companies_needing_migration:
            print(f"   • {company_name}: Missing {', '.join(missing_features)}")
    
    if not companies_needing_migration:
        print("\n🎉 All companies are up-to-date! No migration needed.")
        return
    
    print()
    response = input("❓ Do you want to migrate all companies that need updates? (y/n): ").lower().strip()
    
    if response != 'y':
        print("❌ Migration cancelled by user")
        return
    
    print("\n🚀 Starting comprehensive migration...")
    print("=" * 50)
    
    # Migrate companies
    success_count = 0
    total_count = len(companies_needing_migration)
    
    for db_file, company_name, missing_features in companies_needing_migration:
        print(f"\n🏢 Migrating {company_name}...")
        
        if migrate_single_company(db_file):
            success_count += 1
        else:
            print(f"   ❌ Failed to migrate {company_name}")
    
    print("\n" + "=" * 50)
    print("MIGRATION SUMMARY")
    print("=" * 50)
    print(f"✅ Successfully migrated: {success_count}/{total_count} companies")
    
    if success_count == total_count:
        print("🎉 All companies migrated successfully!")
        print("\n✅ ALL COMPANIES NOW HAVE ALL FEATURES:")
        print("   • Split Transactions")
        print("   • Electronic Bank Reconciliation")
        print("   • Bank Statement Import")
        print("   • Multi-currency Support")
        print("   • All other features")
    else:
        failed_count = total_count - success_count
        print(f"⚠️  {failed_count} companies failed to migrate")
        print("   Please check the error messages above and try again")
    
    print("\n💡 NEXT STEPS:")
    print("   1. Start the Cashbook application (python main.py)")
    print("   2. Create a new company to test all features work")
    print("   3. Open existing companies to verify features are available")
    print("   4. Test Split Transactions and Bank Reconciliation features")
    
    print()
    input("Press Enter to exit...")


def quick_test():
    """Quick test to verify migration system"""
    print("🧪 QUICK MIGRATION TEST")
    print("=" * 30)
    
    db_files = get_all_company_databases()
    if not db_files:
        print("No company databases found for testing")
        return
    
    # Test first database
    test_db = db_files[0]
    print(f"Testing migration on: {test_db}")
    
    results = verify_all_features(test_db)
    print("\nFeature availability:")
    for feature_name, (available, missing) in results.items():
        status = "✅" if available else "❌"
        print(f"  {status} {feature_name}")
        if missing:
            print(f"      Missing: {', '.join(missing)}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        quick_test()
    else:
        main()
