import sqlite3
from abc import ABC, abstractmethod


class BaseModel(ABC):
    """
    Abstract base class for all models with common CRUD operations
    """
    
    def __init__(self, db_path):
        """
        Initialize the model with a database path
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        
    def _get_connection(self):
        """
        Get a database connection with proper settings
        
        Returns:
            SQLite connection object
        """
        conn = sqlite3.connect(self.db_path, timeout=30)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        
        # Enable foreign keys
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Set busy timeout to 10 seconds
        cursor.execute("PRAGMA busy_timeout = 10000")
        
        return conn
    
    @abstractmethod
    def table_name(self):
        """
        Return the table name for this model
        
        Returns:
            String table name
        """
        pass
    
    @abstractmethod
    def fields(self):
        """
        Return a list of field names for this model
        
        Returns:
            List of field names
        """
        pass
    
    @abstractmethod
    def primary_key(self):
        """
        Return the primary key field name
        
        Returns:
            String primary key field name
        """
        pass
    
    def create(self, data):
        """
        Create a new record in the database
        
        Args:
            data: Dictionary of field values
            
        Returns:
            ID of the newly created record
        """
        # Filter data to only include valid fields
        valid_data = {k: v for k, v in data.items() if k in self.fields()}
        
        # Build the SQL query
        fields = ', '.join(valid_data.keys())
        placeholders = ', '.join(['?' for _ in valid_data])
        values = list(valid_data.values())
        
        sql = f"INSERT INTO {self.table_name()} ({fields}) VALUES ({placeholders})"
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, values)
            conn.commit()
            
            # Get the ID of the newly inserted record
            new_id = cursor.lastrowid
            return new_id
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error creating record: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def read(self, record_id):
        """
        Read a record from the database by ID
        
        Args:
            record_id: ID of the record to read
            
        Returns:
            Dictionary containing the record data or None if not found
        """
        sql = f"SELECT * FROM {self.table_name()} WHERE {self.primary_key()} = ?"
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, (record_id,))
            
            result = cursor.fetchone()
            return dict(result) if result else None
            
        except sqlite3.Error as e:
            raise Exception(f"Error reading record: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def update(self, record_id, data):
        """
        Update a record in the database
        
        Args:
            record_id: ID of the record to update
            data: Dictionary of field values to update
            
        Returns:
            True if successful, False otherwise
        """
        # Filter data to only include valid fields
        valid_data = {k: v for k, v in data.items() if k in self.fields() and k != self.primary_key()}
        
        if not valid_data:
            return False
        
        # Build the SQL query
        set_clause = ', '.join([f"{field} = ?" for field in valid_data.keys()])
        values = list(valid_data.values()) + [record_id]
        
        sql = f"UPDATE {self.table_name()} SET {set_clause} WHERE {self.primary_key()} = ?"
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, values)
            conn.commit()
            
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating record: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def delete(self, record_id):
        """
        Delete a record from the database
        
        Args:
            record_id: ID of the record to delete
            
        Returns:
            True if successful, False otherwise
        """
        sql = f"DELETE FROM {self.table_name()} WHERE {self.primary_key()} = ?"
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, (record_id,))
            conn.commit()
            
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting record: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def find_all(self, conditions=None, order_by=None, limit=None):
        """
        Find all records matching the given conditions
        
        Args:
            conditions: Dictionary of field-value pairs to filter by
            order_by: String field name to order by (prefix with - for descending)
            limit: Maximum number of records to return
            
        Returns:
            List of dictionaries containing the record data
        """
        sql = f"SELECT * FROM {self.table_name()}"
        values = []
        
        # Add WHERE clause if conditions are provided
        if conditions:
            valid_conditions = {k: v for k, v in conditions.items() if k in self.fields()}
            if valid_conditions:
                where_clause = ' AND '.join([f"{field} = ?" for field in valid_conditions.keys()])
                values = list(valid_conditions.values())
                sql += f" WHERE {where_clause}"
        
        # Add ORDER BY clause if provided
        if order_by:
            if order_by.startswith('-'):
                field = order_by[1:]
                direction = "DESC"
            else:
                field = order_by
                direction = "ASC"
                
            if field in self.fields():
                sql += f" ORDER BY {field} {direction}"
        
        # Add LIMIT clause if provided
        if limit and isinstance(limit, int) and limit > 0:
            sql += f" LIMIT {limit}"
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, values)
            
            results = cursor.fetchall()
            return [dict(row) for row in results]
            
        except sqlite3.Error as e:
            raise Exception(f"Error finding records: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def count(self, conditions=None):
        """
        Count records matching the given conditions
        
        Args:
            conditions: Dictionary of field-value pairs to filter by
            
        Returns:
            Integer count of matching records
        """
        sql = f"SELECT COUNT(*) FROM {self.table_name()}"
        values = []
        
        # Add WHERE clause if conditions are provided
        if conditions:
            valid_conditions = {k: v for k, v in conditions.items() if k in self.fields()}
            if valid_conditions:
                where_clause = ' AND '.join([f"{field} = ?" for field in valid_conditions.keys()])
                values = list(valid_conditions.values())
                sql += f" WHERE {where_clause}"
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, values)
            
            result = cursor.fetchone()
            return result[0] if result else 0
            
        except sqlite3.Error as e:
            raise Exception(f"Error counting records: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, sql, params=None):
        """
        Execute a custom SQL query
        
        Args:
            sql: SQL query to execute
            params: Parameters for the query
            
        Returns:
            List of dictionaries containing the query results
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
                
            if sql.strip().upper().startswith(('SELECT', 'PRAGMA')):
                results = cursor.fetchall()
                return [dict(row) for row in results]
            else:
                conn.commit()
                return cursor.rowcount
                
        except sqlite3.Error as e:
            if conn and not sql.strip().upper().startswith(('SELECT', 'PRAGMA')):
                conn.rollback()
            raise Exception(f"Error executing query: {str(e)}")
        finally:
            if conn:
                conn.close()
