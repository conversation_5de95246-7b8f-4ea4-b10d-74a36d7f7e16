import os
import sys
import unittest

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from tests.test_account import TestAccount
from tests.test_account_management import TestAccountManagementFrame
from tests.test_base_model import TestBaseModel
from tests.test_category import TestCategory
# Import Phase 7 test modules
from tests.test_client import TestClient
from tests.test_company import TestCompanyWindow
from tests.test_company_frame import TestCompanyFrame
from tests.test_controller import TestController
from tests.test_date_picker import TestDatePicker
from tests.test_email_service import TestEmailService
from tests.test_import import TestRulesEngine, TestTransactionImport
from tests.test_integration import TestIntegration
from tests.test_invoice import TestInvoice
from tests.test_invoice_template import TestInvoiceTemplate
# Import test modules
from tests.test_model import TestDatabase, TestTransactionManager
from tests.test_reconciliation import (TestReconciliationFrame,
                                       TestTransactionManagerReconciliation)
from tests.test_ui import TestAdminDashboard, TestClientDashboard

if __name__ == '__main__':
    # Create a test suite
    test_suite = unittest.TestSuite()

    # Add test cases
    test_classes = [
        TestDatabase,
        TestTransactionManager,
        TestAdminDashboard,
        TestClientDashboard,
        TestCompanyWindow,
        TestCompanyFrame,
        TestIntegration,
        TestController,
        TestBaseModel,
        TestAccount,
        TestCategory,
        TestDatePicker,
        TestAccountManagementFrame,
        TestReconciliationFrame,
        TestTransactionManagerReconciliation,
        TestRulesEngine,
        TestTransactionImport,
        # Phase 7 test classes
        TestClient,
        TestInvoice,
        TestInvoiceTemplate,
        TestEmailService
    ]

    # Load tests from each test class
    for test_class in test_classes:
        test_suite.addTests(unittest.defaultTestLoader.loadTestsFromTestCase(test_class))

    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Exit with non-zero code if tests failed
    sys.exit(not result.wasSuccessful())
