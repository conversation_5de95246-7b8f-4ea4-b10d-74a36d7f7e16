import csv
import datetime
import os
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.bank_format_template import BankFormatTemplate
from model.transaction import TransactionManager
from utils.bank_statement_importer import BankStatementImporter
from utils.duplicate_detector import DuplicateDetector


class ImportFrame(ttk.Frame):
    """Frame for importing transactions from CSV files"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Import Transactions - {company_name}"

        # Create transaction manager
        self.transaction_manager = TransactionManager(self.db_path)

        # Create bank import components
        self.bank_template_manager = BankFormatTemplate(self.db_path)
        self.bank_importer = BankStatementImporter(self.db_path)
        self.duplicate_detector = DuplicateDetector(self.db_path)

        # Import data
        self.file_path = None
        self.csv_data = []
        self.headers = []
        self.preview_data = []
        self.mapping = {}
        self.mapping_profiles = {}
        self.bank_templates = {}
        self.selected_template_id = None

        # Create widgets
        self.create_widgets()

        # Initialize bank templates
        self.load_bank_templates()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with title and close button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(header_frame, text="Import Transactions", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")

        close_button = ttk.Button(header_frame, text="Close", command=self.close_callback, bootstyle=DANGER)
        close_button.pack(side="right")

        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="Select File", padding=10)
        file_frame.pack(fill="x", pady=(0, 20))

        # Bank template selection
        template_frame = ttk.Frame(file_frame)
        template_frame.pack(fill="x", pady=5)

        ttk.Label(template_frame, text="Bank Template:").pack(side="left", padx=(0, 10))
        self.template_var = tk.StringVar()
        self.template_combo = ttk.Combobox(template_frame, textvariable=self.template_var,
                                          state="readonly", width=30)
        self.template_combo.pack(side="left", padx=(0, 10))
        self.template_combo.bind('<<ComboboxSelected>>', self.on_template_selected)

        # Load templates button
        load_templates_btn = ttk.Button(template_frame, text="Refresh Templates",
                                       command=self.load_bank_templates)
        load_templates_btn.pack(side="left", padx=5)

        # File path entry
        file_path_frame = ttk.Frame(file_frame)
        file_path_frame.pack(fill="x", pady=5)

        ttk.Label(file_path_frame, text="File Path:").pack(side="left", padx=5)
        self.file_path_var = tk.StringVar()
        file_path_entry = ttk.Entry(file_path_frame, textvariable=self.file_path_var, width=50)
        file_path_entry.pack(side="left", padx=5, fill="x", expand=True)

        browse_button = ttk.Button(file_path_frame, text="Browse...", command=self.browse_file)
        browse_button.pack(side="left", padx=5)

        # File format options
        format_frame = ttk.Frame(file_frame)
        format_frame.pack(fill="x", pady=5)

        ttk.Label(format_frame, text="File Format:").pack(side="left", padx=5)
        self.format_var = tk.StringVar(value="CSV")
        format_combo = ttk.Combobox(format_frame, textvariable=self.format_var, values=["CSV"], width=15, state="readonly")
        format_combo.pack(side="left", padx=5)

        # Delimiter options
        ttk.Label(format_frame, text="Delimiter:").pack(side="left", padx=5)
        self.delimiter_var = tk.StringVar(value=",")
        delimiter_combo = ttk.Combobox(format_frame, textvariable=self.delimiter_var, values=[",", ";", "\\t", "|"], width=5)
        delimiter_combo.pack(side="left", padx=5)

        # Has header row
        self.header_var = tk.BooleanVar(value=True)
        header_check = ttk.Checkbutton(format_frame, text="First row contains headers", variable=self.header_var)
        header_check.pack(side="left", padx=5)

        # Load file button
        load_button = ttk.Button(file_frame, text="Load File", command=self.load_file, bootstyle=SUCCESS)
        load_button.pack(side="left", padx=5, pady=10)

        # Preview frame
        self.preview_frame = ttk.LabelFrame(main_frame, text="File Preview", padding=10)
        self.preview_frame.pack(fill="both", expand=True, pady=(0, 20))

        # Preview treeview
        preview_tree_frame = ttk.Frame(self.preview_frame)
        preview_tree_frame.pack(fill="both", expand=True)

        # Create columns for preview
        self.preview_columns = ["row_num"]
        self.preview_tree = ttk.Treeview(preview_tree_frame, columns=self.preview_columns, show="headings", height=10)

        # Define headings
        self.preview_tree.heading("row_num", text="#")

        # Define columns
        self.preview_tree.column("row_num", width=50, stretch=False)

        # Add scrollbars
        scrollbar_y = ttk.Scrollbar(preview_tree_frame, orient="vertical", command=self.preview_tree.yview)
        scrollbar_x = ttk.Scrollbar(preview_tree_frame, orient="horizontal", command=self.preview_tree.xview)
        self.preview_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Pack scrollbars and treeview
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        self.preview_tree.pack(fill="both", expand=True)

        # Field mapping frame
        self.mapping_frame = ttk.LabelFrame(main_frame, text="Field Mapping", padding=10)
        self.mapping_frame.pack(fill="x", pady=(0, 20))

        # Mapping profile
        profile_frame = ttk.Frame(self.mapping_frame)
        profile_frame.pack(fill="x", pady=5)

        ttk.Label(profile_frame, text="Mapping Profile:").pack(side="left", padx=5)
        self.profile_var = tk.StringVar()
        self.profile_combo = ttk.Combobox(profile_frame, textvariable=self.profile_var, width=20)
        self.profile_combo.pack(side="left", padx=5)
        self.profile_combo.bind("<<ComboboxSelected>>", self.load_mapping_profile)

        save_profile_button = ttk.Button(profile_frame, text="Save Profile", command=self.save_mapping_profile)
        save_profile_button.pack(side="left", padx=5)

        delete_profile_button = ttk.Button(profile_frame, text="Delete Profile", command=self.delete_mapping_profile)
        delete_profile_button.pack(side="left", padx=5)

        # Mapping fields
        self.mapping_fields_frame = ttk.Frame(self.mapping_frame)
        self.mapping_fields_frame.pack(fill="x", pady=5)

        # Import options frame
        options_frame = ttk.LabelFrame(main_frame, text="Import Options", padding=10)
        options_frame.pack(fill="x", pady=(0, 20))

        # Account selection
        account_frame = ttk.Frame(options_frame)
        account_frame.pack(fill="x", pady=5)

        ttk.Label(account_frame, text="Import to Account:").pack(side="left", padx=5)

        # Get accounts
        accounts = self.transaction_manager.get_accounts()
        account_names = [account["name"] for account in accounts]
        self.account_map = {account["name"]: account["id"] for account in accounts}

        self.account_var = tk.StringVar()
        account_combo = ttk.Combobox(account_frame, textvariable=self.account_var, values=account_names, width=30, state="readonly")
        account_combo.pack(side="left", padx=5)

        # Duplicate handling
        duplicate_frame = ttk.Frame(options_frame)
        duplicate_frame.pack(fill="x", pady=5)

        ttk.Label(duplicate_frame, text="Duplicate Handling:").pack(side="left", padx=5)
        self.duplicate_var = tk.StringVar(value="Skip")
        duplicate_combo = ttk.Combobox(duplicate_frame, textvariable=self.duplicate_var,
                                       values=["Skip", "Replace", "Create New"], width=15, state="readonly")
        duplicate_combo.pack(side="left", padx=5)

        # Import button
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        # Manage Rules button
        rules_button = ttk.Button(button_frame, text="Manage Rules", command=self.open_rules_manager)
        rules_button.pack(side="left", padx=5)

        self.import_button = ttk.Button(button_frame, text="Import Transactions",
                                        command=self.enhanced_import_transactions, bootstyle=SUCCESS, state="disabled")
        self.import_button.pack(side="right", padx=5)

        # Status bar
        self.status_var = tk.StringVar(value="Select a file to import")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

    def browse_file(self):
        """Open file dialog to select a file"""
        # Use appropriate file types based on template selection
        file_types = getattr(self, 'browse_file_types', [("CSV Files", "*.csv"), ("QIF Files", "*.qif"), ("All Files", "*.*")])

        file_path = filedialog.askopenfilename(
            title="Select File to Import",
            filetypes=file_types
        )

        if file_path:
            self.file_path_var.set(file_path)
            self.file_path = file_path

            # Try to detect delimiter for CSV files
            if file_path.lower().endswith('.csv'):
                self.detect_file_format()

    def detect_file_format(self):
        """Detect file format and delimiter"""
        if not self.file_path or not os.path.exists(self.file_path):
            return

        # Read first few lines to detect format
        with open(self.file_path, 'r', newline='', encoding='utf-8-sig') as file:
            sample = file.read(1024)

            # Count delimiters
            comma_count = sample.count(',')
            semicolon_count = sample.count(';')
            tab_count = sample.count('\\t')
            pipe_count = sample.count('|')

            # Set delimiter based on count
            if semicolon_count > comma_count and semicolon_count > tab_count and semicolon_count > pipe_count:
                self.delimiter_var.set(";")
            elif tab_count > comma_count and tab_count > semicolon_count and tab_count > pipe_count:
                self.delimiter_var.set("\\t")
            elif pipe_count > comma_count and pipe_count > semicolon_count and pipe_count > tab_count:
                self.delimiter_var.set("|")
            else:
                self.delimiter_var.set(",")

    def load_file(self):
        """Load the selected file and display preview"""
        if not self.file_path or not os.path.exists(self.file_path):
            messagebox.showerror("Error", "Please select a valid file")
            return

        try:
            # Get delimiter
            delimiter = self.delimiter_var.get()
            if delimiter == "\\t":
                delimiter = '\t'

            # Read CSV file
            with open(self.file_path, 'r', newline='', encoding='utf-8-sig') as file:
                reader = csv.reader(file, delimiter=delimiter)
                self.csv_data = list(reader)

            if not self.csv_data:
                messagebox.showerror("Error", "The file is empty")
                return

            # Get headers
            if self.header_var.get() and len(self.csv_data) > 0:
                self.headers = self.csv_data[0]
                self.preview_data = self.csv_data[1:11]  # First 10 rows for preview
            else:
                # Generate column names
                self.headers = [f"Column {i+1}" for i in range(len(self.csv_data[0]))]
                self.preview_data = self.csv_data[:10]  # First 10 rows for preview

            # Update preview
            self.update_preview()

            # Create mapping fields
            self.create_mapping_fields()

            # Enable import button
            self.import_button.configure(state="normal")

            # Update status
            self.status_var.set(f"Loaded {len(self.csv_data) - (1 if self.header_var.get() else 0)} rows from {os.path.basename(self.file_path)}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {str(e)}")
            self.status_var.set("Error loading file")

    def update_preview(self):
        """Update the preview treeview with loaded data"""
        # Clear existing items
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)

        # Update columns
        self.preview_tree.configure(columns=["row_num"] + [f"col_{i}" for i in range(len(self.headers))])

        # Update headings
        self.preview_tree.heading("row_num", text="#")
        for i, header in enumerate(self.headers):
            self.preview_tree.heading(f"col_{i}", text=header)
            self.preview_tree.column(f"col_{i}", width=100, stretch=True)

        # Add preview data
        for i, row in enumerate(self.preview_data):
            values = [i + 1 + (1 if self.header_var.get() else 0)]
            values.extend(row)

            # Pad row if needed
            while len(values) < len(self.headers) + 1:
                values.append("")

            self.preview_tree.insert("", "end", values=values)

    def create_mapping_fields(self):
        """Create mapping fields for each column"""
        # Clear existing mapping fields
        for widget in self.mapping_fields_frame.winfo_children():
            widget.destroy()

        # Create mapping grid
        mapping_grid = ttk.Frame(self.mapping_fields_frame)
        mapping_grid.pack(fill="x", padx=10, pady=5)

        # Column headers
        ttk.Label(mapping_grid, text="CSV Field", font=("Segoe UI", 10, "bold")).grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Label(mapping_grid, text="Transaction Field", font=("Segoe UI", 10, "bold")).grid(row=0, column=1, sticky="w", padx=5, pady=2)
        ttk.Label(mapping_grid, text="Sample Value", font=("Segoe UI", 10, "bold")).grid(row=0, column=2, sticky="w", padx=5, pady=2)

        # Transaction fields
        transaction_fields = [
            "Date", "Description", "Amount", "Type (income/expense)",
            "Category", "Notes", "Ignore"
        ]

        # Create mapping for each column
        self.mapping_vars = {}
        for i, header in enumerate(self.headers):
            # CSV field name
            ttk.Label(mapping_grid, text=header).grid(row=i+1, column=0, sticky="w", padx=5, pady=2)

            # Transaction field dropdown
            self.mapping_vars[header] = tk.StringVar(value="Ignore")
            field_combo = ttk.Combobox(mapping_grid, textvariable=self.mapping_vars[header],
                                       values=transaction_fields, width=20, state="readonly")
            field_combo.grid(row=i+1, column=1, sticky="w", padx=5, pady=2)

            # Sample value
            sample_value = self.preview_data[0][i] if self.preview_data and i < len(self.preview_data[0]) else ""
            ttk.Label(mapping_grid, text=sample_value).grid(row=i+1, column=2, sticky="w", padx=5, pady=2)

            # Try to auto-detect field type
            self.auto_detect_field_type(header, sample_value)

        # Load mapping profiles
        self.load_mapping_profiles()

    def auto_detect_field_type(self, header, sample_value):
        """Auto-detect field type based on header name and sample value"""
        header_lower = header.lower()

        # Date detection
        if any(date_term in header_lower for date_term in ["date", "time", "day", "month", "year"]):
            self.mapping_vars[header].set("Date")
            return

        # Amount detection
        if any(amount_term in header_lower for amount_term in ["amount", "sum", "total", "price", "cost", "value"]):
            self.mapping_vars[header].set("Amount")
            return

        # Description detection
        if any(desc_term in header_lower for desc_term in ["desc", "narration", "particular", "detail", "memo", "note"]):
            self.mapping_vars[header].set("Description")
            return

        # Type detection
        if any(type_term in header_lower for type_term in ["type", "transaction type", "tr type", "direction"]):
            self.mapping_vars[header].set("Type (income/expense)")
            return

        # Category detection
        if any(cat_term in header_lower for cat_term in ["category", "cat", "classification", "group"]):
            self.mapping_vars[header].set("Category")
            return

        # Notes detection
        if any(note_term in header_lower for note_term in ["note", "comment", "additional", "extra"]):
            self.mapping_vars[header].set("Notes")
            return

    def load_mapping_profiles(self):
        """Load mapping profiles from the database"""
        try:
            # Connect to database
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS mapping_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    mapping TEXT NOT NULL
                )
            """)

            # Get profiles
            cursor.execute("SELECT name, mapping FROM mapping_profiles")
            profiles = cursor.fetchall()

            # Close connection
            conn.close()

            # Update profiles
            self.mapping_profiles = {}
            profile_names = []

            for name, mapping in profiles:
                self.mapping_profiles[name] = eval(mapping)  # Convert string to dict
                profile_names.append(name)

            # Update combobox
            self.profile_combo.configure(values=profile_names)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load mapping profiles: {str(e)}")

    def save_mapping_profile(self):
        """Save the current mapping as a profile"""
        # Get profile name
        profile_name = self.profile_var.get().strip()
        if not profile_name:
            profile_name = simpledialog.askstring("Profile Name", "Enter a name for this mapping profile:")
            if not profile_name:
                return

        # Create mapping
        mapping = {header: self.mapping_vars[header].get() for header in self.headers}

        try:
            # Connect to database
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS mapping_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    mapping TEXT NOT NULL
                )
            """)

            # Check if profile exists
            cursor.execute("SELECT id FROM mapping_profiles WHERE name = ?", (profile_name,))
            profile = cursor.fetchone()

            if profile:
                # Update existing profile
                cursor.execute("UPDATE mapping_profiles SET mapping = ? WHERE name = ?",
                              (str(mapping), profile_name))
            else:
                # Insert new profile
                cursor.execute("INSERT INTO mapping_profiles (name, mapping) VALUES (?, ?)",
                              (profile_name, str(mapping)))

            # Commit changes
            conn.commit()

            # Close connection
            conn.close()

            # Update profiles
            self.load_mapping_profiles()

            # Set current profile
            self.profile_var.set(profile_name)

            # Show success message
            messagebox.showinfo("Success", f"Mapping profile '{profile_name}' saved successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save mapping profile: {str(e)}")

    def load_mapping_profile(self, event=None):
        """Load the selected mapping profile"""
        profile_name = self.profile_var.get()
        if not profile_name or profile_name not in self.mapping_profiles:
            return

        # Get mapping
        mapping = self.mapping_profiles[profile_name]

        # Apply mapping
        for header, field in mapping.items():
            if header in self.mapping_vars:
                self.mapping_vars[header].set(field)

    def delete_mapping_profile(self):
        """Delete the selected mapping profile"""
        profile_name = self.profile_var.get()
        if not profile_name or profile_name not in self.mapping_profiles:
            messagebox.showerror("Error", "Please select a mapping profile to delete")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm", f"Are you sure you want to delete the mapping profile '{profile_name}'?"):
            return

        try:
            # Connect to database
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Delete profile
            cursor.execute("DELETE FROM mapping_profiles WHERE name = ?", (profile_name,))

            # Commit changes
            conn.commit()

            # Close connection
            conn.close()

            # Update profiles
            self.load_mapping_profiles()

            # Clear current profile
            self.profile_var.set("")

            # Show success message
            messagebox.showinfo("Success", f"Mapping profile '{profile_name}' deleted successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete mapping profile: {str(e)}")

    def open_rules_manager(self):
        """Open the rules management screen"""
        # Get the main application container
        main_app = self.parent

        # Create rules frame
        from view.rules_frame import RulesFrame
        rules_key = f"rules_{self.company_name}"

        if rules_key in main_app.screens:
            # Remove existing rules frame
            main_app.remove_screen(rules_key)

        # Create new rules frame
        rules_frame = RulesFrame(
            main_app,
            self.company_name,
            self.db_path,
            lambda: main_app.show_screen(f"import_{self.company_name}")
        )
        main_app.add_screen(rules_key, rules_frame)

        # Show rules frame
        main_app.show_screen(rules_key)

    def import_transactions(self):
        """Import transactions based on mapping"""
        # Check if account is selected
        account_name = self.account_var.get()
        if not account_name:
            messagebox.showerror("Error", "Please select an account to import transactions to")
            return

        account_id = self.account_map.get(account_name)
        if not account_id:
            messagebox.showerror("Error", "Invalid account selected")
            return

        # Get mapping
        mapping = {header: self.mapping_vars[header].get() for header in self.headers}

        # Check required fields
        required_fields = ["Date", "Amount"]
        for field in required_fields:
            if field not in mapping.values():
                messagebox.showerror("Error", f"Required field '{field}' is not mapped")
                return

        # Get data to import
        data_to_import = self.csv_data[1:] if self.header_var.get() else self.csv_data

        # Start import
        self.status_var.set("Importing transactions...")

        # Create index mapping
        index_mapping = {}
        for i, header in enumerate(self.headers):
            field = mapping[header]
            if field != "Ignore":
                index_mapping[field] = i

        # Import transactions
        imported_count = 0
        skipped_count = 0
        error_count = 0

        try:
            # Begin transaction
            self.transaction_manager.begin_transaction()

            for row in data_to_import:
                try:
                    # Skip empty rows
                    if not any(row):
                        continue

                    # Get transaction data
                    transaction = {
                        "account_id": account_id,
                        "date": None,
                        "amount": 0.0,
                        "description": "",
                        "type": "expense",  # Default type
                        "category_id": None,
                        "notes": ""
                    }

                    # Parse date
                    if "Date" in index_mapping and index_mapping["Date"] < len(row):
                        date_str = row[index_mapping["Date"]]
                        try:
                            # Try different date formats
                            date_formats = [
                                "%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y",
                                "%d-%m-%Y", "%m-%d-%Y", "%d.%m.%Y", "%m.%d.%Y"
                            ]

                            for date_format in date_formats:
                                try:
                                    date = datetime.datetime.strptime(date_str, date_format).date()
                                    transaction["date"] = date.strftime("%Y-%m-%d")
                                    break
                                except ValueError:
                                    continue

                            if not transaction["date"]:
                                raise ValueError(f"Could not parse date: {date_str}")

                        except Exception as e:
                            raise ValueError(f"Invalid date format: {date_str}")

                    # Parse amount
                    if "Amount" in index_mapping and index_mapping["Amount"] < len(row):
                        amount_str = row[index_mapping["Amount"]]
                        try:
                            # Remove currency symbols and commas
                            amount_str = amount_str.replace("$", "").replace("€", "").replace("£", "").replace(",", "")
                            transaction["amount"] = abs(float(amount_str))

                            # Determine type based on amount sign
                            if float(amount_str) < 0:
                                transaction["type"] = "expense"
                            else:
                                transaction["type"] = "income"
                        except Exception as e:
                            raise ValueError(f"Invalid amount format: {amount_str}")

                    # Parse description
                    if "Description" in index_mapping and index_mapping["Description"] < len(row):
                        transaction["description"] = row[index_mapping["Description"]]

                    # Parse type
                    if "Type (income/expense)" in index_mapping and index_mapping["Type (income/expense)"] < len(row):
                        type_str = row[index_mapping["Type (income/expense)"]].lower()
                        if any(income_term in type_str for income_term in ["income", "credit", "deposit", "in", "+"]):
                            transaction["type"] = "income"
                        elif any(expense_term in type_str for expense_term in ["expense", "debit", "withdrawal", "out", "-"]):
                            transaction["type"] = "expense"

                    # Parse category
                    if "Category" in index_mapping and index_mapping["Category"] < len(row):
                        category_name = row[index_mapping["Category"]]
                        if category_name:
                            # Try to find category by name
                            category = self.transaction_manager.get_category_by_name(category_name)
                            if category:
                                transaction["category_id"] = category["id"]

                    # Parse notes
                    if "Notes" in index_mapping and index_mapping["Notes"] < len(row):
                        transaction["notes"] = row[index_mapping["Notes"]]

                    # Check for duplicates
                    duplicate_handling = self.duplicate_var.get()
                    is_duplicate = self.transaction_manager.is_duplicate_transaction(
                        transaction["date"],
                        transaction["amount"],
                        transaction["description"],
                        transaction["account_id"]
                    )

                    if is_duplicate and duplicate_handling == "Skip":
                        skipped_count += 1
                        continue
                    elif is_duplicate and duplicate_handling == "Replace":
                        # Delete existing transaction
                        self.transaction_manager.delete_duplicate_transaction(
                            transaction["date"],
                            transaction["amount"],
                            transaction["description"],
                            transaction["account_id"]
                        )

                    # Add transaction
                    self.transaction_manager.add_transaction(
                        transaction["date"],
                        transaction["amount"],
                        transaction["account_id"],
                        transaction["description"],
                        transaction["category_id"],
                        transaction["type"],
                        notes=transaction.get("notes", "")
                    )

                    imported_count += 1

                except Exception as e:
                    error_count += 1
                    print(f"Error importing row: {str(e)}")

            # Commit transaction
            self.transaction_manager.commit_transaction()

            # Show success message
            messagebox.showinfo("Import Complete",
                               f"Import completed successfully.\n\n"
                               f"Imported: {imported_count}\n"
                               f"Skipped: {skipped_count}\n"
                               f"Errors: {error_count}")

            # Update status
            self.status_var.set(f"Import completed. Imported: {imported_count}, Skipped: {skipped_count}, Errors: {error_count}")

        except Exception as e:
            # Rollback transaction
            self.transaction_manager.rollback_transaction()

            # Show error message
            messagebox.showerror("Error", f"Failed to import transactions: {str(e)}")
            self.status_var.set("Error importing transactions")

    def load_bank_templates(self):
        """Load available bank format templates"""
        try:
            # Initialize default templates if needed
            self.bank_template_manager.initialize_default_templates()

            # Get all templates
            templates = self.bank_template_manager.get_all_templates()

            # Update templates dictionary
            self.bank_templates = {f"{template['bank_name']} - {template['name']}": template['id']
                                 for template in templates}

            # Update combobox
            template_names = list(self.bank_templates.keys())
            template_names.insert(0, "Manual Mapping")  # Add manual option
            self.template_combo.configure(values=template_names)

            if template_names:
                self.template_var.set("Manual Mapping")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load bank templates: {str(e)}")

    def on_template_selected(self, event=None):
        """Handle bank template selection"""
        selected_template = self.template_var.get()

        if selected_template == "Manual Mapping":
            self.selected_template_id = None
            # Enable manual field mapping
            self.enable_manual_mapping()
        elif selected_template in self.bank_templates:
            self.selected_template_id = self.bank_templates[selected_template]
            # Load template and apply mapping
            self.apply_template_mapping()

    def enable_manual_mapping(self):
        """Enable manual field mapping interface"""
        # Show mapping frame
        self.mapping_frame.pack(fill="x", pady=(0, 20))

        # Update file type filter
        self.browse_file_types = [("CSV Files", "*.csv"), ("QIF Files", "*.qif"), ("All Files", "*.*")]

    def apply_template_mapping(self):
        """Apply bank template mapping automatically"""
        if not self.selected_template_id:
            return

        try:
            template = self.bank_template_manager.get_template_by_id(self.selected_template_id)
            if not template:
                return

            # Hide manual mapping frame for template-based import
            self.mapping_frame.pack_forget()

            # Update file format settings based on template
            if template['file_type'] == 'CSV':
                self.format_var.set("CSV")
                if template['delimiter']:
                    self.delimiter_var.set(template['delimiter'])
                self.header_var.set(template['has_header'])
            elif template['file_type'] == 'QIF':
                self.format_var.set("QIF")

            # Update file type filter
            if template['file_type'] == 'QIF':
                self.browse_file_types = [("QIF Files", "*.qif"), ("All Files", "*.*")]
            else:
                self.browse_file_types = [("CSV Files", "*.csv"), ("All Files", "*.*")]

        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply template mapping: {str(e)}")

    def enhanced_import_transactions(self):
        """Enhanced import with bank template and duplicate detection"""
        if not self.file_path or not os.path.exists(self.file_path):
            messagebox.showerror("Error", "Please select a valid file")
            return

        # Get selected account
        account_name = self.account_var.get()
        if not account_name:
            messagebox.showerror("Error", "Please select an account")
            return

        account_id = self.account_map.get(account_name)
        if not account_id:
            messagebox.showerror("Error", "Invalid account selected")
            return

        try:
            # Show progress
            self.status_var.set("Importing transactions...")
            self.import_button.configure(state="disabled")

            # Determine import method
            if self.selected_template_id:
                # Use template-based import
                import_result = self.bank_importer.import_with_duplicate_check(
                    self.file_path,
                    account_id,
                    template_id=self.selected_template_id,
                    auto_categorize=True,
                    skip_duplicates=(self.duplicate_var.get() == "Skip")
                )
            else:
                # Use manual mapping import (existing functionality)
                self.import_transactions()
                return

            # Show detailed import results
            self.show_import_results(import_result)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to import transactions: {str(e)}")
        finally:
            self.import_button.configure(state="normal")

    def show_import_results(self, import_result):
        """Show detailed import results with duplicate information"""
        result_message = f"""Import Results:

Total Transactions: {import_result['total_transactions']}
Successfully Imported: {import_result['imported_count']}
Duplicates Skipped: {import_result['duplicate_count']}
Errors: {import_result['error_count']}
Requiring Review: {import_result['review_count']}"""

        if import_result['duplicates']:
            result_message += f"\n\nDuplicate Details:"
            for i, dup in enumerate(import_result['duplicates'][:5]):  # Show first 5
                trans = dup['transaction']
                result_message += f"\n- {trans['date']}: {trans.get('description', 'N/A')} (${trans['amount']:.2f})"

            if len(import_result['duplicates']) > 5:
                result_message += f"\n... and {len(import_result['duplicates']) - 5} more"

        if import_result['errors']:
            result_message += f"\n\nError Details:"
            for i, error in enumerate(import_result['errors'][:3]):  # Show first 3
                result_message += f"\n- {error['error']}"

            if len(import_result['errors']) > 3:
                result_message += f"\n... and {len(import_result['errors']) - 3} more"

        messagebox.showinfo("Import Complete", result_message)

        # Update status
        self.status_var.set(f"Import completed. Imported: {import_result['imported_count']}, "
                           f"Duplicates: {import_result['duplicate_count']}, "
                           f"Errors: {import_result['error_count']}")
