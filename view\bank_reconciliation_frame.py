import os
import tkinter as tk
from datetime import date, datetime
from tkinter import filedialog, messagebox, ttk

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.account import Account
from model.bank_format_template import BankFormatTemplate
from utils.bank_reconciliation_manager import BankReconciliationManager


class BankReconciliationFrame(ttk.Frame):
    """
    Bank Reconciliation interface for managing bank statement reconciliation
    """
    
    def __init__(self, parent, db_path, user_role="admin"):
        super().__init__(parent)
        self.db_path = db_path
        self.user_role = user_role
        self.parent = parent
        
        # Initialize managers
        self.reconciliation_manager = BankReconciliationManager(db_path)
        self.account_manager = Account(db_path)
        self.template_manager = BankFormatTemplate(db_path)
        
        # Current reconciliation session
        self.current_session_id = None
        self.current_session_data = None
        
        # UI variables
        self.account_var = tk.StringVar()
        self.statement_date_var = tk.StringVar()
        self.statement_balance_var = tk.StringVar()
        self.template_var = tk.StringVar()
        self.file_path_var = tk.StringVar()
        
        # Data storage
        self.accounts = {}
        self.templates = {}
        
        # Create widgets
        self.create_widgets()
        self.load_accounts()
        self.load_templates()

    def create_widgets(self):
        """Create the main UI widgets"""
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(self, text="Bank Reconciliation", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20), sticky="w")
        
        # Main container
        main_container = ttk.Frame(self)
        main_container.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_rowconfigure(1, weight=1)
        
        # Create notebook for different views
        self.notebook = ttk.Notebook(main_container)
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # Start Reconciliation Tab
        self.start_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.start_frame, text="Start Reconciliation")
        self.create_start_reconciliation_widgets()
        
        # Active Reconciliation Tab
        self.active_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.active_frame, text="Active Reconciliation")
        self.create_active_reconciliation_widgets()
        
        # History Tab
        self.history_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.history_frame, text="Reconciliation History")
        self.create_history_widgets()
        
        # Initially disable active reconciliation tab
        self.notebook.tab(1, state="disabled")

    def create_start_reconciliation_widgets(self):
        """Create widgets for starting a new reconciliation"""
        # Configure grid
        self.start_frame.grid_columnconfigure(0, weight=1)
        
        # Account selection
        account_frame = ttk.LabelFrame(self.start_frame, text="Select Account", padding=15)
        account_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        account_frame.grid_columnconfigure(1, weight=1)
        
        ttk.Label(account_frame, text="Account:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.account_combo = ttk.Combobox(account_frame, textvariable=self.account_var, 
                                         state="readonly", width=40)
        self.account_combo.grid(row=0, column=1, sticky="ew")
        
        # Statement details
        statement_frame = ttk.LabelFrame(self.start_frame, text="Bank Statement Details", padding=15)
        statement_frame.grid(row=1, column=0, sticky="ew", pady=(0, 15))
        statement_frame.grid_columnconfigure(1, weight=1)
        
        ttk.Label(statement_frame, text="Statement Date:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.date_entry = ttk.DateEntry(statement_frame, dateformat="%Y-%m-%d")
        self.date_entry.grid(row=0, column=1, sticky="w", pady=(0, 10))
        
        ttk.Label(statement_frame, text="Statement Balance:").grid(row=1, column=0, sticky="w", padx=(0, 10))
        self.balance_entry = ttk.Entry(statement_frame, textvariable=self.statement_balance_var, width=20)
        self.balance_entry.grid(row=1, column=1, sticky="w")
        
        # File upload section
        upload_frame = ttk.LabelFrame(self.start_frame, text="Upload Bank Statement", padding=15)
        upload_frame.grid(row=2, column=0, sticky="ew", pady=(0, 15))
        upload_frame.grid_columnconfigure(1, weight=1)
        
        ttk.Label(upload_frame, text="Template:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.template_combo = ttk.Combobox(upload_frame, textvariable=self.template_var, 
                                          state="readonly", width=40)
        self.template_combo.grid(row=0, column=1, sticky="ew", pady=(0, 10))
        
        ttk.Label(upload_frame, text="File:").grid(row=1, column=0, sticky="w", padx=(0, 10))
        file_frame = ttk.Frame(upload_frame)
        file_frame.grid(row=1, column=1, sticky="ew")
        file_frame.grid_columnconfigure(0, weight=1)
        
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var)
        self.file_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10))
        
        browse_btn = ttk.Button(file_frame, text="Browse", command=self.browse_file)
        browse_btn.grid(row=0, column=1)
        
        # Action buttons
        button_frame = ttk.Frame(self.start_frame)
        button_frame.grid(row=3, column=0, pady=20)
        
        self.start_btn = ttk.Button(button_frame, text="Start Reconciliation", 
                                   command=self.start_reconciliation, bootstyle=SUCCESS)
        self.start_btn.pack(side="left", padx=(0, 10))
        
        self.upload_btn = ttk.Button(button_frame, text="Upload Statement", 
                                    command=self.upload_statement, bootstyle=PRIMARY)
        self.upload_btn.pack(side="left")
        self.upload_btn.configure(state="disabled")

    def create_active_reconciliation_widgets(self):
        """Create widgets for active reconciliation management"""
        # Configure grid
        self.active_frame.grid_columnconfigure(0, weight=1)
        self.active_frame.grid_rowconfigure(2, weight=1)
        
        # Session info
        self.session_info_frame = ttk.LabelFrame(self.active_frame, text="Reconciliation Session", padding=10)
        self.session_info_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        # Summary statistics
        self.summary_frame = ttk.LabelFrame(self.active_frame, text="Summary", padding=10)
        self.summary_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        # Transaction matching area
        matching_frame = ttk.LabelFrame(self.active_frame, text="Transaction Matching", padding=10)
        matching_frame.grid(row=2, column=0, sticky="nsew")
        matching_frame.grid_columnconfigure(0, weight=1)
        matching_frame.grid_rowconfigure(1, weight=1)
        
        # Matching controls
        controls_frame = ttk.Frame(matching_frame)
        controls_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        auto_match_btn = ttk.Button(controls_frame, text="Auto Match", 
                                   command=self.auto_match_transactions, bootstyle=SUCCESS)
        auto_match_btn.pack(side="left", padx=(0, 10))
        
        refresh_btn = ttk.Button(controls_frame, text="Refresh", 
                                command=self.refresh_reconciliation_data, bootstyle=INFO)
        refresh_btn.pack(side="left", padx=(0, 10))
        
        complete_btn = ttk.Button(controls_frame, text="Complete Reconciliation", 
                                 command=self.complete_reconciliation, bootstyle=SUCCESS)
        complete_btn.pack(side="right", padx=(10, 0))
        
        cancel_btn = ttk.Button(controls_frame, text="Cancel", 
                               command=self.cancel_reconciliation, bootstyle=DANGER)
        cancel_btn.pack(side="right")
        
        # Transaction lists
        lists_frame = ttk.Frame(matching_frame)
        lists_frame.grid(row=1, column=0, sticky="nsew")
        lists_frame.grid_columnconfigure(0, weight=1)
        lists_frame.grid_columnconfigure(1, weight=1)
        lists_frame.grid_rowconfigure(0, weight=1)
        
        # Unmatched bank transactions
        bank_frame = ttk.LabelFrame(lists_frame, text="Unmatched Bank Transactions", padding=5)
        bank_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        bank_frame.grid_columnconfigure(0, weight=1)
        bank_frame.grid_rowconfigure(0, weight=1)
        
        self.bank_tree = ttk.Treeview(bank_frame, columns=("date", "description", "amount"), 
                                     show="headings", height=10)
        self.bank_tree.grid(row=0, column=0, sticky="nsew")
        
        # Configure bank tree columns
        self.bank_tree.heading("date", text="Date")
        self.bank_tree.heading("description", text="Description")
        self.bank_tree.heading("amount", text="Amount")
        self.bank_tree.column("date", width=100)
        self.bank_tree.column("description", width=200)
        self.bank_tree.column("amount", width=100)
        
        # Bank tree scrollbar
        bank_scroll = ttk.Scrollbar(bank_frame, orient="vertical", command=self.bank_tree.yview)
        bank_scroll.grid(row=0, column=1, sticky="ns")
        self.bank_tree.configure(yscrollcommand=bank_scroll.set)
        
        # Unreconciled book transactions
        book_frame = ttk.LabelFrame(lists_frame, text="Unreconciled Book Transactions", padding=5)
        book_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        book_frame.grid_columnconfigure(0, weight=1)
        book_frame.grid_rowconfigure(0, weight=1)
        
        self.book_tree = ttk.Treeview(book_frame, columns=("date", "description", "amount"), 
                                     show="headings", height=10)
        self.book_tree.grid(row=0, column=0, sticky="nsew")
        
        # Configure book tree columns
        self.book_tree.heading("date", text="Date")
        self.book_tree.heading("description", text="Description")
        self.book_tree.heading("amount", text="Amount")
        self.book_tree.column("date", width=100)
        self.book_tree.column("description", width=200)
        self.book_tree.column("amount", width=100)
        
        # Book tree scrollbar
        book_scroll = ttk.Scrollbar(book_frame, orient="vertical", command=self.book_tree.yview)
        book_scroll.grid(row=0, column=1, sticky="ns")
        self.book_tree.configure(yscrollcommand=book_scroll.set)
        
        # Bind double-click events for manual matching
        self.bank_tree.bind("<Double-1>", self.on_bank_transaction_double_click)
        self.book_tree.bind("<Double-1>", self.on_book_transaction_double_click)

    def create_history_widgets(self):
        """Create widgets for reconciliation history"""
        # Configure grid
        self.history_frame.grid_columnconfigure(0, weight=1)
        self.history_frame.grid_rowconfigure(1, weight=1)
        
        # Controls
        controls_frame = ttk.Frame(self.history_frame)
        controls_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        refresh_history_btn = ttk.Button(controls_frame, text="Refresh", 
                                        command=self.load_reconciliation_history, bootstyle=INFO)
        refresh_history_btn.pack(side="left")
        
        # History tree
        self.history_tree = ttk.Treeview(self.history_frame, 
                                        columns=("account", "date", "status", "difference", "created"), 
                                        show="headings", height=15)
        self.history_tree.grid(row=1, column=0, sticky="nsew")
        
        # Configure history tree columns
        self.history_tree.heading("account", text="Account")
        self.history_tree.heading("date", text="Statement Date")
        self.history_tree.heading("status", text="Status")
        self.history_tree.heading("difference", text="Difference")
        self.history_tree.heading("created", text="Created Date")
        
        self.history_tree.column("account", width=150)
        self.history_tree.column("date", width=100)
        self.history_tree.column("status", width=100)
        self.history_tree.column("difference", width=100)
        self.history_tree.column("created", width=150)
        
        # History tree scrollbar
        history_scroll = ttk.Scrollbar(self.history_frame, orient="vertical", command=self.history_tree.yview)
        history_scroll.grid(row=1, column=1, sticky="ns")
        self.history_tree.configure(yscrollcommand=history_scroll.set)
        
        # Bind double-click to view details
        self.history_tree.bind("<Double-1>", self.view_reconciliation_details)

    def load_accounts(self):
        """Load available accounts"""
        try:
            accounts = self.account_manager.get_all_accounts()
            self.accounts = {f"{acc['name']} ({acc['type']})": acc['id'] for acc in accounts}
            
            account_names = list(self.accounts.keys())
            self.account_combo.configure(values=account_names)
            
            if account_names:
                self.account_var.set(account_names[0])
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load accounts: {str(e)}")

    def load_templates(self):
        """Load available bank templates"""
        try:
            # Initialize default templates
            self.template_manager.initialize_default_templates()
            
            templates = self.template_manager.get_all_templates()
            self.templates = {f"{template['bank_name']} - {template['name']}": template['id'] 
                            for template in templates}
            
            template_names = list(self.templates.keys())
            template_names.insert(0, "Auto-detect / Generic CSV")
            self.template_combo.configure(values=template_names)
            
            if template_names:
                self.template_var.set("Auto-detect / Generic CSV")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load templates: {str(e)}")

    def browse_file(self):
        """Browse for bank statement file"""
        file_path = filedialog.askopenfilename(
            title="Select Bank Statement File",
            filetypes=[("CSV Files", "*.csv"), ("QIF Files", "*.qif"), ("All Files", "*.*")]
        )
        
        if file_path:
            self.file_path_var.set(file_path)

    def start_reconciliation(self):
        """Start a new reconciliation session"""
        try:
            # Validate inputs
            account_name = self.account_var.get()
            if not account_name:
                messagebox.showerror("Error", "Please select an account")
                return

            account_id = self.accounts.get(account_name)
            if not account_id:
                messagebox.showerror("Error", "Invalid account selected")
                return

            statement_date = self.date_entry.entry.get()
            if not statement_date:
                messagebox.showerror("Error", "Please select a statement date")
                return

            try:
                statement_balance = float(self.statement_balance_var.get() or "0")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid statement balance")
                return

            # Start reconciliation
            session_id = self.reconciliation_manager.start_reconciliation(
                account_id, statement_date, statement_balance, "current_user"
            )

            self.current_session_id = session_id

            # Enable upload button and switch to active tab
            self.upload_btn.configure(state="normal")
            messagebox.showinfo("Success", f"Reconciliation session started successfully.\nSession ID: {session_id}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start reconciliation: {str(e)}")

    def upload_statement(self):
        """Upload bank statement for reconciliation"""
        if not self.current_session_id:
            messagebox.showerror("Error", "Please start a reconciliation session first")
            return

        file_path = self.file_path_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("Error", "Please select a valid bank statement file")
            return

        try:
            # Get template ID
            template_name = self.template_var.get()
            template_id = None
            if template_name != "Auto-detect / Generic CSV":
                template_id = self.templates.get(template_name)

            # Upload statement
            result = self.reconciliation_manager.upload_bank_statement(
                self.current_session_id, file_path, template_id
            )

            messagebox.showinfo("Success",
                              f"Bank statement uploaded successfully!\n"
                              f"Transactions added: {result['transactions_added']}")

            # Switch to active reconciliation tab and load data
            self.notebook.tab(1, state="normal")
            self.notebook.select(1)
            self.load_reconciliation_data()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to upload bank statement: {str(e)}")

    def load_reconciliation_data(self):
        """Load data for active reconciliation"""
        if not self.current_session_id:
            return

        try:
            # Get reconciliation dashboard data
            dashboard = self.reconciliation_manager.get_reconciliation_dashboard(self.current_session_id)
            self.current_session_data = dashboard

            # Update session info
            self.update_session_info(dashboard['session'])

            # Update summary
            self.update_summary(dashboard['summary'])

            # Update transaction lists
            self.update_transaction_lists(dashboard)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load reconciliation data: {str(e)}")

    def update_session_info(self, session):
        """Update session information display"""
        # Clear existing widgets
        for widget in self.session_info_frame.winfo_children():
            widget.destroy()

        # Create info labels
        info_text = f"Account: {session['account_name']} | Statement Date: {session['statement_date']} | "
        info_text += f"Statement Balance: ${session['statement_balance']:.2f} | "
        info_text += f"Book Balance: ${session['book_balance']:.2f} | "
        info_text += f"Difference: ${session['difference']:.2f}"

        info_label = ttk.Label(self.session_info_frame, text=info_text, font=("Arial", 10))
        info_label.pack()

    def update_summary(self, summary):
        """Update summary statistics display"""
        # Clear existing widgets
        for widget in self.summary_frame.winfo_children():
            widget.destroy()

        # Create summary grid
        summary_grid = ttk.Frame(self.summary_frame)
        summary_grid.pack(fill="x")

        # Bank transactions summary
        bank_frame = ttk.LabelFrame(summary_grid, text="Bank Transactions", padding=5)
        bank_frame.grid(row=0, column=0, padx=(0, 10), sticky="ew")

        ttk.Label(bank_frame, text=f"Total: {summary['bank_transactions']['total']}").pack(anchor="w")
        ttk.Label(bank_frame, text=f"Matched: {summary['bank_transactions']['matched']}").pack(anchor="w")
        ttk.Label(bank_frame, text=f"Unmatched: {summary['bank_transactions']['unmatched']}").pack(anchor="w")

        # Book transactions summary
        book_frame = ttk.LabelFrame(summary_grid, text="Book Transactions", padding=5)
        book_frame.grid(row=0, column=1, padx=(0, 10), sticky="ew")

        ttk.Label(book_frame, text=f"Unreconciled: {summary['book_transactions']['total']}").pack(anchor="w")

        # Match statistics
        match_frame = ttk.LabelFrame(summary_grid, text="Matching Statistics", padding=5)
        match_frame.grid(row=0, column=2, sticky="ew")

        ttk.Label(match_frame, text=f"Total Matches: {summary['matches']['total']}").pack(anchor="w")
        ttk.Label(match_frame, text=f"Avg Confidence: {summary['matches']['avg_confidence']:.2f}").pack(anchor="w")

    def update_transaction_lists(self, dashboard):
        """Update transaction lists"""
        # Clear existing items
        for item in self.bank_tree.get_children():
            self.bank_tree.delete(item)

        for item in self.book_tree.get_children():
            self.book_tree.delete(item)

        # Populate unmatched bank transactions
        for trans in dashboard['unmatched_bank_transactions']:
            self.bank_tree.insert("", "end", iid=trans['id'], values=(
                trans['date'],
                trans['description'][:50] + "..." if len(trans['description']) > 50 else trans['description'],
                f"${trans['amount']:.2f}"
            ))

        # Populate unreconciled book transactions
        for trans in dashboard['unreconciled_book_transactions']:
            self.book_tree.insert("", "end", iid=trans['id'], values=(
                trans['date'],
                trans['description'][:50] + "..." if len(trans['description']) > 50 else trans['description'],
                f"${trans['amount']:.2f}"
            ))

    def auto_match_transactions(self):
        """Automatically match transactions"""
        if not self.current_session_id:
            return

        try:
            result = self.reconciliation_manager.auto_match_transactions(self.current_session_id)

            messagebox.showinfo("Auto Match Complete",
                              f"Automatically matched {result['matches_made']} transactions")

            # Refresh data
            self.refresh_reconciliation_data()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to auto-match transactions: {str(e)}")

    def refresh_reconciliation_data(self):
        """Refresh reconciliation data"""
        self.load_reconciliation_data()

    def on_bank_transaction_double_click(self, event):
        """Handle double-click on bank transaction for manual matching"""
        selection = self.bank_tree.selection()
        if not selection:
            return

        bank_transaction_id = selection[0]
        self.show_manual_matching_dialog(bank_transaction_id)

    def on_book_transaction_double_click(self, event):
        """Handle double-click on book transaction"""
        # For now, just show transaction details
        selection = self.book_tree.selection()
        if not selection:
            return

        book_transaction_id = selection[0]
        messagebox.showinfo("Transaction Details", f"Book Transaction ID: {book_transaction_id}")

    def show_manual_matching_dialog(self, bank_transaction_id):
        """Show dialog for manual transaction matching"""
        try:
            # Find potential matches
            potential_matches = self.reconciliation_manager.find_potential_matches(
                self.current_session_id, bank_transaction_id
            )

            if not potential_matches:
                messagebox.showinfo("No Matches", "No potential matches found for this transaction")
                return

            # Create matching dialog
            dialog = tk.Toplevel(self)
            dialog.title("Manual Transaction Matching")
            dialog.geometry("800x600")
            dialog.transient(self)
            dialog.grab_set()

            # Create treeview for potential matches
            tree_frame = ttk.Frame(dialog)
            tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

            match_tree = ttk.Treeview(tree_frame,
                                     columns=("date", "description", "amount", "confidence", "type"),
                                     show="headings")
            match_tree.pack(fill="both", expand=True)

            # Configure columns
            match_tree.heading("date", text="Date")
            match_tree.heading("description", text="Description")
            match_tree.heading("amount", text="Amount")
            match_tree.heading("confidence", text="Confidence")
            match_tree.heading("type", text="Match Type")

            # Populate potential matches
            for match in potential_matches:
                trans = match['transaction']
                match_tree.insert("", "end", iid=trans['id'], values=(
                    trans['date'],
                    trans['description'][:40] + "..." if len(trans['description']) > 40 else trans['description'],
                    f"${trans['amount']:.2f}",
                    f"{match['confidence']:.2f}",
                    match['match_type']
                ))

            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill="x", padx=10, pady=10)

            def create_match():
                selection = match_tree.selection()
                if not selection:
                    messagebox.showerror("Error", "Please select a transaction to match")
                    return

                book_transaction_id = selection[0]
                try:
                    success = self.reconciliation_manager.create_manual_match(
                        self.current_session_id, bank_transaction_id, book_transaction_id
                    )

                    if success:
                        messagebox.showinfo("Success", "Manual match created successfully")
                        dialog.destroy()
                        self.refresh_reconciliation_data()
                    else:
                        messagebox.showerror("Error", "Failed to create match")

                except Exception as e:
                    messagebox.showerror("Error", f"Failed to create match: {str(e)}")

            ttk.Button(button_frame, text="Create Match", command=create_match,
                      bootstyle=SUCCESS).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="Cancel", command=dialog.destroy,
                      bootstyle=SECONDARY).pack(side="left")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show matching dialog: {str(e)}")

    def complete_reconciliation(self):
        """Complete the reconciliation process"""
        if not self.current_session_id:
            return

        try:
            # Confirm completion
            if not messagebox.askyesno("Confirm", "Are you sure you want to complete this reconciliation?"):
                return

            result = self.reconciliation_manager.complete_reconciliation(self.current_session_id)

            messagebox.showinfo("Success", "Reconciliation completed successfully!")

            # Reset UI
            self.current_session_id = None
            self.current_session_data = None
            self.notebook.tab(1, state="disabled")
            self.notebook.select(0)
            self.upload_btn.configure(state="disabled")

            # Refresh history
            self.load_reconciliation_history()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to complete reconciliation: {str(e)}")

    def cancel_reconciliation(self):
        """Cancel the reconciliation process"""
        if not self.current_session_id:
            return

        try:
            # Confirm cancellation
            if not messagebox.askyesno("Confirm", "Are you sure you want to cancel this reconciliation?"):
                return

            success = self.reconciliation_manager.cancel_reconciliation(self.current_session_id)

            if success:
                messagebox.showinfo("Success", "Reconciliation cancelled successfully!")

                # Reset UI
                self.current_session_id = None
                self.current_session_data = None
                self.notebook.tab(1, state="disabled")
                self.notebook.select(0)
                self.upload_btn.configure(state="disabled")
            else:
                messagebox.showerror("Error", "Failed to cancel reconciliation")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to cancel reconciliation: {str(e)}")

    def load_reconciliation_history(self):
        """Load reconciliation history"""
        try:
            # Clear existing items
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # Get all reconciliation sessions
            sessions = self.reconciliation_manager.reconciliation_session.get_all_reconciliation_sessions()

            for session in sessions:
                self.history_tree.insert("", "end", iid=session['id'], values=(
                    session['account_name'],
                    session['statement_date'],
                    session['status'].title(),
                    f"${session['difference']:.2f}",
                    session['created_date']
                ))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load reconciliation history: {str(e)}")

    def view_reconciliation_details(self, event):
        """View details of a completed reconciliation"""
        selection = self.history_tree.selection()
        if not selection:
            return

        session_id = selection[0]

        try:
            # Get reconciliation report
            report = self.reconciliation_manager.get_reconciliation_report(session_id)

            # Create details dialog
            dialog = tk.Toplevel(self)
            dialog.title(f"Reconciliation Details - Session {session_id}")
            dialog.geometry("900x700")
            dialog.transient(self)

            # Create notebook for different sections
            notebook = ttk.Notebook(dialog)
            notebook.pack(fill="both", expand=True, padx=10, pady=10)

            # Summary tab
            summary_frame = ttk.Frame(notebook)
            notebook.add(summary_frame, text="Summary")

            summary_text = tk.Text(summary_frame, wrap="word", height=20)
            summary_text.pack(fill="both", expand=True, padx=10, pady=10)

            # Format summary text
            summary_content = f"""Reconciliation Session Details

Account: {report['session_info']['account_name']}
Statement Date: {report['session_info']['statement_date']}
Statement Balance: ${report['session_info']['statement_balance']:.2f}
Book Balance: ${report['session_info']['book_balance']:.2f}
Difference: ${report['session_info']['difference']:.2f}
Status: {report['session_info']['status'].title()}
Created: {report['session_info']['created_date']}

Summary Statistics:
- Total Bank Transactions: {report['summary']['bank_transactions']['total']}
- Matched Transactions: {report['summary']['bank_transactions']['matched']}
- Unmatched Transactions: {report['summary']['bank_transactions']['unmatched']}
- Total Matches: {report['summary']['matches']['total']}
- Average Confidence: {report['summary']['matches']['avg_confidence']:.2f}

Reconciliation Difference: ${report['reconciliation_difference']:.2f}
Report Generated: {report['generated_date']}
"""

            summary_text.insert("1.0", summary_content)
            summary_text.configure(state="disabled")

            # Close button
            close_btn = ttk.Button(dialog, text="Close", command=dialog.destroy)
            close_btn.pack(pady=10)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to view reconciliation details: {str(e)}")
