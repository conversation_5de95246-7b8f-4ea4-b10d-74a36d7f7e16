import csv
import json
import sqlite3
from datetime import datetime
from utils.duplicate_detector import DuplicateDetector
from model.bank_format_template import BankFormatTemplate
from model.rules_engine import RulesEngine


class BankStatementImporter:
    """
    Enhanced bank statement importer with template support and duplicate detection
    """
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.duplicate_detector = DuplicateDetector(db_path)
        self.template_manager = BankFormatTemplate(db_path)
        self.rules_engine = RulesEngine(db_path)
    
    def parse_csv_with_template(self, file_path, template_id):
        """
        Parse CSV file using a bank format template
        """
        template = self.template_manager.get_template_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")
        
        transactions = []
        field_mapping = template['field_mapping']
        
        try:
            with open(file_path, 'r', encoding='utf-8', newline='') as csvfile:
                # Skip lines if specified in template
                for _ in range(template['skip_lines']):
                    next(csvfile)
                
                reader = csv.reader(csvfile, delimiter=template['delimiter'])
                
                # Skip header if specified
                if template['has_header']:
                    next(reader)
                
                for row_num, row in enumerate(reader, start=1):
                    try:
                        transaction = self._parse_csv_row(row, field_mapping, template)
                        if transaction:
                            transaction['row_number'] = row_num
                            transactions.append(transaction)
                    except Exception as e:
                        print(f"Error parsing row {row_num}: {str(e)}")
                        continue
        
        except Exception as e:
            raise Exception(f"Error reading CSV file: {str(e)}")
        
        return transactions
    
    def _parse_csv_row(self, row, field_mapping, template):
        """
        Parse a single CSV row using field mapping
        """
        if len(row) == 0:
            return None
        
        transaction = {}
        
        try:
            # Parse date
            if 'date' in field_mapping:
                date_col = field_mapping['date']
                if isinstance(date_col, int) and date_col < len(row):
                    date_str = row[date_col].strip()
                    if date_str:
                        if template['date_format']:
                            parsed_date = datetime.strptime(date_str, template['date_format'])
                            transaction['date'] = parsed_date.strftime('%Y-%m-%d')
                        else:
                            # Try common date formats
                            for fmt in ['%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', '%d-%m-%Y']:
                                try:
                                    parsed_date = datetime.strptime(date_str, fmt)
                                    transaction['date'] = parsed_date.strftime('%Y-%m-%d')
                                    break
                                except ValueError:
                                    continue
            
            # Parse description
            if 'description' in field_mapping:
                desc_col = field_mapping['description']
                if isinstance(desc_col, int) and desc_col < len(row):
                    transaction['description'] = row[desc_col].strip()
            
            # Parse amount (handle different formats)
            amount = 0.0
            if 'amount' in field_mapping:
                amount_col = field_mapping['amount']
                if isinstance(amount_col, int) and amount_col < len(row):
                    amount_str = row[amount_col].strip().replace(',', '')
                    if amount_str:
                        try:
                            amount = float(amount_str)
                        except ValueError:
                            amount = 0.0
            elif 'debit' in field_mapping and 'credit' in field_mapping:
                # Handle separate debit/credit columns
                debit_col = field_mapping['debit']
                credit_col = field_mapping['credit']
                
                debit_amount = 0.0
                credit_amount = 0.0
                
                if isinstance(debit_col, int) and debit_col < len(row):
                    debit_str = row[debit_col].strip().replace(',', '')
                    if debit_str:
                        try:
                            debit_amount = float(debit_str)
                        except ValueError:
                            pass
                
                if isinstance(credit_col, int) and credit_col < len(row):
                    credit_str = row[credit_col].strip().replace(',', '')
                    if credit_str:
                        try:
                            credit_amount = float(credit_str)
                        except ValueError:
                            pass
                
                # Net amount (credit - debit for bank perspective)
                amount = credit_amount - debit_amount
            
            transaction['amount'] = amount
            
            # Determine transaction type
            transaction['type'] = 'income' if amount > 0 else 'expense'
            
            # Parse additional fields
            if 'reference' in field_mapping:
                ref_col = field_mapping['reference']
                if isinstance(ref_col, int) and ref_col < len(row):
                    transaction['reference'] = row[ref_col].strip()
            
            if 'balance' in field_mapping:
                balance_col = field_mapping['balance']
                if isinstance(balance_col, int) and balance_col < len(row):
                    balance_str = row[balance_col].strip().replace(',', '')
                    if balance_str:
                        try:
                            transaction['balance'] = float(balance_str)
                        except ValueError:
                            pass
            
            # Only return transaction if we have essential data
            if 'date' in transaction and 'amount' in transaction and transaction['amount'] != 0:
                return transaction
            
        except Exception as e:
            raise Exception(f"Error parsing transaction data: {str(e)}")
        
        return None
    
    def parse_qif_file(self, file_path):
        """
        Enhanced QIF file parser
        """
        transactions = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as qif_file:
                current_transaction = {}
                
                for line in qif_file:
                    line = line.strip()
                    
                    if not line:
                        continue
                    
                    if line.startswith('!Type:'):
                        # QIF file type declaration
                        continue
                    elif line.startswith('D'):
                        # Date
                        date_str = line[1:]
                        try:
                            # Try different QIF date formats
                            for fmt in ['%m/%d/%Y', '%m/%d/%y', '%d/%m/%Y', '%d/%m/%y']:
                                try:
                                    parsed_date = datetime.strptime(date_str, fmt)
                                    current_transaction['date'] = parsed_date.strftime('%Y-%m-%d')
                                    break
                                except ValueError:
                                    continue
                        except:
                            pass
                    elif line.startswith('T'):
                        # Amount
                        amount_str = line[1:].replace(',', '')
                        try:
                            amount = float(amount_str)
                            current_transaction['amount'] = amount
                            current_transaction['type'] = 'income' if amount > 0 else 'expense'
                        except ValueError:
                            pass
                    elif line.startswith('P'):
                        # Payee
                        current_transaction['payee'] = line[1:]
                    elif line.startswith('M'):
                        # Memo/Description
                        current_transaction['description'] = line[1:]
                    elif line.startswith('L'):
                        # Category
                        current_transaction['category'] = line[1:]
                    elif line.startswith('N'):
                        # Number (check number, etc.)
                        current_transaction['reference'] = line[1:]
                    elif line.startswith('^'):
                        # End of transaction
                        if 'date' in current_transaction and 'amount' in current_transaction:
                            # Combine payee and description
                            description_parts = []
                            if 'payee' in current_transaction:
                                description_parts.append(current_transaction['payee'])
                            if 'description' in current_transaction:
                                description_parts.append(current_transaction['description'])
                            
                            current_transaction['description'] = ' - '.join(description_parts)
                            transactions.append(current_transaction.copy())
                        
                        current_transaction = {}
        
        except Exception as e:
            raise Exception(f"Error parsing QIF file: {str(e)}")
        
        return transactions
    
    def import_with_duplicate_check(self, file_path, account_id, template_id=None, 
                                   auto_categorize=True, skip_duplicates=True):
        """
        Import bank statement with comprehensive duplicate checking
        """
        import_result = {
            'total_transactions': 0,
            'imported_count': 0,
            'duplicate_count': 0,
            'error_count': 0,
            'review_count': 0,
            'errors': [],
            'duplicates': [],
            'reviews': []
        }
        
        try:
            # Parse transactions based on file type
            if template_id:
                transactions = self.parse_csv_with_template(file_path, template_id)
            elif file_path.lower().endswith('.qif'):
                transactions = self.parse_qif_file(file_path)
            else:
                raise ValueError("Unsupported file format or no template specified")
            
            import_result['total_transactions'] = len(transactions)
            
            # Check duplicates for all transactions
            duplicate_reports = self.duplicate_detector.batch_check_duplicates(transactions, account_id)
            
            # Process each transaction based on duplicate check results
            for i, report in enumerate(duplicate_reports):
                transaction = report['transaction_data']
                
                try:
                    if report['recommendation'] == 'skip' and skip_duplicates:
                        import_result['duplicate_count'] += 1
                        import_result['duplicates'].append({
                            'transaction': transaction,
                            'reason': 'Exact duplicate found',
                            'existing_transactions': report['exact_duplicates']
                        })
                    elif report['recommendation'] == 'review':
                        import_result['review_count'] += 1
                        import_result['reviews'].append({
                            'transaction': transaction,
                            'potential_duplicates': report['fuzzy_duplicates']
                        })
                        # For now, import review transactions (user can decide later)
                        self._import_single_transaction(transaction, account_id, auto_categorize)
                        import_result['imported_count'] += 1
                    else:
                        # Import the transaction
                        self._import_single_transaction(transaction, account_id, auto_categorize)
                        import_result['imported_count'] += 1
                
                except Exception as e:
                    import_result['error_count'] += 1
                    import_result['errors'].append({
                        'transaction': transaction,
                        'error': str(e)
                    })
            
            # Record import statistics
            self._record_import_statistics(file_path, account_id, template_id, import_result)
            
        except Exception as e:
            raise Exception(f"Error during import: {str(e)}")
        
        return import_result
    
    def _import_single_transaction(self, transaction, account_id, auto_categorize=True):
        """
        Import a single transaction into the database
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Auto-categorize if enabled
            category_id = None
            if auto_categorize:
                category_id = self.rules_engine.apply_rules(transaction.get('description', ''))
            
            # Generate import hash
            import_hash = self.duplicate_detector.generate_transaction_hash(
                transaction['date'], 
                transaction['amount'], 
                transaction.get('description', ''), 
                account_id
            )
            
            # Insert transaction
            cursor.execute('''
                INSERT INTO transactions 
                (date, amount, account_id, description, category_id, type, 
                 import_hash, bank_reference, import_source, import_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                transaction['date'],
                abs(transaction['amount']),  # Store as positive amount
                account_id,
                transaction.get('description', ''),
                category_id,
                transaction['type'],
                import_hash,
                transaction.get('reference', ''),
                'Bank Statement Import',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            conn.commit()
            return cursor.lastrowid
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error importing transaction: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def _record_import_statistics(self, filename, account_id, template_id, import_result):
        """
        Record import statistics in the database
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO bank_statement_imports 
                (filename, account_id, import_date, total_transactions, 
                 successful_imports, duplicates_found, errors_count, template_id, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                filename,
                account_id,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                import_result['total_transactions'],
                import_result['imported_count'],
                import_result['duplicate_count'],
                import_result['error_count'],
                template_id,
                'completed'
            ))
            
            conn.commit()
            
        except sqlite3.Error as e:
            print(f"Error recording import statistics: {str(e)}")
        finally:
            if conn:
                conn.close()
