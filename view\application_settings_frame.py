import tkinter as tk
from tkinter import filedialog, messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.application_settings import ApplicationSettings


class ApplicationSettingsFrame(ttk.Frame):
    """Frame for managing application settings and preferences"""

    def __init__(self, parent, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.close_callback = close_callback
        self.title = "Application Settings"

        # Create model
        self.settings_model = ApplicationSettings("settings.db")

        # Variables for settings
        self.setting_vars = {}

        self.create_widgets()
        self.load_settings()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Create notebook for different setting categories
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, pady=(0, 20))

        # Create tabs for each category
        self.create_appearance_tab()
        self.create_regional_tab()
        self.create_behavior_tab()
        self.create_data_tab()
        self.create_security_tab()
        self.create_backup_tab()

        # Action buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")

        # Save button
        save_button = ttk.Button(
            button_frame,
            text="Save Settings",
            command=self.save_settings,
            bootstyle=PRIMARY
        )
        save_button.pack(side="left", padx=(0, 10))

        # Reset to defaults button
        reset_button = ttk.Button(
            button_frame,
            text="Reset to Defaults",
            command=self.reset_to_defaults,
            bootstyle=WARNING
        )
        reset_button.pack(side="left", padx=(0, 10))

        # Export settings button
        export_button = ttk.Button(
            button_frame,
            text="Export Settings",
            command=self.export_settings,
            bootstyle=INFO
        )
        export_button.pack(side="left", padx=(0, 10))

        # Import settings button
        import_button = ttk.Button(
            button_frame,
            text="Import Settings",
            command=self.import_settings,
            bootstyle=INFO
        )
        import_button.pack(side="left")

        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(button_frame, textvariable=self.status_var, foreground="gray")
        status_label.pack(side="right")

    def create_appearance_tab(self):
        """Create appearance settings tab"""
        appearance_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(appearance_frame, text="Appearance")

        # Theme selection
        theme_frame = ttk.LabelFrame(appearance_frame, text="Theme", padding=10)
        theme_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(theme_frame, text="Application Theme:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['theme'] = tk.StringVar()
        theme_combo = ttk.Combobox(
            theme_frame,
            textvariable=self.setting_vars['theme'],
            values=self.settings_model.get_available_themes(),
            state="readonly",
            width=20
        )
        theme_combo.grid(row=0, column=1, sticky="w")

        # Font settings
        font_frame = ttk.LabelFrame(appearance_frame, text="Font", padding=10)
        font_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(font_frame, text="Font Family:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['font_family'] = tk.StringVar()
        font_combo = ttk.Combobox(
            font_frame,
            textvariable=self.setting_vars['font_family'],
            values=['Segoe UI', 'Arial', 'Helvetica', 'Times New Roman', 'Courier New'],
            width=20
        )
        font_combo.grid(row=0, column=1, sticky="w")

        ttk.Label(font_frame, text="Font Size:").grid(row=1, column=0, sticky="w", padx=(0, 10), pady=(5, 0))
        self.setting_vars['font_size'] = tk.IntVar()
        font_size_spin = ttk.Spinbox(font_frame, from_=8, to=16, textvariable=self.setting_vars['font_size'], width=10)
        font_size_spin.grid(row=1, column=1, sticky="w", pady=(5, 0))

        # Window settings
        window_frame = ttk.LabelFrame(appearance_frame, text="Window", padding=10)
        window_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(window_frame, text="Default Width:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['window_width'] = tk.IntVar()
        width_spin = ttk.Spinbox(window_frame, from_=800, to=2000, textvariable=self.setting_vars['window_width'], width=10)
        width_spin.grid(row=0, column=1, sticky="w")

        ttk.Label(window_frame, text="Default Height:").grid(row=1, column=0, sticky="w", padx=(0, 10), pady=(5, 0))
        self.setting_vars['window_height'] = tk.IntVar()
        height_spin = ttk.Spinbox(window_frame, from_=600, to=1500, textvariable=self.setting_vars['window_height'], width=10)
        height_spin.grid(row=1, column=1, sticky="w", pady=(5, 0))

        self.setting_vars['maximize_on_startup'] = tk.BooleanVar()
        maximize_check = ttk.Checkbutton(
            window_frame,
            text="Maximize window on startup",
            variable=self.setting_vars['maximize_on_startup']
        )
        maximize_check.grid(row=2, column=0, columnspan=2, sticky="w", pady=(10, 0))

    def create_regional_tab(self):
        """Create regional settings tab"""
        regional_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(regional_frame, text="Regional")

        # Date and time formats
        datetime_frame = ttk.LabelFrame(regional_frame, text="Date & Time", padding=10)
        datetime_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(datetime_frame, text="Date Format:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['date_format'] = tk.StringVar()
        date_formats = self.settings_model.get_date_formats()
        date_combo = ttk.Combobox(
            datetime_frame,
            textvariable=self.setting_vars['date_format'],
            values=list(date_formats.values()),
            state="readonly",
            width=25
        )
        date_combo.grid(row=0, column=1, sticky="w")

        # Currency settings
        currency_frame = ttk.LabelFrame(regional_frame, text="Currency", padding=10)
        currency_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(currency_frame, text="Currency Symbol:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['currency_symbol'] = tk.StringVar()
        currency_entry = ttk.Entry(currency_frame, textvariable=self.setting_vars['currency_symbol'], width=10)
        currency_entry.grid(row=0, column=1, sticky="w")

        ttk.Label(currency_frame, text="Decimal Places:").grid(row=1, column=0, sticky="w", padx=(0, 10), pady=(5, 0))
        self.setting_vars['decimal_places'] = tk.IntVar()
        decimal_spin = ttk.Spinbox(currency_frame, from_=0, to=4, textvariable=self.setting_vars['decimal_places'], width=10)
        decimal_spin.grid(row=1, column=1, sticky="w", pady=(5, 0))

        # Language settings
        language_frame = ttk.LabelFrame(regional_frame, text="Language", padding=10)
        language_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(language_frame, text="Language:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['language'] = tk.StringVar()
        languages = self.settings_model.get_available_languages()
        language_combo = ttk.Combobox(
            language_frame,
            textvariable=self.setting_vars['language'],
            values=list(languages.values()),
            state="readonly",
            width=20
        )
        language_combo.grid(row=0, column=1, sticky="w")

    def create_behavior_tab(self):
        """Create behavior settings tab"""
        behavior_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(behavior_frame, text="Behavior")

        # General behavior
        general_frame = ttk.LabelFrame(behavior_frame, text="General", padding=10)
        general_frame.pack(fill="x", pady=(0, 10))

        self.setting_vars['auto_save'] = tk.BooleanVar()
        auto_save_check = ttk.Checkbutton(general_frame, text="Auto-save forms", variable=self.setting_vars['auto_save'])
        auto_save_check.pack(anchor="w", pady=2)

        self.setting_vars['confirm_delete'] = tk.BooleanVar()
        confirm_delete_check = ttk.Checkbutton(general_frame, text="Confirm before deleting", variable=self.setting_vars['confirm_delete'])
        confirm_delete_check.pack(anchor="w", pady=2)

        self.setting_vars['show_tooltips'] = tk.BooleanVar()
        tooltips_check = ttk.Checkbutton(general_frame, text="Show tooltips", variable=self.setting_vars['show_tooltips'])
        tooltips_check.pack(anchor="w", pady=2)

        self.setting_vars['remember_window_position'] = tk.BooleanVar()
        remember_pos_check = ttk.Checkbutton(general_frame, text="Remember window position", variable=self.setting_vars['remember_window_position'])
        remember_pos_check.pack(anchor="w", pady=2)

        # Startup settings
        startup_frame = ttk.LabelFrame(behavior_frame, text="Startup", padding=10)
        startup_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(startup_frame, text="Default Company:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['startup_company'] = tk.StringVar()
        startup_entry = ttk.Entry(startup_frame, textvariable=self.setting_vars['startup_company'], width=30)
        startup_entry.grid(row=0, column=1, sticky="w")

    def create_data_tab(self):
        """Create data settings tab"""
        data_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(data_frame, text="Data")

        # Default values
        defaults_frame = ttk.LabelFrame(data_frame, text="Default Values", padding=10)
        defaults_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(defaults_frame, text="Default Account Type:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['default_account_type'] = tk.StringVar()
        account_types = ['Checking Account', 'Savings Account', 'Credit Card', 'Cash', 'Investment']
        account_combo = ttk.Combobox(defaults_frame, textvariable=self.setting_vars['default_account_type'], values=account_types, width=20)
        account_combo.grid(row=0, column=1, sticky="w")

        ttk.Label(defaults_frame, text="Default Transaction Type:").grid(row=1, column=0, sticky="w", padx=(0, 10), pady=(5, 0))
        self.setting_vars['default_transaction_type'] = tk.StringVar()
        transaction_types = ['Income', 'Expense', 'Transfer']
        transaction_combo = ttk.Combobox(defaults_frame, textvariable=self.setting_vars['default_transaction_type'], values=transaction_types, width=20)
        transaction_combo.grid(row=1, column=1, sticky="w", pady=(5, 0))

        # Features
        features_frame = ttk.LabelFrame(data_frame, text="Features", padding=10)
        features_frame.pack(fill="x", pady=(0, 10))

        self.setting_vars['enable_categories'] = tk.BooleanVar()
        categories_check = ttk.Checkbutton(features_frame, text="Enable transaction categories", variable=self.setting_vars['enable_categories'])
        categories_check.pack(anchor="w", pady=2)

        self.setting_vars['enable_projects'] = tk.BooleanVar()
        projects_check = ttk.Checkbutton(features_frame, text="Enable project tracking", variable=self.setting_vars['enable_projects'])
        projects_check.pack(anchor="w", pady=2)

    def create_security_tab(self):
        """Create security settings tab"""
        security_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(security_frame, text="Security")

        # Session settings
        session_frame = ttk.LabelFrame(security_frame, text="Session", padding=10)
        session_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(session_frame, text="Session Timeout (minutes):").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['session_timeout'] = tk.IntVar()
        timeout_spin = ttk.Spinbox(session_frame, from_=5, to=120, textvariable=self.setting_vars['session_timeout'], width=10)
        timeout_spin.grid(row=0, column=1, sticky="w")

        # Password settings
        password_frame = ttk.LabelFrame(security_frame, text="Password", padding=10)
        password_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(password_frame, text="Minimum Password Length:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.setting_vars['password_min_length'] = tk.IntVar()
        password_spin = ttk.Spinbox(password_frame, from_=4, to=20, textvariable=self.setting_vars['password_min_length'], width=10)
        password_spin.grid(row=0, column=1, sticky="w")

        # Audit settings
        audit_frame = ttk.LabelFrame(security_frame, text="Audit", padding=10)
        audit_frame.pack(fill="x", pady=(0, 10))

        self.setting_vars['enable_audit_log'] = tk.BooleanVar()
        audit_check = ttk.Checkbutton(audit_frame, text="Enable audit logging", variable=self.setting_vars['enable_audit_log'])
        audit_check.pack(anchor="w", pady=2)

    def create_backup_tab(self):
        """Create backup settings tab"""
        backup_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(backup_frame, text="Backup")

        # Backup settings
        backup_settings_frame = ttk.LabelFrame(backup_frame, text="Automatic Backup", padding=10)
        backup_settings_frame.pack(fill="x", pady=(0, 10))

        self.setting_vars['backup_enabled'] = tk.BooleanVar()
        backup_check = ttk.Checkbutton(backup_settings_frame, text="Enable automatic backups", variable=self.setting_vars['backup_enabled'])
        backup_check.pack(anchor="w", pady=2)

        # Frequency setting
        freq_frame = ttk.Frame(backup_settings_frame)
        freq_frame.pack(fill="x", pady=(10, 5))

        ttk.Label(freq_frame, text="Backup Frequency:").pack(side="left")
        self.setting_vars['backup_frequency'] = tk.StringVar()
        frequency_combo = ttk.Combobox(
            freq_frame,
            textvariable=self.setting_vars['backup_frequency'],
            values=['daily', 'weekly', 'monthly'],
            state="readonly",
            width=15
        )
        frequency_combo.pack(side="left", padx=(10, 0))

        # Location setting
        location_frame = ttk.Frame(backup_settings_frame)
        location_frame.pack(fill="x", pady=5)

        ttk.Label(location_frame, text="Backup Location:").pack(side="left")

        self.setting_vars['backup_location'] = tk.StringVar()
        location_entry = ttk.Entry(location_frame, textvariable=self.setting_vars['backup_location'], width=30)
        location_entry.pack(side="left", padx=(10, 0))

        browse_button = ttk.Button(location_frame, text="Browse", command=self.browse_backup_location, bootstyle=SECONDARY)
        browse_button.pack(side="left", padx=(5, 0))

    def browse_backup_location(self):
        """Browse for backup location"""
        folder = filedialog.askdirectory(title="Select Backup Location")
        if folder:
            self.setting_vars['backup_location'].set(folder)

    def load_settings(self):
        """Load current settings into the form"""
        try:
            all_settings = self.settings_model.get_all_settings()

            for category, settings in all_settings.items():
                for key, setting_info in settings.items():
                    if key in self.setting_vars:
                        var = self.setting_vars[key]
                        value = setting_info['value']

                        if isinstance(var, tk.BooleanVar):
                            var.set(bool(value))
                        elif isinstance(var, tk.IntVar):
                            var.set(int(value))
                        else:
                            var.set(str(value))

            self.status_var.set("Settings loaded")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load settings: {str(e)}")

    def save_settings(self):
        """Save current settings"""
        try:
            for key, var in self.setting_vars.items():
                value = var.get()

                # Determine setting type
                if isinstance(var, tk.BooleanVar):
                    setting_type = 'boolean'
                elif isinstance(var, tk.IntVar):
                    setting_type = 'integer'
                else:
                    setting_type = 'string'

                self.settings_model.set_setting(key, value, setting_type)

            messagebox.showinfo("Success", "Settings saved successfully!")
            self.status_var.set("Settings saved")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def reset_to_defaults(self):
        """Reset all settings to default values"""
        if messagebox.askyesno("Confirm Reset", "Are you sure you want to reset all settings to their default values?"):
            try:
                self.settings_model.reset_to_defaults()
                self.load_settings()
                messagebox.showinfo("Success", "Settings reset to defaults!")
                self.status_var.set("Settings reset to defaults")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to reset settings: {str(e)}")

    def export_settings(self):
        """Export settings to a file"""
        file_path = filedialog.asksaveasfilename(
            title="Export Settings",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                if self.settings_model.export_settings(file_path):
                    messagebox.showinfo("Success", f"Settings exported to {file_path}")
                    self.status_var.set("Settings exported")
                else:
                    messagebox.showerror("Error", "Failed to export settings")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export settings: {str(e)}")

    def import_settings(self):
        """Import settings from a file"""
        file_path = filedialog.askopenfilename(
            title="Import Settings",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            if messagebox.askyesno("Confirm Import", "This will overwrite your current settings. Continue?"):
                try:
                    if self.settings_model.import_settings(file_path):
                        self.load_settings()
                        messagebox.showinfo("Success", f"Settings imported from {file_path}")
                        self.status_var.set("Settings imported")
                    else:
                        messagebox.showerror("Error", "Failed to import settings")

                except Exception as e:
                    messagebox.showerror("Error", f"Failed to import settings: {str(e)}")
