#!/usr/bin/env python3
"""
Demo script for Multi-Currency Support (Phase 10.3)
Demonstrates comprehensive multi-currency functionality
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import sys
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.currency import Currency
from view.currency_management_frame import CurrencyManagementFrame
from view.multi_currency_transaction_frame import MultiCurrencyTransactionFrame
from view.multi_currency_reports_frame import MultiCurrencyReportsFrame


class MultiCurrencyDemo:
    def __init__(self):
        # Create main window
        self.root = ttk.Window(themename="cosmo")
        self.root.title("Multi-Currency Support Demo - Phase 10.3")
        self.root.geometry("1200x800")
        
        # Create demo database
        self.db_path = "demo_multicurrency.db"
        self.setup_demo_database()
        
        # Create main interface
        self.create_main_interface()
        
    def setup_demo_database(self):
        """Set up demo database with sample data"""
        try:
            # Initialize currency model (this will create tables and default currencies)
            currency_model = Currency(self.db_path)
            
            # Add some sample exchange rates
            currency_model.add_exchange_rate("EUR", "USD", 1.0850)
            currency_model.add_exchange_rate("GBP", "USD", 1.2650)
            currency_model.add_exchange_rate("JPY", "USD", 0.0067)
            currency_model.add_exchange_rate("CAD", "USD", 0.7420)
            currency_model.add_exchange_rate("AUD", "USD", 0.6580)
            
            print("✅ Demo database setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up demo database: {e}")
    
    def create_main_interface(self):
        """Create the main demo interface"""
        # Main container
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))
        
        title_label = ttk.Label(
            header_frame,
            text="🌍 Multi-Currency Support Demo",
            font=("Segoe UI", 20, "bold")
        )
        title_label.pack(side="left")
        
        # Version info
        version_label = ttk.Label(
            header_frame,
            text="Phase 10.3 - Complete Implementation",
            font=("Segoe UI", 12),
            bootstyle="secondary"
        )
        version_label.pack(side="right")
        
        # Feature overview
        overview_frame = ttk.LabelFrame(main_frame, text="Multi-Currency Features", padding=20)
        overview_frame.pack(fill="x", pady=(0, 20))
        
        features_text = """
🎯 COMPREHENSIVE MULTI-CURRENCY SUPPORT

✅ Core Features Implemented:
• Currency Management - Add, edit, and manage multiple currencies
• Exchange Rate Management - Manual and automatic rate updates
• Multi-Currency Accounts - Accounts in different currencies
• Multi-Currency Transactions - Transactions with automatic conversion
• Currency Conversion - Real-time currency conversion calculator
• Gains/Losses Tracking - Realized and unrealized currency gains/losses
• Multi-Currency Reporting - Comprehensive reports and analysis
• Currency Risk Analysis - Exposure analysis and risk assessment

✅ Advanced Features:
• Base Currency Configuration - Set any currency as base
• Automatic Exchange Rate Updates - API integration ready
• Currency Exposure Analysis - Portfolio risk assessment
• Historical Exchange Rates - Rate history tracking
• Multi-Currency Transfers - Cross-currency account transfers
• Currency Formatting - Proper formatting per currency rules
        """.strip()
        
        features_label = ttk.Label(
            overview_frame,
            text=features_text,
            font=("Segoe UI", 10),
            justify="left"
        )
        features_label.pack(anchor="w")
        
        # Demo buttons
        buttons_frame = ttk.LabelFrame(main_frame, text="Demo Components", padding=20)
        buttons_frame.pack(fill="x", pady=(0, 20))
        
        # Currency Management Demo
        currency_mgmt_button = ttk.Button(
            buttons_frame,
            text="💱 Currency Management",
            command=self.open_currency_management,
            bootstyle=PRIMARY,
            width=25
        )
        currency_mgmt_button.pack(side="left", padx=(0, 10))
        
        # Multi-Currency Transaction Demo
        mc_transaction_button = ttk.Button(
            buttons_frame,
            text="💰 Multi-Currency Transactions",
            command=self.open_mc_transaction,
            bootstyle=INFO,
            width=25
        )
        mc_transaction_button.pack(side="left", padx=(0, 10))
        
        # Multi-Currency Reports Demo
        mc_reports_button = ttk.Button(
            buttons_frame,
            text="📊 Multi-Currency Reports",
            command=self.open_mc_reports,
            bootstyle=SUCCESS,
            width=25
        )
        mc_reports_button.pack(side="left")
        
        # Quick demo section
        quick_demo_frame = ttk.LabelFrame(main_frame, text="Quick Demo", padding=20)
        quick_demo_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # Currency converter
        converter_frame = ttk.Frame(quick_demo_frame)
        converter_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(converter_frame, text="Quick Currency Converter:", font=("Segoe UI", 12, "bold")).pack(anchor="w")
        
        conv_controls_frame = ttk.Frame(converter_frame)
        conv_controls_frame.pack(fill="x", pady=(10, 0))
        
        self.amount_var = tk.DoubleVar(value=100.0)
        self.from_currency_var = tk.StringVar(value="USD")
        self.to_currency_var = tk.StringVar(value="EUR")
        self.result_var = tk.StringVar()
        
        ttk.Label(conv_controls_frame, text="Amount:").pack(side="left", padx=(0, 5))
        ttk.Entry(conv_controls_frame, textvariable=self.amount_var, width=10).pack(side="left", padx=(0, 10))
        
        ttk.Label(conv_controls_frame, text="From:").pack(side="left", padx=(0, 5))
        from_combo = ttk.Combobox(conv_controls_frame, textvariable=self.from_currency_var, 
                                 values=["USD", "EUR", "GBP", "JPY", "CAD", "AUD"], width=8, state="readonly")
        from_combo.pack(side="left", padx=(0, 10))
        
        ttk.Label(conv_controls_frame, text="To:").pack(side="left", padx=(0, 5))
        to_combo = ttk.Combobox(conv_controls_frame, textvariable=self.to_currency_var, 
                               values=["USD", "EUR", "GBP", "JPY", "CAD", "AUD"], width=8, state="readonly")
        to_combo.pack(side="left", padx=(0, 10))
        
        ttk.Button(conv_controls_frame, text="Convert", command=self.quick_convert, 
                  bootstyle=PRIMARY).pack(side="left", padx=(10, 0))
        
        # Result display
        result_frame = ttk.Frame(converter_frame)
        result_frame.pack(fill="x", pady=(10, 0))
        
        ttk.Label(result_frame, text="Result:", font=("Segoe UI", 12, "bold")).pack(side="left")
        result_label = ttk.Label(result_frame, textvariable=self.result_var, 
                               font=("Segoe UI", 14, "bold"), foreground="blue")
        result_label.pack(side="left", padx=(10, 0))
        
        # Status and info
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill="x")
        
        self.status_var = tk.StringVar(value="Multi-Currency Demo Ready - Try the features above!")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, foreground="gray")
        status_label.pack(side="left")
        
        # Close button
        close_button = ttk.Button(status_frame, text="Close Demo", command=self.close_demo, bootstyle=DANGER)
        close_button.pack(side="right")
    
    def open_currency_management(self):
        """Open currency management demo"""
        try:
            currency_window = tk.Toplevel(self.root)
            currency_window.title("Currency Management Demo")
            currency_window.geometry("1000x700")
            currency_window.transient(self.root)
            
            def close_currency_window():
                currency_window.destroy()
                self.status_var.set("Currency Management demo closed")
            
            currency_frame = CurrencyManagementFrame(currency_window, self.db_path, close_currency_window)
            currency_frame.pack(fill="both", expand=True)
            
            self.status_var.set("Currency Management demo opened")
            
        except Exception as e:
            self.status_var.set(f"Error opening Currency Management: {str(e)}")
    
    def open_mc_transaction(self):
        """Open multi-currency transaction demo"""
        try:
            transaction_window = tk.Toplevel(self.root)
            transaction_window.title("Multi-Currency Transaction Demo")
            transaction_window.geometry("800x600")
            transaction_window.transient(self.root)
            
            def close_transaction_window():
                transaction_window.destroy()
                self.status_var.set("Multi-Currency Transaction demo closed")
            
            transaction_frame = MultiCurrencyTransactionFrame(transaction_window, self.db_path, close_transaction_window)
            transaction_frame.pack(fill="both", expand=True)
            
            self.status_var.set("Multi-Currency Transaction demo opened")
            
        except Exception as e:
            self.status_var.set(f"Error opening Multi-Currency Transaction: {str(e)}")
    
    def open_mc_reports(self):
        """Open multi-currency reports demo"""
        try:
            reports_window = tk.Toplevel(self.root)
            reports_window.title("Multi-Currency Reports Demo")
            reports_window.geometry("1200x800")
            reports_window.transient(self.root)
            
            def close_reports_window():
                reports_window.destroy()
                self.status_var.set("Multi-Currency Reports demo closed")
            
            reports_frame = MultiCurrencyReportsFrame(reports_window, self.db_path, close_reports_window)
            reports_frame.pack(fill="both", expand=True)
            
            self.status_var.set("Multi-Currency Reports demo opened")
            
        except Exception as e:
            self.status_var.set(f"Error opening Multi-Currency Reports: {str(e)}")
    
    def quick_convert(self):
        """Perform quick currency conversion"""
        try:
            currency_model = Currency(self.db_path)
            
            amount = self.amount_var.get()
            from_currency = self.from_currency_var.get()
            to_currency = self.to_currency_var.get()
            
            if from_currency == to_currency:
                result = amount
            else:
                result = currency_model.convert_amount(amount, from_currency, to_currency)
            
            formatted_result = currency_model.format_currency_amount(result, to_currency)
            self.result_var.set(formatted_result)
            
            self.status_var.set(f"Converted {amount} {from_currency} to {formatted_result}")
            
        except Exception as e:
            self.status_var.set(f"Conversion error: {str(e)}")
    
    def close_demo(self):
        """Close the demo and clean up"""
        try:
            # Ask for confirmation
            result = tk.messagebox.askyesno(
                "Close Demo",
                "Close the Multi-Currency Demo?\n\nDemo database will be preserved for testing."
            )
            
            if result:
                self.root.quit()
                
        except Exception as e:
            print(f"Error closing demo: {e}")
            self.root.quit()
    
    def run(self):
        """Run the demo"""
        print("=" * 70)
        print("MULTI-CURRENCY SUPPORT DEMO - PHASE 10.3")
        print("=" * 70)
        print()
        print("🌍 Features Demonstrated:")
        print("✅ Currency Management - Add, edit, manage currencies")
        print("✅ Exchange Rate Management - Manual and automatic updates")
        print("✅ Multi-Currency Transactions - Cross-currency transactions")
        print("✅ Currency Conversion - Real-time conversion calculator")
        print("✅ Gains/Losses Tracking - Currency gain/loss analysis")
        print("✅ Multi-Currency Reporting - Comprehensive reports")
        print("✅ Currency Risk Analysis - Exposure and risk assessment")
        print()
        print("💡 Demo Database: demo_multicurrency.db")
        print("📊 Sample Data: 15 currencies, exchange rates included")
        print()
        print("🚀 Starting Multi-Currency Demo...")
        print("=" * 70)
        
        self.root.mainloop()


def main():
    """Main function"""
    print("Multi-Currency Support Demo")
    print("Phase 10.3 - Complete Implementation")
    print("=" * 50)
    
    # Check if required files exist
    required_files = [
        "model/currency.py",
        "view/currency_management_frame.py",
        "view/multi_currency_transaction_frame.py",
        "view/multi_currency_reports_frame.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ERROR: Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all Multi-Currency components are present.")
        return
    
    print("✅ All required files found")
    print("✅ Starting multi-currency demo...")
    print()
    
    try:
        app = MultiCurrencyDemo()
        app.run()
        
        print("\n" + "=" * 50)
        print("Multi-Currency Demo completed!")
        print("Phase 10.3 implementation is fully functional.")
        
    except Exception as e:
        print(f"\n❌ ERROR: Failed to start demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
