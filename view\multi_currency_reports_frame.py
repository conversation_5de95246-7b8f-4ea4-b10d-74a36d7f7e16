import tkinter as tk
from tkinter import messagebox, ttk as tkinter_ttk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from datetime import datetime, date, timedelta
import sqlite3

from model.currency import Currency
from model.account import Account


class MultiCurrencyReportsFrame(ttk.Frame):
    """Frame for multi-currency reporting and analysis"""

    def __init__(self, parent, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = "Multi-Currency Reports"

        # Create models
        self.currency_model = Currency(db_path)
        self.account_model = Account(db_path)

        # Variables
        self.date_from_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.date_to_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.base_currency_var = tk.StringVar()
        self.report_currency_var = tk.StringVar()

        self.create_widgets()
        self.load_data()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Create notebook for different reports
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, pady=(0, 20))

        # Create tabs
        self.create_account_balances_tab()
        self.create_currency_exposure_tab()
        self.create_gains_losses_tab()
        self.create_conversion_summary_tab()

    def create_account_balances_tab(self):
        """Create account balances in multiple currencies tab"""
        balances_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(balances_frame, text="Account Balances")

        # Controls
        controls_frame = ttk.Frame(balances_frame)
        controls_frame.pack(fill="x", pady=(0, 20))

        ttk.Label(controls_frame, text="Report Currency:").pack(side="left", padx=(0, 5))
        self.report_currency_combo = ttk.Combobox(controls_frame, textvariable=self.report_currency_var, 
                                                 width=10, state="readonly")
        self.report_currency_combo.pack(side="left", padx=(0, 10))

        ttk.Button(controls_frame, text="Generate Report", command=self.generate_balance_report, 
                  bootstyle=PRIMARY).pack(side="left", padx=(0, 10))

        ttk.Button(controls_frame, text="Export to CSV", command=self.export_balance_report, 
                  bootstyle=INFO).pack(side="left")

        # Account balances treeview
        balance_columns = ("Account", "Original Currency", "Original Balance", "Report Currency", "Converted Balance", "Exchange Rate")
        self.balance_tree = ttk.Treeview(balances_frame, columns=balance_columns, show="headings", height=12)

        for col in balance_columns:
            self.balance_tree.heading(col, text=col)
            self.balance_tree.column(col, width=120)

        # Scrollbar for balance tree
        balance_scrollbar = ttk.Scrollbar(balances_frame, orient="vertical", command=self.balance_tree.yview)
        self.balance_tree.configure(yscrollcommand=balance_scrollbar.set)

        self.balance_tree.pack(side="left", fill="both", expand=True)
        balance_scrollbar.pack(side="right", fill="y")

    def create_currency_exposure_tab(self):
        """Create currency exposure analysis tab"""
        exposure_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(exposure_frame, text="Currency Exposure")

        # Controls
        controls_frame = ttk.Frame(exposure_frame)
        controls_frame.pack(fill="x", pady=(0, 20))

        ttk.Button(controls_frame, text="Analyze Exposure", command=self.analyze_currency_exposure, 
                  bootstyle=PRIMARY).pack(side="left", padx=(0, 10))

        ttk.Button(controls_frame, text="Calculate Risk", command=self.calculate_currency_risk, 
                  bootstyle=WARNING).pack(side="left")

        # Exposure summary
        summary_frame = ttk.LabelFrame(exposure_frame, text="Exposure Summary", padding=15)
        summary_frame.pack(fill="x", pady=(0, 20))

        self.exposure_summary_text = tk.Text(summary_frame, height=6, wrap=tk.WORD)
        self.exposure_summary_text.pack(fill="x")

        # Currency exposure treeview
        exposure_columns = ("Currency", "Total Assets", "Total Liabilities", "Net Exposure", "% of Portfolio", "Risk Level")
        self.exposure_tree = ttk.Treeview(exposure_frame, columns=exposure_columns, show="headings", height=8)

        for col in exposure_columns:
            self.exposure_tree.heading(col, text=col)
            self.exposure_tree.column(col, width=100)

        self.exposure_tree.pack(fill="both", expand=True)

    def create_gains_losses_tab(self):
        """Create gains/losses analysis tab"""
        gains_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(gains_frame, text="Gains & Losses")

        # Date range controls
        date_frame = ttk.Frame(gains_frame)
        date_frame.pack(fill="x", pady=(0, 20))

        ttk.Label(date_frame, text="From:").pack(side="left", padx=(0, 5))
        ttk.Entry(date_frame, textvariable=self.date_from_var, width=12).pack(side="left", padx=(0, 10))

        ttk.Label(date_frame, text="To:").pack(side="left", padx=(0, 5))
        ttk.Entry(date_frame, textvariable=self.date_to_var, width=12).pack(side="left", padx=(0, 10))

        ttk.Button(date_frame, text="Calculate Gains/Losses", command=self.calculate_gains_losses, 
                  bootstyle=PRIMARY).pack(side="left", padx=(10, 0))

        # Gains/Losses summary
        summary_frame = ttk.LabelFrame(gains_frame, text="Summary", padding=15)
        summary_frame.pack(fill="x", pady=(0, 20))

        self.gains_summary_vars = {
            'realized_gains': tk.StringVar(value="0.00"),
            'unrealized_gains': tk.StringVar(value="0.00"),
            'total_gains': tk.StringVar(value="0.00")
        }

        base_currency = self.currency_model.get_base_currency()
        base_symbol = base_currency['symbol']

        ttk.Label(summary_frame, text="Realized Gains/Losses:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        ttk.Label(summary_frame, textvariable=self.gains_summary_vars['realized_gains'], 
                 font=("Segoe UI", 10, "bold")).grid(row=0, column=1, sticky="w")

        ttk.Label(summary_frame, text="Unrealized Gains/Losses:").grid(row=1, column=0, sticky="w", padx=(0, 10))
        ttk.Label(summary_frame, textvariable=self.gains_summary_vars['unrealized_gains'], 
                 font=("Segoe UI", 10, "bold")).grid(row=1, column=1, sticky="w")

        ttk.Label(summary_frame, text="Total Gains/Losses:").grid(row=2, column=0, sticky="w", padx=(0, 10))
        ttk.Label(summary_frame, textvariable=self.gains_summary_vars['total_gains'], 
                 font=("Segoe UI", 12, "bold"), foreground="blue").grid(row=2, column=1, sticky="w")

        # Detailed gains/losses
        gains_columns = ("Account", "Currency", "Original Amount", "Current Value", "Gain/Loss", "Type", "Date")
        self.gains_tree = ttk.Treeview(gains_frame, columns=gains_columns, show="headings", height=10)

        for col in gains_columns:
            self.gains_tree.heading(col, text=col)
            self.gains_tree.column(col, width=100)

        self.gains_tree.pack(fill="both", expand=True)

    def create_conversion_summary_tab(self):
        """Create currency conversion summary tab"""
        conversion_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(conversion_frame, text="Conversion Summary")

        # Controls
        controls_frame = ttk.Frame(conversion_frame)
        controls_frame.pack(fill="x", pady=(0, 20))

        ttk.Button(controls_frame, text="Generate Summary", command=self.generate_conversion_summary, 
                  bootstyle=PRIMARY).pack(side="left", padx=(0, 10))

        ttk.Button(controls_frame, text="Update Exchange Rates", command=self.update_all_rates, 
                  bootstyle=INFO).pack(side="left")

        # Current exchange rates
        rates_frame = ttk.LabelFrame(conversion_frame, text="Current Exchange Rates", padding=15)
        rates_frame.pack(fill="x", pady=(0, 20))

        rates_columns = ("From Currency", "To Currency", "Exchange Rate", "Last Updated", "Source")
        self.rates_tree = ttk.Treeview(rates_frame, columns=rates_columns, show="headings", height=8)

        for col in rates_columns:
            self.rates_tree.heading(col, text=col)
            self.rates_tree.column(col, width=100)

        self.rates_tree.pack(fill="both", expand=True)

        # Conversion calculator
        calc_frame = ttk.LabelFrame(conversion_frame, text="Quick Converter", padding=15)
        calc_frame.pack(fill="x")

        self.calc_amount_var = tk.DoubleVar(value=1.0)
        self.calc_from_var = tk.StringVar()
        self.calc_to_var = tk.StringVar()
        self.calc_result_var = tk.StringVar()

        ttk.Label(calc_frame, text="Amount:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        ttk.Entry(calc_frame, textvariable=self.calc_amount_var, width=12).grid(row=0, column=1, padx=(0, 10))

        ttk.Label(calc_frame, text="From:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        calc_from_combo = ttk.Combobox(calc_frame, textvariable=self.calc_from_var, width=8, state="readonly")
        calc_from_combo.grid(row=0, column=3, padx=(0, 10))

        ttk.Label(calc_frame, text="To:").grid(row=0, column=4, sticky="w", padx=(0, 5))
        calc_to_combo = ttk.Combobox(calc_frame, textvariable=self.calc_to_var, width=8, state="readonly")
        calc_to_combo.grid(row=0, column=5, padx=(0, 10))

        ttk.Button(calc_frame, text="Convert", command=self.quick_convert, bootstyle=SUCCESS).grid(row=0, column=6, padx=(10, 0))

        ttk.Label(calc_frame, text="Result:", font=("Segoe UI", 10, "bold")).grid(row=1, column=0, sticky="w", pady=(10, 0))
        ttk.Label(calc_frame, textvariable=self.calc_result_var, font=("Segoe UI", 12, "bold"), 
                 foreground="blue").grid(row=1, column=1, columnspan=3, sticky="w", pady=(10, 0))

        # Store combo references
        self.calc_from_combo = calc_from_combo
        self.calc_to_combo = calc_to_combo

    def load_data(self):
        """Load initial data"""
        # Load currencies
        currencies = self.currency_model.get_all_currencies()
        currency_codes = [curr['code'] for curr in currencies]
        
        if hasattr(self, 'report_currency_combo'):
            self.report_currency_combo['values'] = currency_codes
            self.calc_from_combo['values'] = currency_codes
            self.calc_to_combo['values'] = currency_codes

        # Set base currency as default
        base_currency = self.currency_model.get_base_currency()
        self.base_currency_var.set(base_currency['code'])
        self.report_currency_var.set(base_currency['code'])

    def generate_balance_report(self):
        """Generate account balances report in selected currency"""
        try:
            report_currency = self.report_currency_var.get()
            if not report_currency:
                messagebox.showerror("Error", "Please select a report currency")
                return

            # Clear existing items
            for item in self.balance_tree.get_children():
                self.balance_tree.delete(item)

            # Get all accounts
            accounts = self.account_model.get_all_accounts()
            
            for account in accounts:
                if not account['is_active']:
                    continue

                original_currency = account['currency']
                original_balance = account['current_balance']
                
                if original_currency == report_currency:
                    converted_balance = original_balance
                    exchange_rate = 1.0
                else:
                    exchange_rate = self.currency_model.get_exchange_rate(original_currency, report_currency)
                    converted_balance = self.currency_model.convert_amount(original_balance, original_currency, report_currency)

                # Format values
                original_formatted = self.currency_model.format_currency_amount(original_balance, original_currency)
                converted_formatted = self.currency_model.format_currency_amount(converted_balance, report_currency)

                values = (
                    account['name'],
                    original_currency,
                    original_formatted,
                    report_currency,
                    converted_formatted,
                    f"{exchange_rate:.6f}"
                )
                
                self.balance_tree.insert("", "end", values=values)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate balance report: {str(e)}")

    def analyze_currency_exposure(self):
        """Analyze currency exposure across accounts"""
        try:
            # Clear existing items
            for item in self.exposure_tree.get_children():
                self.exposure_tree.delete(item)

            base_currency = self.currency_model.get_base_currency()
            base_currency_code = base_currency['code']

            # Get accounts grouped by currency
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT currency, 
                       SUM(CASE WHEN current_balance > 0 THEN current_balance ELSE 0 END) as assets,
                       SUM(CASE WHEN current_balance < 0 THEN ABS(current_balance) ELSE 0 END) as liabilities,
                       SUM(current_balance) as net_exposure
                FROM accounts 
                WHERE is_active = 1
                GROUP BY currency
                ORDER BY ABS(SUM(current_balance)) DESC
            ''')

            exposure_data = cursor.fetchall()
            total_portfolio_value = 0

            # Calculate total portfolio value in base currency
            for currency_code, assets, liabilities, net_exposure in exposure_data:
                if currency_code != base_currency_code:
                    rate = self.currency_model.get_exchange_rate(currency_code, base_currency_code)
                    net_exposure_base = net_exposure * rate
                else:
                    net_exposure_base = net_exposure
                total_portfolio_value += abs(net_exposure_base)

            # Generate exposure analysis
            exposure_summary = f"Currency Exposure Analysis (Base: {base_currency_code})\n"
            exposure_summary += "=" * 50 + "\n\n"

            for currency_code, assets, liabilities, net_exposure in exposure_data:
                if currency_code != base_currency_code:
                    rate = self.currency_model.get_exchange_rate(currency_code, base_currency_code)
                    net_exposure_base = net_exposure * rate
                else:
                    rate = 1.0
                    net_exposure_base = net_exposure

                # Calculate percentage of portfolio
                portfolio_percentage = (abs(net_exposure_base) / total_portfolio_value * 100) if total_portfolio_value > 0 else 0

                # Determine risk level
                if portfolio_percentage > 25:
                    risk_level = "High"
                elif portfolio_percentage > 10:
                    risk_level = "Medium"
                else:
                    risk_level = "Low"

                # Format values
                assets_formatted = self.currency_model.format_currency_amount(assets, currency_code)
                liabilities_formatted = self.currency_model.format_currency_amount(liabilities, currency_code)
                net_formatted = self.currency_model.format_currency_amount(net_exposure, currency_code)

                values = (
                    currency_code,
                    assets_formatted,
                    liabilities_formatted,
                    net_formatted,
                    f"{portfolio_percentage:.1f}%",
                    risk_level
                )
                
                self.exposure_tree.insert("", "end", values=values)

                exposure_summary += f"{currency_code}: {net_formatted} ({portfolio_percentage:.1f}% of portfolio, {risk_level} risk)\n"

            # Update summary text
            self.exposure_summary_text.delete(1.0, tk.END)
            self.exposure_summary_text.insert(1.0, exposure_summary)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze currency exposure: {str(e)}")
        finally:
            if conn:
                conn.close()

    def calculate_currency_risk(self):
        """Calculate currency risk metrics"""
        messagebox.showinfo("Info", "Currency risk calculation feature coming soon!")

    def calculate_gains_losses(self):
        """Calculate realized and unrealized gains/losses"""
        try:
            # Clear existing items
            for item in self.gains_tree.get_children():
                self.gains_tree.delete(item)

            # Calculate unrealized gains/losses
            total_unrealized = self.currency_model.calculate_unrealized_gains_losses()

            # For now, set realized gains to 0 (would need transaction history analysis)
            total_realized = 0.0
            total_gains = total_realized + total_unrealized

            # Update summary
            base_currency = self.currency_model.get_base_currency()
            symbol = base_currency['symbol']

            self.gains_summary_vars['realized_gains'].set(f"{symbol}{total_realized:.2f}")
            self.gains_summary_vars['unrealized_gains'].set(f"{symbol}{total_unrealized:.2f}")
            self.gains_summary_vars['total_gains'].set(f"{symbol}{total_gains:.2f}")

            # Load detailed gains/losses
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT a.name, cgl.currency_code, cgl.original_amount, 
                       cgl.base_amount, cgl.gain_loss_amount, cgl.gain_loss_type, 
                       cgl.calculation_date
                FROM currency_gains_losses cgl
                JOIN accounts a ON cgl.account_id = a.id
                WHERE cgl.calculation_date BETWEEN ? AND ?
                ORDER BY cgl.calculation_date DESC
            ''', (self.date_from_var.get(), self.date_to_var.get()))

            for row in cursor.fetchall():
                formatted_row = (
                    row[0],  # Account name
                    row[1],  # Currency
                    f"{row[2]:.2f}",  # Original amount
                    f"{row[3]:.2f}",  # Current value
                    f"{row[4]:.2f}",  # Gain/Loss
                    row[5].title(),  # Type
                    row[6]   # Date
                )
                self.gains_tree.insert("", "end", values=formatted_row)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to calculate gains/losses: {str(e)}")
        finally:
            if conn:
                conn.close()

    def generate_conversion_summary(self):
        """Generate currency conversion summary"""
        try:
            # Clear existing items
            for item in self.rates_tree.get_children():
                self.rates_tree.delete(item)

            # Load current exchange rates
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT from_currency_code, to_currency_code, rate, rate_date, source
                FROM exchange_rates 
                WHERE is_active = 1
                ORDER BY rate_date DESC, from_currency_code
            ''')

            for row in cursor.fetchall():
                self.rates_tree.insert("", "end", values=row)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate conversion summary: {str(e)}")
        finally:
            if conn:
                conn.close()

    def update_all_rates(self):
        """Update all exchange rates"""
        if self.currency_model.update_exchange_rates_from_api():
            messagebox.showinfo("Success", "Exchange rates updated successfully")
            self.generate_conversion_summary()
        else:
            messagebox.showinfo("Info", "Exchange rate API not available yet")

    def quick_convert(self):
        """Perform quick currency conversion"""
        try:
            amount = self.calc_amount_var.get()
            from_currency = self.calc_from_var.get()
            to_currency = self.calc_to_var.get()

            if not from_currency or not to_currency:
                messagebox.showerror("Error", "Please select both currencies")
                return

            converted = self.currency_model.convert_amount(amount, from_currency, to_currency)
            formatted_result = self.currency_model.format_currency_amount(converted, to_currency)
            
            self.calc_result_var.set(formatted_result)

        except Exception as e:
            messagebox.showerror("Error", f"Conversion failed: {str(e)}")

    def export_balance_report(self):
        """Export balance report to CSV"""
        messagebox.showinfo("Info", "CSV export feature coming soon!")
