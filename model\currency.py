import sqlite3
import json
import requests
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from model.base_model import BaseModel


class Currency(BaseModel):
    """Model for managing currencies and exchange rates"""

    def __init__(self, db_path):
        super().__init__(db_path)
        self._table_name = "currencies"
        self._create_tables()
        self._initialize_default_currencies()

    def table_name(self):
        return self._table_name

    def fields(self):
        return [
            'id', 'code', 'name', 'symbol', 'decimal_places', 'is_base_currency',
            'is_active', 'created_at', 'updated_at'
        ]

    def primary_key(self):
        return 'id'

    def _create_tables(self):
        """Create currency-related tables"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Create currencies table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    decimal_places INTEGER DEFAULT 2,
                    is_base_currency BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create exchange_rates table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS exchange_rates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    from_currency_code TEXT NOT NULL,
                    to_currency_code TEXT NOT NULL,
                    rate DECIMAL(15,6) NOT NULL,
                    rate_date DATE NOT NULL,
                    source TEXT DEFAULT 'manual',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (from_currency_code) REFERENCES currencies (code),
                    FOREIGN KEY (to_currency_code) REFERENCES currencies (code),
                    UNIQUE(from_currency_code, to_currency_code, rate_date)
                )
            ''')

            # Create currency_gains_losses table for tracking unrealized/realized gains
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS currency_gains_losses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id INTEGER,
                    account_id INTEGER,
                    currency_code TEXT NOT NULL,
                    original_amount DECIMAL(15,2) NOT NULL,
                    base_amount DECIMAL(15,2) NOT NULL,
                    exchange_rate DECIMAL(15,6) NOT NULL,
                    gain_loss_amount DECIMAL(15,2) DEFAULT 0,
                    gain_loss_type TEXT CHECK(gain_loss_type IN ('realized', 'unrealized')),
                    calculation_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (transaction_id) REFERENCES transactions (id),
                    FOREIGN KEY (account_id) REFERENCES accounts (id),
                    FOREIGN KEY (currency_code) REFERENCES currencies (code)
                )
            ''')

            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_exchange_rates_date ON exchange_rates(rate_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_exchange_rates_currencies ON exchange_rates(from_currency_code, to_currency_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_currency_gains_losses_account ON currency_gains_losses(account_id)')

            conn.commit()

        except sqlite3.Error as e:
            print(f"Error creating currency tables: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    def _initialize_default_currencies(self):
        """Initialize default currencies if none exist"""
        try:
            if not self.get_all_currencies():
                default_currencies = [
                    ('USD', 'US Dollar', '$', 2, True),
                    ('EUR', 'Euro', '€', 2, False),
                    ('GBP', 'British Pound', '£', 2, False),
                    ('JPY', 'Japanese Yen', '¥', 0, False),
                    ('CAD', 'Canadian Dollar', 'C$', 2, False),
                    ('AUD', 'Australian Dollar', 'A$', 2, False),
                    ('CHF', 'Swiss Franc', 'CHF', 2, False),
                    ('CNY', 'Chinese Yuan', '¥', 2, False),
                    ('INR', 'Indian Rupee', '₹', 2, False),
                    ('NZD', 'New Zealand Dollar', 'NZ$', 2, False),
                    ('SEK', 'Swedish Krona', 'kr', 2, False),
                    ('NOK', 'Norwegian Krone', 'kr', 2, False),
                    ('DKK', 'Danish Krone', 'kr', 2, False),
                    ('SGD', 'Singapore Dollar', 'S$', 2, False),
                    ('HKD', 'Hong Kong Dollar', 'HK$', 2, False)
                ]

                for code, name, symbol, decimal_places, is_base in default_currencies:
                    self.add_currency(code, name, symbol, decimal_places, is_base)

        except Exception as e:
            print(f"Error initializing default currencies: {e}")

    def add_currency(self, code, name, symbol, decimal_places=2, is_base_currency=False):
        """Add a new currency"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # If this is set as base currency, unset all others
            if is_base_currency:
                cursor.execute('UPDATE currencies SET is_base_currency = 0')

            cursor.execute('''
                INSERT OR REPLACE INTO currencies 
                (code, name, symbol, decimal_places, is_base_currency, is_active, updated_at)
                VALUES (?, ?, ?, ?, ?, 1, ?)
            ''', (code, name, symbol, decimal_places, is_base_currency, datetime.now()))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"Error adding currency: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def get_all_currencies(self, active_only=True):
        """Get all currencies"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            query = 'SELECT * FROM currencies'
            if active_only:
                query += ' WHERE is_active = 1'
            query += ' ORDER BY is_base_currency DESC, code ASC'

            cursor.execute(query)
            columns = [description[0] for description in cursor.description]
            currencies = []

            for row in cursor.fetchall():
                currency = dict(zip(columns, row))
                currencies.append(currency)

            return currencies

        except sqlite3.Error as e:
            print(f"Error getting currencies: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def get_base_currency(self):
        """Get the base currency"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM currencies WHERE is_base_currency = 1 LIMIT 1')
            row = cursor.fetchone()

            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            else:
                # Default to USD if no base currency is set
                return {'code': 'USD', 'name': 'US Dollar', 'symbol': '$', 'decimal_places': 2}

        except sqlite3.Error as e:
            print(f"Error getting base currency: {e}")
            return {'code': 'USD', 'name': 'US Dollar', 'symbol': '$', 'decimal_places': 2}
        finally:
            if conn:
                conn.close()

    def set_base_currency(self, currency_code):
        """Set a currency as the base currency"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Unset all base currencies
            cursor.execute('UPDATE currencies SET is_base_currency = 0')

            # Set the new base currency
            cursor.execute(
                'UPDATE currencies SET is_base_currency = 1, updated_at = ? WHERE code = ?',
                (datetime.now(), currency_code)
            )

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"Error setting base currency: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def add_exchange_rate(self, from_currency, to_currency, rate, rate_date=None, source='manual'):
        """Add or update an exchange rate"""
        try:
            if rate_date is None:
                rate_date = datetime.now().date()

            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Add the rate
            cursor.execute('''
                INSERT OR REPLACE INTO exchange_rates 
                (from_currency_code, to_currency_code, rate, rate_date, source, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (from_currency, to_currency, rate, rate_date, source, datetime.now()))

            # Also add the inverse rate
            if rate > 0:
                inverse_rate = 1 / float(rate)
                cursor.execute('''
                    INSERT OR REPLACE INTO exchange_rates 
                    (from_currency_code, to_currency_code, rate, rate_date, source, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (to_currency, from_currency, inverse_rate, rate_date, source, datetime.now()))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"Error adding exchange rate: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def get_exchange_rate(self, from_currency, to_currency, rate_date=None):
        """Get exchange rate between two currencies"""
        try:
            if from_currency == to_currency:
                return 1.0

            if rate_date is None:
                rate_date = datetime.now().date()

            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Try to get exact date first
            cursor.execute('''
                SELECT rate FROM exchange_rates 
                WHERE from_currency_code = ? AND to_currency_code = ? 
                AND rate_date = ? AND is_active = 1
                ORDER BY created_at DESC LIMIT 1
            ''', (from_currency, to_currency, rate_date))

            result = cursor.fetchone()
            if result:
                return float(result[0])

            # If no exact date, get the most recent rate before the date
            cursor.execute('''
                SELECT rate FROM exchange_rates 
                WHERE from_currency_code = ? AND to_currency_code = ? 
                AND rate_date <= ? AND is_active = 1
                ORDER BY rate_date DESC, created_at DESC LIMIT 1
            ''', (from_currency, to_currency, rate_date))

            result = cursor.fetchone()
            if result:
                return float(result[0])

            # If still no rate found, return 1.0 as default
            return 1.0

        except sqlite3.Error as e:
            print(f"Error getting exchange rate: {e}")
            return 1.0
        finally:
            if conn:
                conn.close()

    def convert_amount(self, amount, from_currency, to_currency, rate_date=None):
        """Convert amount from one currency to another"""
        try:
            if from_currency == to_currency:
                return float(amount)

            rate = self.get_exchange_rate(from_currency, to_currency, rate_date)
            converted_amount = float(amount) * rate

            # Round to appropriate decimal places
            to_currency_info = self.get_currency_by_code(to_currency)
            decimal_places = to_currency_info.get('decimal_places', 2) if to_currency_info else 2

            return round(converted_amount, decimal_places)

        except Exception as e:
            print(f"Error converting amount: {e}")
            return float(amount)

    def get_currency_by_code(self, currency_code):
        """Get currency information by code"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM currencies WHERE code = ? AND is_active = 1', (currency_code,))
            row = cursor.fetchone()

            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))

            return None

        except sqlite3.Error as e:
            print(f"Error getting currency by code: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def format_currency_amount(self, amount, currency_code):
        """Format amount according to currency specifications"""
        try:
            currency_info = self.get_currency_by_code(currency_code)
            if not currency_info:
                return f"{amount:.2f}"

            decimal_places = currency_info.get('decimal_places', 2)
            symbol = currency_info.get('symbol', currency_code)

            # Round to appropriate decimal places
            rounded_amount = round(float(amount), decimal_places)

            # Format with appropriate decimal places
            if decimal_places == 0:
                formatted_amount = f"{rounded_amount:.0f}"
            else:
                formatted_amount = f"{rounded_amount:.{decimal_places}f}"

            return f"{symbol}{formatted_amount}"

        except Exception as e:
            print(f"Error formatting currency amount: {e}")
            return f"{amount:.2f}"

    def update_exchange_rates_from_api(self):
        """Update exchange rates from external API (placeholder for future implementation)"""
        # This would integrate with services like:
        # - exchangerate-api.com
        # - fixer.io
        # - currencylayer.com
        # For now, return True as placeholder
        print("Exchange rate API update not implemented yet")
        return True

    def calculate_unrealized_gains_losses(self, account_id=None):
        """Calculate unrealized gains/losses for foreign currency accounts"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            base_currency = self.get_base_currency()
            base_currency_code = base_currency['code']

            # Get accounts with foreign currencies
            query = '''
                SELECT id, currency, current_balance 
                FROM accounts 
                WHERE currency != ? AND is_active = 1
            '''
            params = [base_currency_code]

            if account_id:
                query += ' AND id = ?'
                params.append(account_id)

            cursor.execute(query, params)
            accounts = cursor.fetchall()

            total_unrealized_gain_loss = 0

            for account_id, currency_code, balance in accounts:
                if balance == 0:
                    continue

                # Get current exchange rate
                current_rate = self.get_exchange_rate(currency_code, base_currency_code)
                current_base_value = float(balance) * current_rate

                # Get historical base value (this would need more complex logic in real implementation)
                # For now, we'll use a simplified approach
                historical_rate = self.get_exchange_rate(currency_code, base_currency_code, 
                                                       datetime.now().date() - timedelta(days=30))
                historical_base_value = float(balance) * historical_rate

                unrealized_gain_loss = current_base_value - historical_base_value
                total_unrealized_gain_loss += unrealized_gain_loss

                # Store the calculation
                cursor.execute('''
                    INSERT OR REPLACE INTO currency_gains_losses
                    (account_id, currency_code, original_amount, base_amount, 
                     exchange_rate, gain_loss_amount, gain_loss_type, calculation_date)
                    VALUES (?, ?, ?, ?, ?, ?, 'unrealized', ?)
                ''', (account_id, currency_code, balance, current_base_value, 
                      current_rate, unrealized_gain_loss, datetime.now().date()))

            conn.commit()
            return total_unrealized_gain_loss

        except sqlite3.Error as e:
            print(f"Error calculating unrealized gains/losses: {e}")
            if conn:
                conn.rollback()
            return 0
        finally:
            if conn:
                conn.close()
