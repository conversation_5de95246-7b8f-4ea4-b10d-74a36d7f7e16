import tkinter as tk
from tkinter import messagebox, ttk

from ttkbootstrap.constants import DANGER, INFO, SUCCESS


class AdminDashboard:
    def __init__(self, root, username, logout_callback, db=None):
        self.root = root
        self.username = username
        self.logout_callback = logout_callback

        # Get database instance
        if db is None:
            from model.database import Database
            self.db = Database()
        else:
            self.db = db

        self.root.title("Admin Dashboard")
        self.root.geometry("1000x700")

        self.create_widgets()
        self.load_users()
        self.load_companies()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with welcome message and logout button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        welcome_label = ttk.Label(header_frame, text=f"Welcome, {self.username} (Admin)", font=("Segoe UI", 16, "bold"))
        welcome_label.pack(side="left")

        logout_button = ttk.Button(header_frame, text="Logout", command=self.logout_callback, bootstyle=DANGER)
        logout_button.pack(side="right")

        # Notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill="both", expand=True)

        # User Management Tab
        user_tab = ttk.Frame(notebook, padding=10)
        notebook.add(user_tab, text="User Management")

        # User list
        users_frame = ttk.LabelFrame(user_tab, text="Users")
        users_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # User treeview with scrollbars
        columns = ("id", "username", "role")

        # Create a frame for the treeview and scrollbar
        tree_frame = ttk.Frame(users_frame)
        tree_frame.pack(fill="both", expand=True)

        # Create the treeview
        self.user_tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.user_tree.yview)
        vsb.pack(side="right", fill="y")
        self.user_tree.configure(yscrollcommand=vsb.set)

        # Add horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.user_tree.xview)
        hsb.pack(side="bottom", fill="x")
        self.user_tree.configure(xscrollcommand=hsb.set)

        # Define headings
        self.user_tree.heading("id", text="ID")
        self.user_tree.heading("username", text="Username")
        self.user_tree.heading("role", text="Role")

        # Define columns
        self.user_tree.column("id", width=50)
        self.user_tree.column("username", width=200)
        self.user_tree.column("role", width=100)

        self.user_tree.pack(side="left", fill="both", expand=True)

        # User action buttons
        user_buttons_frame = ttk.Frame(user_tab)
        user_buttons_frame.pack(fill="x", pady=10)

        add_user_button = ttk.Button(user_buttons_frame, text="Add User", command=self.add_user, bootstyle=SUCCESS)
        add_user_button.pack(side="left", padx=5)

        delete_user_button = ttk.Button(user_buttons_frame, text="Delete User", command=self.delete_user, bootstyle=DANGER)
        delete_user_button.pack(side="left", padx=5)

        # Companies Tab
        companies_tab = ttk.Frame(notebook, padding=10)
        notebook.add(companies_tab, text="Companies")

        # Company list
        companies_frame = ttk.LabelFrame(companies_tab, text="All Companies")
        companies_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Company treeview with scrollbars
        columns = ("name", "owner", "transactions", "balance")

        # Create a frame for the treeview and scrollbar
        tree_frame = ttk.Frame(companies_frame)
        tree_frame.pack(fill="both", expand=True)

        # Create the treeview
        self.company_tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.company_tree.yview)
        vsb.pack(side="right", fill="y")
        self.company_tree.configure(yscrollcommand=vsb.set)

        # Add horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.company_tree.xview)
        hsb.pack(side="bottom", fill="x")
        self.company_tree.configure(xscrollcommand=hsb.set)

        # Define headings
        self.company_tree.heading("name", text="Company Name")
        self.company_tree.heading("owner", text="Owner")
        self.company_tree.heading("transactions", text="Transactions")
        self.company_tree.heading("balance", text="Balance")

        # Define columns
        self.company_tree.column("name", width=200)
        self.company_tree.column("owner", width=150)
        self.company_tree.column("transactions", width=100)
        self.company_tree.column("balance", width=100)

        self.company_tree.pack(side="left", fill="both", expand=True)

        # View button
        view_button = ttk.Button(companies_tab, text="View Company Details", command=self.view_company_details, bootstyle=INFO)
        view_button.pack(pady=10)

    def load_users(self):
        """Load users into the treeview"""
        # Clear existing items
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)

        # Get all users from database
        users = self.db.get_all_users()

        # Add users to treeview
        for user in users:
            self.user_tree.insert("", "end", values=(user["id"], user["username"], user["role"]))

    def add_user(self):
        """Show dialog to add a new user"""
        # Create a new toplevel window
        add_window = tk.Toplevel(self.root)
        add_window.title("Add User")
        add_window.geometry("400x300")
        add_window.transient(self.root)  # Set to be on top of the main window
        add_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(add_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Username field
        ttk.Label(frame, text="Username:").pack(anchor="w", pady=(0, 5))
        username_var = tk.StringVar()
        username_entry = ttk.Entry(frame, textvariable=username_var)
        username_entry.pack(fill="x", pady=(0, 10))

        # Password field
        ttk.Label(frame, text="Password:").pack(anchor="w", pady=(0, 5))
        password_var = tk.StringVar()
        password_entry = ttk.Entry(frame, textvariable=password_var, show="*")
        password_entry.pack(fill="x", pady=(0, 10))

        # Role selection
        ttk.Label(frame, text="Role:").pack(anchor="w", pady=(0, 5))
        role_var = tk.StringVar(value="Client")
        role_frame = ttk.Frame(frame)
        role_frame.pack(fill="x", pady=(0, 10))

        ttk.Radiobutton(role_frame, text="Admin", variable=role_var, value="Admin").pack(side="left", padx=(0, 10))
        ttk.Radiobutton(role_frame, text="Client", variable=role_var, value="Client").pack(side="left")

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x", pady=(10, 0))

        def save_user():
            # Validate inputs
            username = username_var.get().strip()
            password = password_var.get().strip()
            role = role_var.get()

            if not username or not password:
                messagebox.showerror("Error", "Username and password are required", parent=add_window)
                return

            try:
                # Add user to database
                self.db.add_user(username, password, role)

                # Close window and refresh user list
                add_window.destroy()
                self.load_users()

                # Show success message
                messagebox.showinfo("Success", f"User '{username}' added successfully")

            except Exception as e:
                messagebox.showerror("Error", str(e), parent=add_window)

        ttk.Button(button_frame, text="Save", command=save_user, bootstyle=SUCCESS).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=add_window.destroy, bootstyle=DANGER).pack(side="left")

        # Focus the username entry
        username_entry.focus()

    def delete_user(self):
        """Delete the selected user"""
        # Get selected item
        selected = self.user_tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a user to delete")
            return

        # Get user ID and username
        user_id = self.user_tree.item(selected[0], "values")[0]
        username = self.user_tree.item(selected[0], "values")[1]

        # Confirm deletion
        if not messagebox.askyesno("Confirm", f"Are you sure you want to delete user '{username}'?\n\nThis action cannot be undone."):
            return

        try:
            # Delete user from database
            self.db.delete_user(user_id)

            # Refresh user list
            self.load_users()

            # Show success message
            messagebox.showinfo("Success", f"User '{username}' deleted successfully")

        except Exception as e:
            messagebox.showerror("Error", str(e))

    def load_companies(self):
        """Load companies into the treeview"""
        # Clear existing items
        for item in self.company_tree.get_children():
            self.company_tree.delete(item)

        # Get all company databases
        import os
        import sqlite3

        # Get all .db files in the current directory, excluding system databases
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        # Process each database file
        for db_file in db_files:
            try:
                # Extract company name from filename
                company_name = db_file.replace('.db', '').replace('_', ' ').title()

                # Connect to the database
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Get owner (placeholder - not stored in database yet)
                owner = "Unknown"

                # Get transaction count
                cursor.execute("SELECT COUNT(*) FROM transactions")
                transaction_count = cursor.fetchone()[0]

                # Get total balance
                cursor.execute("SELECT SUM(current_balance) FROM accounts")
                balance = cursor.fetchone()[0] or 0.0

                # Add to treeview
                self.company_tree.insert("", "end", values=(company_name, owner, transaction_count, f"${balance:.2f}"))

                conn.close()
            except Exception as e:
                print(f"Error loading company {db_file}: {e}")

    def view_company_details(self):
        """View details of the selected company"""
        # Get selected item
        selected = self.company_tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a company to view")
            return

        # Get company name
        company_name = self.company_tree.item(selected[0], "values")[0]

        # Create a new toplevel window
        details_window = tk.Toplevel(self.root)
        details_window.title(f"Company Details: {company_name}")
        details_window.geometry("600x400")
        details_window.transient(self.root)  # Set to be on top of the main window
        details_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(details_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Company name header
        ttk.Label(frame, text=company_name, font=("Segoe UI", 16, "bold")).pack(anchor="w", pady=(0, 20))

        # Connect to the database
        db_file = f"{company_name.lower().replace(' ', '_')}.db"

        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # Get company information
            cursor.execute("SELECT key, value FROM metadata")
            metadata = dict(cursor.fetchall())

            # Get account information
            cursor.execute("SELECT COUNT(*), SUM(current_balance) FROM accounts")
            account_count, total_balance = cursor.fetchone()

            # Get transaction information
            cursor.execute("SELECT COUNT(*) FROM transactions")
            transaction_count = cursor.fetchone()[0]

            # Get category information
            cursor.execute("SELECT COUNT(*) FROM categories")
            category_count = cursor.fetchone()[0]

            # Display information
            info_frame = ttk.LabelFrame(frame, text="Company Information", padding=10)
            info_frame.pack(fill="x", pady=(0, 10))

            # Created date
            created_date = metadata.get("created_date", "Unknown")
            ttk.Label(info_frame, text=f"Created: {created_date}").pack(anchor="w")

            # Statistics
            stats_frame = ttk.LabelFrame(frame, text="Statistics", padding=10)
            stats_frame.pack(fill="x")

            ttk.Label(stats_frame, text=f"Accounts: {account_count}").pack(anchor="w")
            ttk.Label(stats_frame, text=f"Total Balance: ${total_balance or 0:.2f}").pack(anchor="w")
            ttk.Label(stats_frame, text=f"Transactions: {transaction_count}").pack(anchor="w")
            ttk.Label(stats_frame, text=f"Categories: {category_count}").pack(anchor="w")

            conn.close()

        except Exception as e:
            ttk.Label(frame, text=f"Error loading company details: {e}", foreground="red").pack(pady=20)

        # Close button
        ttk.Button(frame, text="Close", command=details_window.destroy, bootstyle=DANGER).pack(pady=20)


class ClientDashboard:
    def __init__(self, root, username, logout_callback, create_company_callback, open_company_callback):
        self.root = root
        self.username = username
        self.logout_callback = logout_callback
        self.create_company_callback = create_company_callback
        self.open_company_callback = open_company_callback

        self.root.title("Client Dashboard")
        self.root.geometry("1000x700")

        self.create_widgets()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with welcome message and logout button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        welcome_label = ttk.Label(header_frame, text=f"Welcome, {self.username}", font=("Segoe UI", 16, "bold"))
        welcome_label.pack(side="left")

        logout_button = ttk.Button(header_frame, text="Logout", command=self.logout_callback, bootstyle=DANGER)
        logout_button.pack(side="right")

        # Companies frame
        companies_frame = ttk.LabelFrame(main_frame, text="Your Companies")
        companies_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Companies treeview
        columns = ("name", "transactions", "balance", "last_modified")
        self.company_tree = ttk.Treeview(companies_frame, columns=columns, show="headings")

        # Define headings
        self.company_tree.heading("name", text="Company Name")
        self.company_tree.heading("transactions", text="Transactions")
        self.company_tree.heading("balance", text="Balance")
        self.company_tree.heading("last_modified", text="Last Modified")

        # Define columns
        self.company_tree.column("name", width=200)
        self.company_tree.column("transactions", width=100)
        self.company_tree.column("balance", width=100)
        self.company_tree.column("last_modified", width=150)

        self.company_tree.pack(fill="both", expand=True)

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x", pady=10)

        create_button = ttk.Button(buttons_frame, text="Create New Company", command=self.create_company, bootstyle=SUCCESS)
        create_button.pack(side="left", padx=5)

        open_button = ttk.Button(buttons_frame, text="Open Selected Company", command=self.open_company, bootstyle=INFO)
        open_button.pack(side="left", padx=5)

    def create_company(self):
        self.create_company_callback()

    def open_company(self):
        selected = self.company_tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a company to open")
            return

        company_name = self.company_tree.item(selected[0])["values"][0]
        self.open_company_callback(company_name)