import os
import sqlite3
import tkinter as tk
import unittest
from unittest.mock import MagicMock

# Import modules to test
from model.database import Database
from model.transaction import TransactionManager
from view.company import CompanyWindow
from view.dashboard import AdminDashboard, ClientDashboard


class TestIntegration(unittest.TestCase):
    def setUp(self):
        # Create a root window for testing
        self.root = tk.Tk()

        # Create a test database
        self.db = Database()
        self.db.users_db = "test_users.db"
        self.db.init_users_db()

        # Create a test company
        self.company_name = "Test Company"
        self.db_path = self.db.init_company_db(self.company_name)

        # Create a transaction manager
        self.transaction_manager = TransactionManager(self.db_path)

        # Add some test transactions
        self.transaction_manager.add_transaction(
            "2025-01-01", 100.00, 1, "Test Income", 1, "income", 0
        )
        self.transaction_manager.add_transaction(
            "2025-01-02", 50.00, 1, "Test Expense", 2, "expense", 0
        )

    def tearDown(self):
        # Destroy the root window
        self.root.destroy()

        # Clean up test databases
        if os.path.exists("test_users.db"):
            os.remove("test_users.db")
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def test_admin_dashboard_with_database(self):
        """Test the integration of AdminDashboard with Database"""
        # Create a mock logout callback
        logout_callback = MagicMock()

        # Create the dashboard
        dashboard = AdminDashboard(self.root, "admin", logout_callback, self.db)

        # Check that the user tree has items (at least the admin user)
        self.assertGreater(len(dashboard.user_tree.get_children()), 0)

        # Check that the company tree has items
        self.assertGreater(len(dashboard.company_tree.get_children()), 0)

        # Add a new user
        user_count_before = len(dashboard.user_tree.get_children())

        # Simulate adding a user through the database
        self.db.add_user("testuser", "testpass", "Client")

        # Reload users
        dashboard.load_users()

        # Check that a new user was added
        user_count_after = len(dashboard.user_tree.get_children())
        self.assertEqual(user_count_after, user_count_before + 1)

    def test_client_dashboard_with_company(self):
        """Test the integration of ClientDashboard with company creation"""
        # Create mock callbacks
        logout_callback = MagicMock()

        # Create a real create_company_callback that uses the database
        def create_company_callback():
            self.db.init_company_db("New Test Company")

        # Create a mock open_company_callback
        open_company_callback = MagicMock()

        # Create the dashboard
        dashboard = ClientDashboard(
            self.root,
            "testclient",
            logout_callback,
            create_company_callback,
            open_company_callback
        )

        # Get the initial company count
        company_count_before = len(dashboard.company_tree.get_children())

        # Create a new company
        create_company_callback()

        # Check that the open_company_callback was not called yet
        open_company_callback.assert_not_called()

        # Check that the new company exists
        self.assertTrue(os.path.exists("new_test_company.db"))

        # Clean up the new company database
        if os.path.exists("new_test_company.db"):
            os.remove("new_test_company.db")

    def test_company_window_with_transaction_manager(self):
        """Test the integration of CompanyWindow with TransactionManager"""
        # Create a mock close callback
        close_callback = MagicMock()

        # Create the company window
        company_window = CompanyWindow(
            self.root,
            self.company_name,
            self.transaction_manager,
            close_callback
        )

        # Check that the transaction tree has items
        self.assertEqual(len(company_window.tree.get_children()), 2)

        # Check that the summary information is correct
        summary = self.transaction_manager.get_balance_summary()
        self.assertEqual(company_window.income_label.cget("text"), f"${summary['income']:.2f}")
        self.assertEqual(company_window.expenses_label.cget("text"), f"${summary['expenses']:.2f}")
        self.assertEqual(company_window.balance_label.cget("text"), f"${summary['balance']:.2f}")

        # Test the search functionality
        company_window.search_var.set("income")
        company_window.filter_transactions()
        self.assertEqual(len(company_window.tree.get_children()), 1)

        # Clear the search
        company_window.search_var.set("")
        company_window.filter_transactions()
        self.assertEqual(len(company_window.tree.get_children()), 2)


if __name__ == '__main__':
    unittest.main()