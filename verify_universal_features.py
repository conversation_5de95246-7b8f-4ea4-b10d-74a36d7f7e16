#!/usr/bin/env python3
"""
Universal Feature Verification Script
Verifies that ALL companies have ALL features and tests new company creation
"""

import os
import sys
import tempfile
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from migrate_all_companies import (get_all_company_databases,
                                   verify_all_features)
from model.database import Database


def test_existing_companies():
    """Test that all existing companies have all features"""
    print("🔍 TESTING EXISTING COMPANIES")
    print("=" * 50)
    
    db_files = get_all_company_databases()
    
    if not db_files:
        print("✅ No existing companies found")
        return True
    
    all_good = True
    
    for db_file in db_files:
        company_name = db_file.replace('.db', '').replace('_', ' ').title()
        print(f"\n📋 Testing {company_name} ({db_file})...")
        
        results = verify_all_features(db_file)
        company_good = True
        
        for feature_name, (available, missing) in results.items():
            if not available:
                print(f"   ❌ {feature_name}: Missing {', '.join(missing) if missing else 'features'}")
                company_good = False
                all_good = False
            else:
                print(f"   ✅ {feature_name}: Available")
        
        if company_good:
            print(f"   🎉 {company_name} has ALL features!")
        else:
            print(f"   ⚠️  {company_name} is missing some features")
    
    return all_good


def test_new_company_creation():
    """Test that new companies get all features"""
    print("\n🆕 TESTING NEW COMPANY CREATION")
    print("=" * 50)
    
    try:
        # Create a test company
        db = Database()
        test_company_name = f"Test Company {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_db_path = db.init_company_db(test_company_name)
        
        print(f"✅ Created test company: {test_company_name}")
        print(f"   Database: {test_db_path}")
        
        # Verify it has all features
        print("\n🔍 Verifying features in new company...")
        results = verify_all_features(test_db_path)
        
        all_good = True
        for feature_name, (available, missing) in results.items():
            if not available:
                print(f"   ❌ {feature_name}: Missing {', '.join(missing) if missing else 'features'}")
                all_good = False
            else:
                print(f"   ✅ {feature_name}: Available")
        
        # Clean up test database
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print(f"\n🧹 Cleaned up test database: {test_db_path}")
        
        if all_good:
            print("🎉 New company has ALL features!")
        else:
            print("⚠️  New company is missing some features")
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error testing new company creation: {e}")
        return False


def test_feature_integration():
    """Test that features are properly integrated"""
    print("\n🔧 TESTING FEATURE INTEGRATION")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing module imports...")
        
        from model.split_transaction import SplitTransaction
        print("   ✅ Split Transaction model")
        
        from view.split_transaction_dialog import SplitTransactionDialog
        print("   ✅ Split Transaction dialog")
        
        from view.split_transaction_frame import SplitTransactionFrame
        print("   ✅ Split Transaction frame")
        
        from model.bank_reconciliation import BankReconciliationSession
        print("   ✅ Bank Reconciliation model")

        from utils.database_migration import DatabaseMigration
        print("   ✅ Database Migration")

        print("\n🎯 Testing feature functionality...")

        # Create database with migration
        db = Database()
        test_db_path = db.init_company_db("Integration Test")

        # Test split transaction creation
        split_model = SplitTransaction(test_db_path)
        print("   ✅ Split Transaction model initialization")

        # Test bank reconciliation
        recon_model = BankReconciliationSession(test_db_path)
        print("   ✅ Bank Reconciliation model initialization")
        
        # Clean up
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        print("🎉 All feature integrations working!")
        return True
        
    except Exception as e:
        print(f"❌ Feature integration error: {e}")
        return False


def main():
    """Main verification function"""
    print("=" * 70)
    print("UNIVERSAL FEATURE VERIFICATION")
    print("=" * 70)
    print("This script verifies that:")
    print("• ALL existing companies have ALL features")
    print("• NEW companies automatically get ALL features")
    print("• Feature integration is working correctly")
    print()
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("❌ Error: Please run this script from the Cashbook application directory")
        print("   (The directory containing main.py)")
        return
    
    # Run tests
    test_results = []
    
    # Test 1: Existing companies
    existing_companies_ok = test_existing_companies()
    test_results.append(("Existing Companies", existing_companies_ok))
    
    # Test 2: New company creation
    new_company_ok = test_new_company_creation()
    test_results.append(("New Company Creation", new_company_ok))
    
    # Test 3: Feature integration
    integration_ok = test_feature_integration()
    test_results.append(("Feature Integration", integration_ok))
    
    # Summary
    print("\n" + "=" * 70)
    print("VERIFICATION SUMMARY")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print()
        print("✅ UNIVERSAL FEATURE AVAILABILITY CONFIRMED:")
        print("   • Split Transactions work in ALL companies")
        print("   • Bank Reconciliation works in ALL companies")
        print("   • Bank Import works in ALL companies")
        print("   • Multi-currency works in ALL companies")
        print("   • New companies automatically get ALL features")
        print("   • Feature integration is working correctly")
        print()
        print("🚀 The Cashbook application is ready for use!")
        print("   All features are universally available across all companies.")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("   Please review the error messages above and fix any issues.")
        print("   You may need to run the migration script:")
        print("   python migrate_all_companies.py")
    
    print()
    input("Press Enter to exit...")


if __name__ == "__main__":
    main()
