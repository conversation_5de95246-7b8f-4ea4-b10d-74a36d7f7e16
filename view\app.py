import tkinter as tk
from tkinter import ttk


class MainApplication(tk.Tk):
    def __init__(self):
        super().__init__()

        self.title("Cashbook Application")
        self.geometry("800x600")
        self.resizable(False, False)

        self.set_application_icon()

        # Create main menu
        self.create_main_menu()

        # Create sidebar
        self.create_sidebar()

        # Create main content area
        self.main_content = ttk.Frame(self, padding=10)
        self.main_content.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Default page
        self.switch_page("Dashboard")

    def set_application_icon(self):
        try:
            self.iconbitmap("resources/logo.png")
        except Exception as e:
            print(f"Error setting application icon: {e}")

    def create_main_menu(self):
        menu_bar = tk.Menu(self)

        # File menu
        file_menu = tk.Menu(menu_bar, tearoff=0)
        file_menu.add_command(label="New", command=self.new_file)
        file_menu.add_command(label="Open", command=self.open_file)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.quit)
        menu_bar.add_cascade(label="File", menu=file_menu)

        # Help menu
        help_menu = tk.Menu(menu_bar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menu_bar.add_cascade(label="Help", menu=help_menu)

        self.config(menu=menu_bar)

    def create_sidebar(self):
        sidebar = ttk.Frame(self, width=200, padding=10)
        sidebar.pack(side=tk.LEFT, fill=tk.Y)

        ttk.Button(sidebar, text="Dashboard", command=lambda: self.switch_page("Dashboard")).pack(fill=tk.X, pady=5)
        ttk.Button(sidebar, text="Transactions", command=lambda: self.switch_page("Transactions")).pack(fill=tk.X, pady=5)
        ttk.Button(sidebar, text="Accounts", command=lambda: self.switch_page("Accounts")).pack(fill=tk.X, pady=5)
        ttk.Button(sidebar, text="Reports", command=lambda: self.switch_page("Reports")).pack(fill=tk.X, pady=5)
        ttk.Button(sidebar, text="Settings", command=lambda: self.switch_page("Settings")).pack(fill=tk.X, pady=5)

    def switch_page(self, page_name):
        for widget in self.main_content.winfo_children():
            widget.destroy()

        if page_name == "Dashboard":
            label = ttk.Label(self.main_content, text="Dashboard Page", font=("Segoe UI", 16))
            label.pack(pady=20)
        elif page_name == "Transactions":
            label = ttk.Label(self.main_content, text="Transactions Page", font=("Segoe UI", 16))
            label.pack(pady=20)
        elif page_name == "Accounts":
            label = ttk.Label(self.main_content, text="Accounts Page", font=("Segoe UI", 16))
            label.pack(pady=20)
        elif page_name == "Reports":
            label = ttk.Label(self.main_content, text="Reports Page", font=("Segoe UI", 16))
            label.pack(pady=20)
        elif page_name == "Settings":
            label = ttk.Label(self.main_content, text="Settings Page", font=("Segoe UI", 16))
            label.pack(pady=20)

    def new_file(self):
        print("New file created")

    def open_file(self):
        print("File opened")

    def show_about(self):
        about_text = """Cashbook Application v1.0

Developed by: Akash Hasendra
Company: LocalSEO.lk
Website: https://localseo.lk

© 2025 All Rights Reserved"""
        tk.messagebox.showinfo("About Cashbook", about_text)

if __name__ == "__main__":
    app = MainApplication()
    app.mainloop()