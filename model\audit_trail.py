import sqlite3
from datetime import datetime
from model.base_model import BaseModel


class AuditTrail(BaseModel):
    """Model for managing audit trail and transaction tagging"""

    def __init__(self, db_path):
        super().__init__(db_path)
        self._table_name = "audit_trail"
        self._create_tables()

    def table_name(self):
        return self._table_name

    def fields(self):
        return [
            'id', 'table_name', 'record_id', 'action', 'old_values', 'new_values',
            'user_id', 'timestamp', 'ip_address', 'session_id', 'tags'
        ]

    def primary_key(self):
        return 'id'

    def _create_tables(self):
        """Create audit trail and transaction tags tables"""
        # Create audit trail table
        audit_trail_query = '''
        CREATE TABLE IF NOT EXISTS audit_trail (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_name TEXT NOT NULL,
            record_id INTEGER NOT NULL,
            action TEXT NOT NULL,
            old_values TEXT,
            new_values TEXT,
            user_id TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address TEXT,
            session_id TEXT,
            tags TEXT,
            description TEXT
        )
        '''
        self.execute_query(audit_trail_query)

        # Create transaction tags table
        transaction_tags_query = '''
        CREATE TABLE IF NOT EXISTS transaction_tags (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id INTEGER,
            journal_entry_id INTEGER,
            tag_name TEXT NOT NULL,
            tag_value TEXT,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE CASCADE,
            FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE
        )
        '''
        self.execute_query(transaction_tags_query)

        # Create indexes for better performance
        index_queries = [
            'CREATE INDEX IF NOT EXISTS idx_audit_trail_table_record ON audit_trail (table_name, record_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail (timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_transaction_tags_transaction ON transaction_tags (transaction_id)',
            'CREATE INDEX IF NOT EXISTS idx_transaction_tags_journal ON transaction_tags (journal_entry_id)',
            'CREATE INDEX IF NOT EXISTS idx_transaction_tags_name ON transaction_tags (tag_name)'
        ]
        
        for query in index_queries:
            self.execute_query(query)

    def log_action(self, table_name, record_id, action, old_values=None, new_values=None, 
                   user_id=None, description=None, tags=None):
        """
        Log an action to the audit trail
        
        Args:
            table_name (str): Name of the table affected
            record_id (int): ID of the record affected
            action (str): Action performed (INSERT, UPDATE, DELETE)
            old_values (dict): Old values before change
            new_values (dict): New values after change
            user_id (str): User who performed the action
            description (str): Description of the action
            tags (str): Comma-separated tags
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Convert dictionaries to JSON strings
            old_values_json = str(old_values) if old_values else None
            new_values_json = str(new_values) if new_values else None
            
            cursor.execute('''
                INSERT INTO audit_trail 
                (table_name, record_id, action, old_values, new_values, 
                 user_id, timestamp, description, tags)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (table_name, record_id, action, old_values_json, new_values_json,
                  user_id, datetime.now(), description, tags))
            
            conn.commit()
            return cursor.lastrowid
            
        except sqlite3.Error as e:
            print(f"Error logging audit action: {e}")
            return None
        finally:
            conn.close()

    def add_transaction_tag(self, transaction_id=None, journal_entry_id=None, 
                           tag_name=None, tag_value=None, created_by=None):
        """
        Add a tag to a transaction or journal entry
        
        Args:
            transaction_id (int): ID of the transaction (for legacy transactions)
            journal_entry_id (int): ID of the journal entry (for new multi-line entries)
            tag_name (str): Name of the tag
            tag_value (str): Value of the tag
            created_by (str): User who created the tag
        """
        if not transaction_id and not journal_entry_id:
            raise ValueError("Either transaction_id or journal_entry_id must be provided")
        
        if not tag_name:
            raise ValueError("Tag name is required")
        
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO transaction_tags 
                (transaction_id, journal_entry_id, tag_name, tag_value, created_by)
                VALUES (?, ?, ?, ?, ?)
            ''', (transaction_id, journal_entry_id, tag_name, tag_value, created_by))
            
            conn.commit()
            
            # Log the tagging action
            record_id = transaction_id or journal_entry_id
            table_name = "transactions" if transaction_id else "journal_entries"
            self.log_action(
                table_name=table_name,
                record_id=record_id,
                action="TAG_ADDED",
                new_values={"tag_name": tag_name, "tag_value": tag_value},
                user_id=created_by,
                description=f"Added tag '{tag_name}' with value '{tag_value}'"
            )
            
            return cursor.lastrowid
            
        except sqlite3.Error as e:
            print(f"Error adding transaction tag: {e}")
            return None
        finally:
            conn.close()

    def get_transaction_tags(self, transaction_id=None, journal_entry_id=None):
        """Get all tags for a transaction or journal entry"""
        if not transaction_id and not journal_entry_id:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            if transaction_id:
                cursor.execute('''
                    SELECT id, tag_name, tag_value, created_by, created_at
                    FROM transaction_tags 
                    WHERE transaction_id = ?
                    ORDER BY created_at DESC
                ''', (transaction_id,))
            else:
                cursor.execute('''
                    SELECT id, tag_name, tag_value, created_by, created_at
                    FROM transaction_tags 
                    WHERE journal_entry_id = ?
                    ORDER BY created_at DESC
                ''', (journal_entry_id,))
            
            tags = []
            for row in cursor.fetchall():
                tags.append({
                    'id': row[0],
                    'tag_name': row[1],
                    'tag_value': row[2],
                    'created_by': row[3],
                    'created_at': row[4]
                })
            
            return tags
            
        except sqlite3.Error as e:
            print(f"Error getting transaction tags: {e}")
            return []
        finally:
            conn.close()

    def remove_transaction_tag(self, tag_id, user_id=None):
        """Remove a transaction tag"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get tag details before deletion for audit
            cursor.execute('SELECT tag_name, tag_value FROM transaction_tags WHERE id = ?', (tag_id,))
            tag_info = cursor.fetchone()
            
            if tag_info:
                # Delete the tag
                cursor.execute('DELETE FROM transaction_tags WHERE id = ?', (tag_id,))
                
                # Log the removal
                self.log_action(
                    table_name="transaction_tags",
                    record_id=tag_id,
                    action="DELETE",
                    old_values={"tag_name": tag_info[0], "tag_value": tag_info[1]},
                    user_id=user_id,
                    description=f"Removed tag '{tag_info[0]}'"
                )
                
                conn.commit()
                return True
            
            return False
            
        except sqlite3.Error as e:
            print(f"Error removing transaction tag: {e}")
            return False
        finally:
            conn.close()

    def get_audit_trail(self, table_name=None, record_id=None, start_date=None, end_date=None, 
                       user_id=None, action=None, limit=100):
        """
        Get audit trail records with optional filtering
        
        Args:
            table_name (str): Filter by table name
            record_id (int): Filter by record ID
            start_date (str): Start date for filtering (YYYY-MM-DD)
            end_date (str): End date for filtering (YYYY-MM-DD)
            user_id (str): Filter by user ID
            action (str): Filter by action type
            limit (int): Maximum number of records to return
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            query = '''
                SELECT id, table_name, record_id, action, old_values, new_values,
                       user_id, timestamp, description, tags
                FROM audit_trail 
                WHERE 1=1
            '''
            params = []
            
            if table_name:
                query += " AND table_name = ?"
                params.append(table_name)
            
            if record_id:
                query += " AND record_id = ?"
                params.append(record_id)
            
            if start_date:
                query += " AND DATE(timestamp) >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND DATE(timestamp) <= ?"
                params.append(end_date)
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if action:
                query += " AND action = ?"
                params.append(action)
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            
            audit_records = []
            for row in cursor.fetchall():
                audit_records.append({
                    'id': row[0],
                    'table_name': row[1],
                    'record_id': row[2],
                    'action': row[3],
                    'old_values': row[4],
                    'new_values': row[5],
                    'user_id': row[6],
                    'timestamp': row[7],
                    'description': row[8],
                    'tags': row[9]
                })
            
            return audit_records
            
        except sqlite3.Error as e:
            print(f"Error getting audit trail: {e}")
            return []
        finally:
            conn.close()

    def search_by_tags(self, tag_name=None, tag_value=None):
        """Search transactions and journal entries by tags"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            query = '''
                SELECT DISTINCT 
                    tt.transaction_id,
                    tt.journal_entry_id,
                    tt.tag_name,
                    tt.tag_value,
                    CASE 
                        WHEN tt.transaction_id IS NOT NULL THEN 'transaction'
                        ELSE 'journal_entry'
                    END as record_type
                FROM transaction_tags tt
                WHERE 1=1
            '''
            params = []
            
            if tag_name:
                query += " AND tt.tag_name = ?"
                params.append(tag_name)
            
            if tag_value:
                query += " AND tt.tag_value LIKE ?"
                params.append(f"%{tag_value}%")
            
            query += " ORDER BY tt.created_at DESC"
            
            cursor.execute(query, params)
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'transaction_id': row[0],
                    'journal_entry_id': row[1],
                    'tag_name': row[2],
                    'tag_value': row[3],
                    'record_type': row[4]
                })
            
            return results
            
        except sqlite3.Error as e:
            print(f"Error searching by tags: {e}")
            return []
        finally:
            conn.close()

    def get_popular_tags(self, limit=20):
        """Get the most frequently used tags"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT tag_name, COUNT(*) as usage_count
                FROM transaction_tags
                GROUP BY tag_name
                ORDER BY usage_count DESC
                LIMIT ?
            ''', (limit,))
            
            tags = []
            for row in cursor.fetchall():
                tags.append({
                    'tag_name': row[0],
                    'usage_count': row[1]
                })
            
            return tags
            
        except sqlite3.Error as e:
            print(f"Error getting popular tags: {e}")
            return []
        finally:
            conn.close()

    def cleanup_old_audit_records(self, days_to_keep=365):
        """Clean up old audit trail records"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            cursor.execute('''
                DELETE FROM audit_trail 
                WHERE timestamp < ?
            ''', (cutoff_date,))
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            return deleted_count
            
        except sqlite3.Error as e:
            print(f"Error cleaning up audit records: {e}")
            return 0
        finally:
            conn.close()
