import sqlite3
from datetime import datetime

from model.base_model import BaseModel
from model.invoice import Invoice


class InvoicePayment(BaseModel):
    """Model for managing invoice payments"""

    def __init__(self, db_path):
        """Initialize the InvoicePayment model with database path"""
        super().__init__(db_path)
        self._table_name = "invoice_payments"
        self._create_tables()
        self.invoice_model = Invoice(db_path)

    def table_name(self):
        """Return the table name for this model"""
        return self._table_name

    def fields(self):
        """Return a list of field names for this model"""
        return [
            'id', 'invoice_id', 'amount', 'payment_date', 'payment_method',
            'notes', 'transaction_id', 'created_at', 'updated_at'
        ]

    def primary_key(self):
        """Return the primary key field name"""
        return 'id'

    def _create_tables(self):
        """Create the invoice_payments table if it doesn't exist"""
        query = '''
        CREATE TABLE IF NOT EXISTS invoice_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            payment_date TEXT NOT NULL,
            payment_method TEXT NOT NULL,
            notes TEXT,
            transaction_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id),
            FOREIGN KEY (transaction_id) REFERENCES transactions (id)
        )
        '''
        self.execute_query(query)

    def add_payment(self, payment_data, transaction_id=None):
        """Add a new payment for an invoice

        Args:
            payment_data (dict): Dictionary containing payment information
                - invoice_id: ID of the invoice (required)
                - amount: Payment amount (required)
                - payment_date: Date of payment (required)
                - payment_method: Method of payment (required)
                - notes: Additional notes about the payment
            transaction_id (int, optional): ID of the transaction linked to this payment

        Returns:
            int: ID of the newly created payment
        """
        # Validate required fields
        if not payment_data.get('invoice_id'):
            raise ValueError("Invoice ID is required")
        if not payment_data.get('amount'):
            raise ValueError("Payment amount is required")
        if not payment_data.get('payment_date'):
            raise ValueError("Payment date is required")
        if not payment_data.get('payment_method'):
            raise ValueError("Payment method is required")

        # Get invoice details
        invoice = self.invoice_model.get_by_id(payment_data['invoice_id'])
        if not invoice:
            raise ValueError(f"Invoice with ID {payment_data['invoice_id']} not found")

        # Check if payment amount is valid
        if payment_data['amount'] <= 0:
            raise ValueError("Payment amount must be greater than zero")

        # Get total amount already paid
        total_paid = self.get_total_paid(payment_data['invoice_id'])
        remaining = invoice['total_amount'] - total_paid

        # Check if payment amount exceeds remaining balance
        if payment_data['amount'] > remaining:
            raise ValueError(f"Payment amount (${payment_data['amount']:.2f}) exceeds remaining balance (${remaining:.2f})")

        # Start a transaction
        conn = self.get_connection()
        conn.execute("BEGIN TRANSACTION")

        try:
            # Prepare data for insertion
            data = {
                'invoice_id': payment_data['invoice_id'],
                'amount': payment_data['amount'],
                'payment_date': payment_data['payment_date'],
                'payment_method': payment_data['payment_method'],
                'notes': payment_data.get('notes', ''),
                'transaction_id': transaction_id,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }

            # Insert payment
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data.keys()])
            values = list(data.values())

            query = f'''
            INSERT INTO {self.table_name()} ({columns})
            VALUES ({placeholders})
            '''

            cursor = conn.cursor()
            cursor.execute(query, values)
            payment_id = cursor.lastrowid

            # Update invoice status based on payment
            new_total_paid = total_paid + payment_data['amount']
            if new_total_paid >= invoice['total_amount']:
                # Invoice is fully paid
                self.invoice_model.update_invoice_status(payment_data['invoice_id'], Invoice.STATUS_PAID)
            elif new_total_paid > 0:
                # Invoice is partially paid (still marked as sent)
                if invoice['status'] == Invoice.STATUS_OVERDUE:
                    # If it was overdue, keep it as overdue
                    pass
                else:
                    # Otherwise, mark as sent
                    self.invoice_model.update_invoice_status(payment_data['invoice_id'], Invoice.STATUS_SENT)

            conn.commit()
            return payment_id
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    def get_payments_for_invoice(self, invoice_id):
        """Get all payments for an invoice

        Args:
            invoice_id (int): ID of the invoice

        Returns:
            list: List of dictionaries containing payment information
        """
        query = f'''
        SELECT p.*, t.date as transaction_date, t.description as transaction_description,
               a.name as account_name
        FROM {self.table_name()} p
        LEFT JOIN transactions t ON p.transaction_id = t.id
        LEFT JOIN accounts a ON t.account_id = a.id
        WHERE p.invoice_id = ?
        ORDER BY p.payment_date DESC
        '''

        return self.execute_query(query, [invoice_id])

    def get_total_paid(self, invoice_id):
        """Get the total amount paid for an invoice

        Args:
            invoice_id (int): ID of the invoice

        Returns:
            float: Total amount paid
        """
        query = f'''
        SELECT SUM(amount) as total_paid
        FROM {self.table_name()}
        WHERE invoice_id = ?
        '''

        result = self.execute_query(query, [invoice_id])
        if result and result[0]['total_paid'] is not None:
            return result[0]['total_paid']
        return 0.0

    def get_remaining_balance(self, invoice_id):
        """Get the remaining balance for an invoice

        Args:
            invoice_id (int): ID of the invoice

        Returns:
            float: Remaining balance
        """
        # Get invoice details
        invoice = self.invoice_model.get_by_id(invoice_id)
        if not invoice:
            raise ValueError(f"Invoice with ID {invoice_id} not found")

        # Get total amount paid
        total_paid = self.get_total_paid(invoice_id)

        # Calculate remaining balance
        return invoice['total_amount'] - total_paid

    def delete_payment(self, payment_id):
        """Delete a payment and update invoice status

        Args:
            payment_id (int): ID of the payment to delete

        Returns:
            bool: True if the deletion was successful, False otherwise
        """
        # Get payment details
        payment = self.get_by_id(payment_id)
        if not payment:
            return False

        # Start a transaction
        conn = self.get_connection()
        conn.execute("BEGIN TRANSACTION")

        try:
            # Delete payment
            query = f'''
            DELETE FROM {self.table_name()}
            WHERE id = ?
            '''
            cursor = conn.cursor()
            cursor.execute(query, [payment_id])

            # Update invoice status based on remaining payments
            invoice_id = payment['invoice_id']
            total_paid = self.get_total_paid(invoice_id)
            invoice = self.invoice_model.get_by_id(invoice_id)

            if total_paid <= 0:
                # No payments left, revert to sent status
                self.invoice_model.update_invoice_status(invoice_id, Invoice.STATUS_SENT)
            elif total_paid < invoice['total_amount']:
                # Partial payment, keep as sent
                self.invoice_model.update_invoice_status(invoice_id, Invoice.STATUS_SENT)

            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
