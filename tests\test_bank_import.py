import csv
import os
import sqlite3
import tempfile
import unittest
from datetime import datetime

from model.bank_format_template import BankFormatTemplate
from model.transaction import TransactionManager
from utils.bank_statement_importer import BankStatementImporter
from utils.duplicate_detector import DuplicateDetector


class TestBankImport(unittest.TestCase):
    """Test cases for bank statement import functionality"""
    
    def setUp(self):
        """Set up test database and sample data"""
        # Create temporary database
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
        
        # Initialize database
        self.init_test_database()
        
        # Create test components
        self.template_manager = BankFormatTemplate(self.db_path)
        self.importer = BankStatementImporter(self.db_path)
        self.duplicate_detector = DuplicateDetector(self.db_path)
        self.transaction_manager = TransactionManager(self.db_path)
        
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def init_test_database(self):
        """Initialize test database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create accounts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL
            )
        """)
        
        # Create categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        # Create transactions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                import_hash TEXT,
                bank_reference TEXT,
                import_source TEXT,
                import_date TEXT,
                currency_code TEXT DEFAULT 'USD',
                exchange_rate REAL DEFAULT 1.0,
                base_amount REAL,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Create bank format templates table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_format_templates (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                bank_name TEXT NOT NULL,
                file_type TEXT NOT NULL,
                field_mapping TEXT NOT NULL,
                date_format TEXT,
                delimiter TEXT,
                has_header INTEGER DEFAULT 1,
                skip_lines INTEGER DEFAULT 0,
                created_date TEXT NOT NULL,
                is_active INTEGER DEFAULT 1
            )
        """)
        
        # Create bank statement imports table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_statement_imports (
                id INTEGER PRIMARY KEY,
                filename TEXT NOT NULL,
                account_id INTEGER NOT NULL,
                import_date TEXT NOT NULL,
                total_transactions INTEGER DEFAULT 0,
                successful_imports INTEGER DEFAULT 0,
                duplicates_found INTEGER DEFAULT 0,
                errors_count INTEGER DEFAULT 0,
                template_id INTEGER,
                status TEXT DEFAULT 'completed',
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (template_id) REFERENCES bank_format_templates(id)
            )
        """)
        
        # Create import rules table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS import_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                pattern TEXT NOT NULL,
                category_id INTEGER,
                is_regex INTEGER DEFAULT 0,
                is_case_sensitive INTEGER DEFAULT 0,
                priority INTEGER DEFAULT 0,
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Insert test data
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?)",
            ("Test Checking", "checking", "USD", 1000.00, 1000.00)
        )
        
        cursor.execute(
            "INSERT INTO categories (name, type) VALUES (?, ?)",
            ("Groceries", "expense")
        )
        
        conn.commit()
        conn.close()
    
    def test_bank_template_creation(self):
        """Test creating bank format templates"""
        template_id = self.template_manager.create_template(
            name="Test Bank CSV",
            bank_name="Test Bank",
            file_type="CSV",
            field_mapping={
                "date": 0,
                "description": 1,
                "amount": 2
            },
            date_format="%d/%m/%Y",
            delimiter=",",
            has_header=True
        )
        
        self.assertIsNotNone(template_id)
        
        # Retrieve template
        template = self.template_manager.get_template_by_id(template_id)
        self.assertIsNotNone(template)
        self.assertEqual(template['name'], "Test Bank CSV")
        self.assertEqual(template['bank_name'], "Test Bank")
    
    def test_default_templates_initialization(self):
        """Test initialization of default bank templates"""
        self.template_manager.initialize_default_templates()
        
        templates = self.template_manager.get_all_templates()
        self.assertGreater(len(templates), 0)
        
        # Check for specific templates
        template_names = [t['name'] for t in templates]
        self.assertIn("ANZ CSV Standard", template_names)
        self.assertIn("ASB CSV Standard", template_names)
    
    def test_duplicate_detection(self):
        """Test duplicate transaction detection"""
        # Add a test transaction
        transaction_id = self.transaction_manager.add_transaction(
            date="2024-01-15",
            amount=50.00,
            account_id=1,
            description="Test Transaction",
            type_name="expense"
        )

        # Update the transaction hash for duplicate detection
        self.duplicate_detector.update_transaction_hash(
            transaction_id, "2024-01-15", 50.00, "Test Transaction", 1
        )

        # Test exact duplicate detection
        has_exact, exact_dups = self.duplicate_detector.check_exact_duplicate(
            "2024-01-15", 50.00, "Test Transaction", 1
        )
        self.assertTrue(has_exact)
        self.assertEqual(len(exact_dups), 1)
        
        # Test fuzzy duplicate detection
        has_fuzzy, fuzzy_dups = self.duplicate_detector.check_fuzzy_duplicates(
            "2024-01-16", 50.00, "Test Transaction Similar", 1
        )
        self.assertTrue(has_fuzzy)
        self.assertGreater(len(fuzzy_dups), 0)
    
    def test_csv_import_with_template(self):
        """Test CSV import using bank template"""
        # Create test CSV file
        test_csv = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        csv_writer = csv.writer(test_csv)
        
        # Write header
        csv_writer.writerow(["Date", "Description", "Amount", "Balance"])
        
        # Write test data
        csv_writer.writerow(["15/01/2024", "Grocery Store", "-25.50", "974.50"])
        csv_writer.writerow(["16/01/2024", "Salary Payment", "2000.00", "2974.50"])
        csv_writer.writerow(["17/01/2024", "Gas Station", "-45.00", "2929.50"])
        
        test_csv.close()
        
        try:
            # Create template
            template_id = self.template_manager.create_template(
                name="Test CSV Template",
                bank_name="Test Bank",
                file_type="CSV",
                field_mapping={
                    "date": 0,
                    "description": 1,
                    "amount": 2,
                    "balance": 3
                },
                date_format="%d/%m/%Y",
                delimiter=",",
                has_header=True
            )
            
            # Import transactions
            import_result = self.importer.import_with_duplicate_check(
                test_csv.name, 
                account_id=1, 
                template_id=template_id,
                auto_categorize=False,
                skip_duplicates=True
            )
            
            # Verify import results
            self.assertEqual(import_result['total_transactions'], 3)
            self.assertEqual(import_result['imported_count'], 3)
            self.assertEqual(import_result['duplicate_count'], 0)
            self.assertEqual(import_result['error_count'], 0)
            
        finally:
            os.unlink(test_csv.name)
    
    def test_qif_import(self):
        """Test QIF file import"""
        # Create test QIF file
        test_qif = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.qif')
        
        qif_content = """!Type:Bank
D01/15/2024
T-25.50
PGrocery Store
MWeekly groceries
^
D01/16/2024
T2000.00
PSalary Payment
MMonthly salary
^
D01/17/2024
T-45.00
PGas Station
MFuel for car
^
"""
        test_qif.write(qif_content)
        test_qif.close()
        
        try:
            # Parse QIF file
            transactions = self.importer.parse_qif_file(test_qif.name)
            
            # Verify parsed transactions
            self.assertEqual(len(transactions), 3)
            
            # Check first transaction
            first_trans = transactions[0]
            self.assertEqual(first_trans['date'], "2024-01-15")
            self.assertEqual(first_trans['amount'], -25.50)
            self.assertEqual(first_trans['type'], "expense")
            self.assertIn("Grocery Store", first_trans['description'])
            
        finally:
            os.unlink(test_qif.name)
    
    def test_transaction_hash_generation(self):
        """Test transaction hash generation for duplicate detection"""
        hash1 = self.duplicate_detector.generate_transaction_hash(
            "2024-01-15", 50.00, "Test Transaction", 1
        )
        
        hash2 = self.duplicate_detector.generate_transaction_hash(
            "2024-01-15", 50.00, "Test Transaction", 1
        )
        
        # Same transaction should generate same hash
        self.assertEqual(hash1, hash2)
        
        hash3 = self.duplicate_detector.generate_transaction_hash(
            "2024-01-15", 51.00, "Test Transaction", 1
        )
        
        # Different amount should generate different hash
        self.assertNotEqual(hash1, hash3)


if __name__ == '__main__':
    unittest.main()
