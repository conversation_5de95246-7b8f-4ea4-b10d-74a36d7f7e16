import sqlite3
import csv
import tempfile
from datetime import datetime
from model.bank_reconciliation import BankReconciliationSession
from model.bank_statement_transaction import BankStatementTransaction
from utils.transaction_matcher import TransactionMatcher
from utils.bank_statement_importer import BankStatementImporter


class BankReconciliationManager:
    """
    Comprehensive bank reconciliation manager
    Handles the complete reconciliation workflow from statement upload to completion
    """
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.reconciliation_session = BankReconciliationSession(db_path)
        self.bank_statement_transaction = BankStatementTransaction(db_path)
        self.transaction_matcher = TransactionMatcher(db_path)
        self.bank_importer = BankStatementImporter(db_path)

    def start_reconciliation(self, account_id, statement_date, statement_balance, created_by=None):
        """Start a new bank reconciliation session"""
        try:
            # Check if there's already a pending reconciliation for this account
            existing_sessions = self.reconciliation_session.get_all_reconciliation_sessions(
                account_id=account_id, status='pending'
            )
            
            if existing_sessions:
                raise Exception(f"There is already a pending reconciliation for this account. "
                              f"Please complete or cancel the existing reconciliation first.")
            
            # Create new reconciliation session
            session_id = self.reconciliation_session.create_reconciliation_session(
                account_id, statement_date, statement_balance, created_by
            )
            
            return session_id
            
        except Exception as e:
            raise Exception(f"Error starting reconciliation: {str(e)}")

    def upload_bank_statement(self, session_id, file_path, template_id=None):
        """Upload and process bank statement for reconciliation"""
        try:
            # Get session details
            session = self.reconciliation_session.get_reconciliation_session(session_id)
            if not session:
                raise Exception("Reconciliation session not found")
            
            if session['status'] != 'pending':
                raise Exception("Cannot upload statement to a non-pending reconciliation")
            
            # Parse bank statement using the importer
            if template_id:
                transactions = self.bank_importer.parse_csv_with_template(file_path, template_id)
            elif file_path.lower().endswith('.qif'):
                transactions = self.bank_importer.parse_qif_file(file_path)
            else:
                # Try to parse as generic CSV
                transactions = self._parse_generic_csv(file_path)
            
            if not transactions:
                raise Exception("No transactions found in the bank statement file")
            
            # Add transactions to the reconciliation session
            added_count = self.bank_statement_transaction.bulk_add_bank_statement_transactions(
                session_id, transactions
            )
            
            return {
                'transactions_added': added_count,
                'total_transactions': len(transactions),
                'session_id': session_id
            }
            
        except Exception as e:
            raise Exception(f"Error uploading bank statement: {str(e)}")

    def _parse_generic_csv(self, file_path):
        """Parse a generic CSV file for bank statement transactions"""
        transactions = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', newline='') as csvfile:
                # Try to detect the format
                sample = csvfile.read(1024)
                csvfile.seek(0)
                
                # Detect delimiter
                delimiter = ','
                if ';' in sample:
                    delimiter = ';'
                elif '\t' in sample:
                    delimiter = '\t'
                
                reader = csv.reader(csvfile, delimiter=delimiter)
                
                # Skip header if it exists
                first_row = next(reader, None)
                if first_row and any(keyword in str(first_row).lower() 
                                   for keyword in ['date', 'description', 'amount', 'balance']):
                    # This looks like a header, skip it
                    pass
                else:
                    # Process the first row as data
                    csvfile.seek(0)
                    reader = csv.reader(csvfile, delimiter=delimiter)
                
                for row_num, row in enumerate(reader, start=1):
                    if len(row) < 3:  # Need at least date, description, amount
                        continue
                    
                    try:
                        # Assume format: Date, Description, Amount, [Balance]
                        transaction = {
                            'date': self._parse_date(row[0]),
                            'description': row[1].strip() if len(row) > 1 else '',
                            'amount': self._parse_amount(row[2]) if len(row) > 2 else 0.0,
                            'reference': row[3].strip() if len(row) > 3 else '',
                            'transaction_type': 'debit' if self._parse_amount(row[2]) < 0 else 'credit'
                        }
                        
                        if transaction['date'] and transaction['amount'] != 0:
                            transactions.append(transaction)
                            
                    except Exception as e:
                        print(f"Error parsing row {row_num}: {str(e)}")
                        continue
        
        except Exception as e:
            raise Exception(f"Error parsing CSV file: {str(e)}")
        
        return transactions

    def _parse_date(self, date_str):
        """Parse date string in various formats"""
        if not date_str:
            return None
        
        date_str = str(date_str).strip()
        
        # Try common date formats
        formats = [
            '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', '%d-%m-%Y',
            '%d/%m/%y', '%m/%d/%y', '%y-%m-%d', '%d-%m-%y',
            '%d.%m.%Y', '%d.%m.%y'
        ]
        
        for fmt in formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue
        
        return None

    def _parse_amount(self, amount_str):
        """Parse amount string to float"""
        if not amount_str:
            return 0.0
        
        # Clean the amount string
        amount_str = str(amount_str).strip()
        amount_str = amount_str.replace(',', '')  # Remove thousands separators
        amount_str = amount_str.replace('$', '')  # Remove currency symbols
        amount_str = amount_str.replace('€', '')
        amount_str = amount_str.replace('£', '')
        
        try:
            return float(amount_str)
        except ValueError:
            return 0.0

    def auto_match_transactions(self, session_id, min_confidence=0.9):
        """Automatically match transactions with high confidence"""
        try:
            matches_made = self.transaction_matcher.auto_match_transactions(
                session_id, min_confidence
            )
            
            return {
                'matches_made': matches_made,
                'session_id': session_id
            }
            
        except Exception as e:
            raise Exception(f"Error auto-matching transactions: {str(e)}")

    def get_reconciliation_dashboard(self, session_id):
        """Get comprehensive reconciliation dashboard data"""
        try:
            # Get session summary
            summary = self.reconciliation_session.get_reconciliation_summary(session_id)
            if not summary:
                raise Exception("Reconciliation session not found")
            
            # Get unmatched bank transactions
            unmatched_bank = self.bank_statement_transaction.get_unmatched_transactions(session_id)
            
            # Get matched transactions
            matched_bank = self.bank_statement_transaction.get_matched_transactions(session_id)
            
            # Get matching statistics
            match_stats = self.transaction_matcher.get_match_statistics(session_id)
            
            # Get unreconciled book transactions for the account
            unreconciled_book = self._get_unreconciled_book_transactions(
                summary['session']['account_id'], 
                summary['session']['statement_date']
            )
            
            dashboard = {
                'session': summary['session'],
                'summary': {
                    'bank_transactions': summary['bank_transactions'],
                    'book_transactions': summary['book_transactions'],
                    'matches': summary['matches'],
                    'match_statistics': match_stats
                },
                'unmatched_bank_transactions': unmatched_bank,
                'matched_transactions': matched_bank,
                'unreconciled_book_transactions': unreconciled_book,
                'reconciliation_difference': summary['session']['difference']
            }
            
            return dashboard
            
        except Exception as e:
            raise Exception(f"Error getting reconciliation dashboard: {str(e)}")

    def _get_unreconciled_book_transactions(self, account_id, statement_date):
        """Get unreconciled book transactions for the account up to statement date"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT t.*, c.name as category_name
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.account_id = ? 
                AND t.date <= ? 
                AND t.reconciled = 0
                ORDER BY t.date DESC, t.amount DESC
            ''', (account_id, statement_date))
            
            transactions = []
            for row in cursor.fetchall():
                transaction = {
                    'id': row[0],
                    'date': row[1],
                    'amount': row[2],
                    'description': row[3],
                    'category_id': row[4],
                    'account_id': row[5],
                    'reconciled': row[6],
                    'type': row[7],
                    'category_name': row[15] if len(row) > 15 else None
                }
                transactions.append(transaction)
            
            return transactions
            
        except sqlite3.Error as e:
            raise Exception(f"Error getting unreconciled book transactions: {str(e)}")
        finally:
            if conn:
                conn.close()

    def find_potential_matches(self, session_id, bank_transaction_id):
        """Find potential matches for a specific bank transaction"""
        try:
            # Get the bank transaction
            bank_transaction = self.bank_statement_transaction.get_transaction_by_id(bank_transaction_id)
            if not bank_transaction:
                raise Exception("Bank transaction not found")
            
            # Find potential matches
            potential_matches = self.transaction_matcher.find_matches_for_bank_transaction(
                bank_transaction, session_id
            )
            
            return potential_matches
            
        except Exception as e:
            raise Exception(f"Error finding potential matches: {str(e)}")

    def create_manual_match(self, session_id, bank_transaction_id, book_transaction_id, notes=None):
        """Create a manual match between bank and book transactions"""
        try:
            # Create the match with manual confidence
            success = self.transaction_matcher.create_match(
                session_id, bank_transaction_id, book_transaction_id,
                'manual', 1.0, 'manual'
            )
            
            if success and notes:
                # Add notes to the match record
                self._add_match_notes(session_id, bank_transaction_id, notes)
            
            return success
            
        except Exception as e:
            raise Exception(f"Error creating manual match: {str(e)}")

    def _add_match_notes(self, session_id, bank_transaction_id, notes):
        """Add notes to a match record"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE reconciliation_matches 
                SET notes = ?
                WHERE reconciliation_session_id = ? 
                AND bank_statement_transaction_id = ?
            ''', (notes, session_id, bank_transaction_id))
            
            conn.commit()
            
        except sqlite3.Error as e:
            raise Exception(f"Error adding match notes: {str(e)}")
        finally:
            if conn:
                conn.close()

    def remove_match(self, session_id, bank_transaction_id):
        """Remove a match between transactions"""
        try:
            return self.transaction_matcher.remove_match(session_id, bank_transaction_id)
            
        except Exception as e:
            raise Exception(f"Error removing match: {str(e)}")

    def complete_reconciliation(self, session_id, notes=None):
        """Complete the reconciliation process"""
        try:
            # Check if reconciliation is ready to be completed
            dashboard = self.get_reconciliation_dashboard(session_id)
            
            unmatched_count = len(dashboard['unmatched_bank_transactions'])
            if unmatched_count > 0:
                raise Exception(f"Cannot complete reconciliation. {unmatched_count} bank transactions remain unmatched.")
            
            # Update reconciliation status
            success = self.reconciliation_session.update_reconciliation_status(
                session_id, 'completed', notes
            )
            
            if success:
                return {
                    'status': 'completed',
                    'session_id': session_id,
                    'completion_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                raise Exception("Failed to update reconciliation status")
            
        except Exception as e:
            raise Exception(f"Error completing reconciliation: {str(e)}")

    def cancel_reconciliation(self, session_id, notes=None):
        """Cancel the reconciliation process"""
        try:
            # Unmark all reconciled transactions
            self._unmark_reconciled_transactions(session_id)
            
            # Update reconciliation status
            success = self.reconciliation_session.update_reconciliation_status(
                session_id, 'cancelled', notes
            )
            
            return success
            
        except Exception as e:
            raise Exception(f"Error cancelling reconciliation: {str(e)}")

    def _unmark_reconciled_transactions(self, session_id):
        """Unmark all transactions that were reconciled in this session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get all matched book transaction IDs
            cursor.execute('''
                SELECT DISTINCT book_transaction_id 
                FROM reconciliation_matches 
                WHERE reconciliation_session_id = ?
            ''', (session_id,))
            
            transaction_ids = [row[0] for row in cursor.fetchall()]
            
            # Unmark them as reconciled
            for trans_id in transaction_ids:
                cursor.execute('''
                    UPDATE transactions 
                    SET reconciled = 0 
                    WHERE id = ?
                ''', (trans_id,))
            
            conn.commit()
            
        except sqlite3.Error as e:
            raise Exception(f"Error unmarking reconciled transactions: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_reconciliation_report(self, session_id):
        """Generate a comprehensive reconciliation report"""
        try:
            dashboard = self.get_reconciliation_dashboard(session_id)
            
            report = {
                'session_info': dashboard['session'],
                'summary': dashboard['summary'],
                'reconciliation_items': {
                    'matched_transactions': dashboard['matched_transactions'],
                    'unmatched_bank_transactions': dashboard['unmatched_bank_transactions'],
                    'unreconciled_book_transactions': dashboard['unreconciled_book_transactions']
                },
                'reconciliation_difference': dashboard['reconciliation_difference'],
                'generated_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return report
            
        except Exception as e:
            raise Exception(f"Error generating reconciliation report: {str(e)}")
