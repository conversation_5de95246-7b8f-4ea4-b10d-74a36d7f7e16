# Setup Files for Cashbook Application

This folder contains all the files needed to build and distribute the Cashbook application as a Windows executable.

## Files Description

- **cashbook.spec** - PyInstaller specification file that defines how to package the application
- **cashbook_installer.iss** - Inno Setup script for creating a professional Windows installer
- **file_version_info.txt** - Version information file with author and copyright details
- **build_exe.bat** - Simple batch file to build just the executable
- **setup_and_build.bat** - Comprehensive script that installs dependencies, builds executable, and creates installer
- **build_release.bat** - Complete release builder script
- **create_icon.py** - Script to convert logo.png to Windows icon format
- **DISTRIBUTION_GUIDE.md** - Detailed guide for building and distributing the application
- **DISTRIBUTION_README.md** - Quick reference for the distribution process

## Quick Start

1. **For a complete build**: Run `setup_and_build.bat`
2. **For just the executable**: Run `build_exe.bat`
3. **For a release build**: Run `build_release.bat`

## Prerequisites

- Python 3.x with all required packages installed
- PyInstaller (`pip install pyinstaller`)
- Inno Setup 6 or later (for creating the installer)

## Output

- Executable will be created in `dist/Cashbook/`
- Installer will be created in `Output/Cashbook_Setup.exe`

## Notes

- All scripts are designed to be run from the project root directory
- The scripts will automatically handle path references to files in this folder
- Make sure all dependencies are installed before running the build scripts
