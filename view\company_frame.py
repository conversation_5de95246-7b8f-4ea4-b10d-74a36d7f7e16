import datetime
import tkinter as tk
from tkinter import messagebox, simpledialog

import ttkbootstrap as ttk
from PIL import Image, ImageTk
from ttkbootstrap.constants import *
from ttkbootstrap.tooltip import ToolTip

from model.account import Account
from model.category import Category
from view.components.date_picker import DatePicker


class CompanyFrame(ttk.Frame):
    """Company window as a frame instead of a standalone window"""

    def __init__(self, parent, company_name, transaction_manager, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.transaction_manager = transaction_manager
        self.close_callback = close_callback
        self.title = f"Cashbook - {company_name}"

        # State variables for panels
        self.search_panel_visible = False
        self.filter_panel_visible = False

        self.create_widgets()
        self.load_transactions()
        self.update_summary()

    def load_icon(self, path, size=None):
        """Load an icon from the specified path and resize if needed"""
        try:
            img = Image.open(path)
            if size:
                img = img.resize(size, Image.LANCZOS)
            return ImageTk.PhotoImage(img)
        except Exception as e:
            print(f"Error loading icon {path}: {e}")
            return None

    def toggle_search_panel(self):
        """Toggle the visibility of the search panel"""
        self.search_panel_visible = not self.search_panel_visible
        if self.search_panel_visible:
            self.search_frame.pack(fill="x", pady=5, after=self.toolbar_frame)
        else:
            self.search_frame.pack_forget()

    def toggle_filter_panel(self):
        """Toggle the visibility of the filter panel"""
        self.filter_panel_visible = not self.filter_panel_visible
        if self.filter_panel_visible:
            self.filter_frame.pack(fill="x", pady=5, after=self.search_frame if self.search_panel_visible else self.toolbar_frame)
        else:
            self.filter_frame.pack_forget()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with company name and controls
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        # Left side - company name
        company_label = ttk.Label(header_frame, text=self.company_name, font=("Segoe UI", 16, "bold"))
        company_label.pack(side="left")

        # Right side - search, filter, and close buttons
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side="right")

        # Create a custom style for transparent icon buttons
        style = ttk.Style()

        # Get the default background color from the parent frame
        bg_color = style.lookup("TFrame", "background")

        # Configure a completely transparent button style
        style.configure("Transparent.TButton",
                       background=bg_color,  # Match parent background
                       foreground=bg_color,  # Match parent background
                       borderwidth=0,        # No border
                       focusthickness=0,     # No focus thickness
                       focuscolor=bg_color,  # No focus color
                       relief="flat",        # Flat appearance
                       padding=4)            # Small padding

        # Configure hover effect without changing background
        style.map("Transparent.TButton",
                 background=[("active", bg_color)],  # Keep background on hover
                 relief=[("active", "flat")])        # Keep flat on hover

        # Load icons
        search_icon = self.load_icon("resources/icons/icons8-search-48.png", (24, 24))
        filter_icon = self.load_icon("resources/icons/icons8-filter-48.png", (24, 24))

        # Create a frame with the same background color for each icon button
        search_frame = ttk.Frame(controls_frame, style="TFrame")
        search_frame.pack(side="left", padx=5)

        # Search button with icon using transparent style
        search_button = ttk.Button(search_frame, image=search_icon, style="Transparent.TButton",
                                  command=self.toggle_search_panel)
        search_button.image = search_icon  # Keep a reference to prevent garbage collection
        search_button.pack(side="left")
        ToolTip(search_button, text="Search transactions")

        # Create a frame for the filter button
        filter_frame = ttk.Frame(controls_frame, style="TFrame")
        filter_frame.pack(side="left", padx=5)

        # Filter button with icon using transparent style
        filter_button = ttk.Button(filter_frame, image=filter_icon, style="Transparent.TButton",
                                  command=self.toggle_filter_panel)
        filter_button.image = filter_icon  # Keep a reference to prevent garbage collection
        filter_button.pack(side="left")
        ToolTip(filter_button, text="Filter transactions")

        # Close company button
        close_button = ttk.Button(controls_frame, text="Close Company",
                                 command=self.close_callback, bootstyle=DANGER)
        close_button.pack(side="left", padx=5)

        # Summary frame
        summary_frame = ttk.Frame(main_frame)
        summary_frame.pack(fill="x", pady=(0, 10))

        # Income
        income_frame = ttk.LabelFrame(summary_frame, text="Income", bootstyle=SUCCESS)
        income_frame.pack(side="left", fill="x", expand=True, padx=(0, 5))

        self.income_label = ttk.Label(income_frame, text="$0.00", font=("Segoe UI", 14, "bold"), foreground="green")
        self.income_label.pack(pady=10)

        # Expenses
        expenses_frame = ttk.LabelFrame(summary_frame, text="Expenses", bootstyle=DANGER)
        expenses_frame.pack(side="left", fill="x", expand=True, padx=5)

        self.expenses_label = ttk.Label(expenses_frame, text="$0.00", font=("Segoe UI", 14, "bold"), foreground="red")
        self.expenses_label.pack(pady=10)

        # Balance
        balance_frame = ttk.LabelFrame(summary_frame, text="Balance", bootstyle=INFO)
        balance_frame.pack(side="left", fill="x", expand=True, padx=(5, 0))

        self.balance_label = ttk.Label(balance_frame, text="$0.00", font=("Segoe UI", 14, "bold"))
        self.balance_label.pack(pady=10)

        # Button toolbar frame
        self.toolbar_frame = ttk.Frame(main_frame)
        self.toolbar_frame.pack(fill="x", pady=10)

        # Create button groups
        transaction_group = ttk.LabelFrame(self.toolbar_frame, text="Transaction", padding=(5, 0, 5, 5))
        transaction_group.pack(side="left", padx=(0, 10), fill="y")

        data_group = ttk.LabelFrame(self.toolbar_frame, text="Data Management", padding=(5, 0, 5, 5))
        data_group.pack(side="left", padx=(0, 10), fill="y")

        operations_group = ttk.LabelFrame(self.toolbar_frame, text="Operations", padding=(5, 0, 5, 5))
        operations_group.pack(side="left", padx=(0, 10), fill="y")

        analysis_group = ttk.LabelFrame(self.toolbar_frame, text="Analysis", padding=(5, 0, 5, 5))
        analysis_group.pack(side="left", padx=(0, 10), fill="y")

        import_export_group = ttk.LabelFrame(self.toolbar_frame, text="Import/Export", padding=(5, 0, 5, 5))
        import_export_group.pack(side="left", padx=(0, 10), fill="y")

        # Transaction Management Group
        add_button = ttk.Button(transaction_group, text="Add", command=self.add_transaction,
                               bootstyle=SUCCESS, width=8)
        add_button.pack(side="left", padx=2)
        ToolTip(add_button, text="Add a new transaction")

        edit_button = ttk.Button(transaction_group, text="Edit", command=self.edit_transaction,
                                width=8)
        edit_button.pack(side="left", padx=2)
        ToolTip(edit_button, text="Edit the selected transaction")

        delete_button = ttk.Button(transaction_group, text="Delete", command=self.delete_transaction,
                                  bootstyle=DANGER, width=8)
        delete_button.pack(side="left", padx=2)
        ToolTip(delete_button, text="Delete the selected transaction")

        # Data Management Group
        accounts_button = ttk.Button(data_group, text="Accounts", command=self.manage_accounts,
                                    width=10)
        accounts_button.pack(side="left", padx=2)
        ToolTip(accounts_button, text="Manage bank accounts and credit cards")

        categories_button = ttk.Button(data_group, text="Categories", command=self.manage_categories,
                                      width=10)
        categories_button.pack(side="left", padx=2)
        ToolTip(categories_button, text="Manage income and expense categories")

        dashboard_button = ttk.Button(data_group, text="Dashboard", command=self.open_account_dashboard,
                                     width=10)
        dashboard_button.pack(side="left", padx=2)
        ToolTip(dashboard_button, text="View account dashboard with balances and statistics")

        # Financial Operations Group
        journal_button = ttk.Button(operations_group, text="Journal", command=self.open_journal_entry,
                                   width=10)
        journal_button.pack(side="left", padx=2)
        ToolTip(journal_button, text="Create journal entries for accounting adjustments")

        reconciliation_button = ttk.Button(operations_group, text="Reconcile", command=self.open_reconciliation,
                                          width=10)
        reconciliation_button.pack(side="left", padx=2)
        ToolTip(reconciliation_button, text="Reconcile accounts with bank statements")

        split_mgmt_button = ttk.Button(operations_group, text="Splits", command=self.open_split_management,
                                      width=10, bootstyle=INFO)
        split_mgmt_button.pack(side="left", padx=2)
        ToolTip(split_mgmt_button, text="Manage split transactions")

        bills_button = ttk.Button(operations_group, text="Bills", command=self.open_bills_dashboard,
                                 width=10, bootstyle=WARNING)
        bills_button.pack(side="left", padx=2)
        ToolTip(bills_button, text="Manage bills and recurring payments")

        invoices_button = ttk.Button(operations_group, text="Invoices", command=self.open_invoice_dashboard,
                                   width=10, bootstyle=INFO)
        invoices_button.pack(side="left", padx=2)
        ToolTip(invoices_button, text="Create and manage invoices")

        reminders_button = ttk.Button(operations_group, text="Reminders", command=self.open_reminder_settings,
                                     width=10)
        reminders_button.pack(side="left", padx=2)
        ToolTip(reminders_button, text="Configure bill reminders and notifications")

        # Data Analysis Group
        reports_button = ttk.Button(analysis_group, text="Reports", command=self.open_reports,
                                   bootstyle=INFO, width=8)
        reports_button.pack(side="left", padx=2)
        ToolTip(reports_button, text="Generate financial reports")

        charts_button = ttk.Button(analysis_group, text="Charts", command=self.open_charts,
                                  bootstyle=INFO, width=8)
        charts_button.pack(side="left", padx=2)
        ToolTip(charts_button, text="View financial charts and visualizations")

        # Import/Export Group
        import_trans_button = ttk.Button(import_export_group, text="Import Tx", command=self.open_import,
                                        width=8)
        import_trans_button.pack(side="left", padx=2)
        ToolTip(import_trans_button, text="Import transactions from bank statements")

        import_button = ttk.Button(import_export_group, text="Import", command=self.import_file,
                                  width=8)
        import_button.pack(side="left", padx=2)
        ToolTip(import_button, text="Import data from files")

        export_button = ttk.Button(import_export_group, text="Export", command=self.generate_report,
                                  width=8)
        export_button.pack(side="left", padx=2)
        ToolTip(export_button, text="Export data to various formats")

        share_button = ttk.Button(import_export_group, text="Share", command=self.share_data,
                                 width=8)
        share_button.pack(side="left", padx=2)
        ToolTip(share_button, text="Share data with others")

        # Search frame - initially hidden
        self.search_frame = ttk.Frame(main_frame)
        # Not packed initially - will be shown when search button is clicked

        search_icon_small = self.load_icon("resources/icons/icons8-search-48.png", (16, 16))
        search_label = ttk.Label(self.search_frame, image=search_icon_small, text="Search:", compound="left")
        search_label.image = search_icon_small  # Keep a reference
        search_label.pack(side="left", padx=5)

        self.search_var = tk.StringVar()
        self.search_var.trace_add("write", lambda *_: self.filter_transactions())

        search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side="left", padx=5)

        # Quick search buttons
        quick_search_frame = ttk.Frame(self.search_frame)
        quick_search_frame.pack(side="left", padx=10)

        ttk.Button(quick_search_frame, text="Today", width=8,
                  command=lambda: self.quick_search("today")).pack(side="left", padx=2)
        ttk.Button(quick_search_frame, text="This Week", width=8,
                  command=lambda: self.quick_search("week")).pack(side="left", padx=2)
        ttk.Button(quick_search_frame, text="This Month", width=8,
                  command=lambda: self.quick_search("month")).pack(side="left", padx=2)

        # Filter frame - initially hidden
        self.filter_frame = ttk.LabelFrame(main_frame, text="Filters", padding=10)
        # Not packed initially - will be shown when filter button is clicked

        # Date range filter
        date_filter_frame = ttk.Frame(self.filter_frame)
        date_filter_frame.pack(side="left", padx=10)

        ttk.Label(date_filter_frame, text="Date Range:").grid(row=0, column=0, padx=5)

        # From date
        ttk.Label(date_filter_frame, text="From:").grid(row=0, column=1, padx=5)
        self.from_date_var = tk.StringVar()
        from_date_entry = ttk.Entry(date_filter_frame, textvariable=self.from_date_var, width=10)
        from_date_entry.grid(row=0, column=2, padx=5)

        # To date
        ttk.Label(date_filter_frame, text="To:").grid(row=0, column=3, padx=5)
        self.to_date_var = tk.StringVar()
        to_date_entry = ttk.Entry(date_filter_frame, textvariable=self.to_date_var, width=10)
        to_date_entry.grid(row=0, column=4, padx=5)

        # Account filter
        account_filter_frame = ttk.Frame(self.filter_frame)
        account_filter_frame.pack(side="left", padx=10)

        ttk.Label(account_filter_frame, text="Account:").grid(row=0, column=0, padx=5)

        # Get accounts from database
        from model.account import Account
        account_model = Account(self.transaction_manager.db_path)
        accounts = account_model.get_all_accounts()

        # Create account dropdown
        self.account_filter_var = tk.StringVar()
        account_names = ["All Accounts"] + [account["name"] for account in accounts]
        self.account_map_by_name = {account["name"]: account["id"] for account in accounts}

        account_combo = ttk.Combobox(account_filter_frame, textvariable=self.account_filter_var,
                                    values=account_names, width=15, state="readonly")
        account_combo.current(0)  # Set to "All Accounts"
        account_combo.grid(row=0, column=1, padx=5)

        # Apply filter button
        apply_filter_button = ttk.Button(self.filter_frame, text="Apply", command=self.filter_transactions)
        apply_filter_button.pack(side="left", padx=10)

        # Reset filter button
        reset_filter_button = ttk.Button(self.filter_frame, text="Reset", command=self.reset_filters)
        reset_filter_button.pack(side="left", padx=5)

        # Transaction section with header and controls
        transaction_section = ttk.Frame(main_frame)
        transaction_section.pack(fill="both", expand=True, pady=5)

        # Transaction header with title and controls
        transaction_header = ttk.Frame(transaction_section)
        transaction_header.pack(fill="x", pady=(0, 5))

        # Left side - title
        ttk.Label(transaction_header, text="Transactions",
                 font=("Segoe UI", 12, "bold")).pack(side="left")

        # Right side - transaction controls
        transaction_controls = ttk.Frame(transaction_header)
        transaction_controls.pack(side="right")

        # Category filter
        ttk.Label(transaction_controls, text="Category:").pack(side="left", padx=(0, 5))
        self.category_filter_var = tk.StringVar(value="All Categories")

        # Get categories for filter
        category_model = Category(self.transaction_manager.db_path)
        categories = category_model.get_all_categories()
        category_names = ["All Categories"] + [cat["name"] for cat in categories]

        category_filter = ttk.Combobox(transaction_controls, textvariable=self.category_filter_var,
                                     values=category_names, width=15, state="readonly")
        category_filter.pack(side="left", padx=(0, 10))
        category_filter.bind("<<ComboboxSelected>>", lambda _: self.filter_transactions())

        # Type filter
        ttk.Label(transaction_controls, text="Type:").pack(side="left", padx=(0, 5))
        self.type_filter_var = tk.StringVar(value="All Types")
        type_filter = ttk.Combobox(transaction_controls, textvariable=self.type_filter_var,
                                 values=["All Types", "Income", "Expense"], width=10, state="readonly")
        type_filter.pack(side="left", padx=(0, 10))
        type_filter.bind("<<ComboboxSelected>>", lambda _: self.filter_transactions())

        # Status filter
        ttk.Label(transaction_controls, text="Status:").pack(side="left", padx=(0, 5))
        self.status_filter_var = tk.StringVar(value="All")
        status_filter = ttk.Combobox(transaction_controls, textvariable=self.status_filter_var,
                                   values=["All", "Categorized", "Uncategorized"], width=12, state="readonly")
        status_filter.pack(side="left")
        status_filter.bind("<<ComboboxSelected>>", lambda _: self.filter_transactions())

        # Transaction list in a frame with padding
        transactions_frame = ttk.Frame(transaction_section, padding=5)
        transactions_frame.pack(fill="both", expand=True)

        # Create a modern styled treeview for transactions
        columns = ("selected", "id", "date", "description", "type", "category", "income", "expense", "balance")

        # Create a custom style for the treeview with modern styling
        style = ttk.Style()

        # Configure the custom Treeview style with striping and modern look
        style.configure("Modern.Treeview",
                       rowheight=28,
                       fieldbackground="white",
                       background="white",
                       foreground="#333333",
                       borderwidth=0)

        # Configure the heading style
        style.configure("Modern.Treeview.Heading",
                       font=("Segoe UI", 10, "bold"),
                       borderwidth=1,
                       relief="solid",
                       background="#f0f0f0",
                       foreground="#333333")

        # Configure selection colors
        style.map("Modern.Treeview",
                 background=[("selected", "#e6f2ff")],
                 foreground=[("selected", "#000000")])

        # Create the treeview with the modern style
        self.tree = ttk.Treeview(transactions_frame, columns=columns, show="headings",
                               style="Modern.Treeview", selectmode="extended")

        # Define headings with centered text and icons
        self.tree.heading("selected", text="✓", anchor="center")
        self.tree.heading("id", text="ID", anchor="center")
        self.tree.heading("date", text="Date", anchor="center")
        self.tree.heading("description", text="Description", anchor="center")
        self.tree.heading("type", text="Type", anchor="center")
        self.tree.heading("category", text="Category", anchor="center")
        self.tree.heading("income", text="Income", anchor="center")
        self.tree.heading("expense", text="Expense", anchor="center")
        self.tree.heading("balance", text="Balance", anchor="center")

        # Define columns with proper widths and alignment
        self.tree.column("selected", width=30, anchor="center", stretch=False)
        self.tree.column("id", width=50, anchor="center", stretch=False)
        self.tree.column("date", width=100, anchor="center", stretch=False)
        self.tree.column("description", width=250, anchor="w", stretch=True)
        self.tree.column("type", width=80, anchor="center", stretch=False)
        self.tree.column("category", width=150, anchor="w", stretch=False)
        self.tree.column("income", width=100, anchor="e", stretch=False)
        self.tree.column("expense", width=100, anchor="e", stretch=False)
        self.tree.column("balance", width=120, anchor="e", stretch=False)

        # Add scrollbars
        scrollbar_y = ttk.Scrollbar(transactions_frame, orient="vertical", command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(transactions_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Pack scrollbars and treeview
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        self.tree.pack(fill="both", expand=True)

        # Add visual separators between columns
        self.add_column_separators()

        # Add transaction summary panel
        self.summary_panel = ttk.Frame(transaction_section, padding=5)
        self.summary_panel.pack(fill="x", pady=(5, 0))

        # Transaction counts
        counts_frame = ttk.Frame(self.summary_panel)
        counts_frame.pack(side="left")

        self.total_count_var = tk.StringVar(value="Total: 0")
        self.categorized_count_var = tk.StringVar(value="Categorized: 0")
        self.uncategorized_count_var = tk.StringVar(value="Uncategorized: 0")

        ttk.Label(counts_frame, textvariable=self.total_count_var).pack(side="left", padx=(0, 15))
        ttk.Label(counts_frame, textvariable=self.categorized_count_var,
                 bootstyle="success").pack(side="left", padx=(0, 15))
        ttk.Label(counts_frame, textvariable=self.uncategorized_count_var,
                 bootstyle="danger").pack(side="left")

        # Dictionary to track selected state of each row
        self.selected_items = {}

        # Bind click on the checkbox column
        self.tree.bind("<ButtonRelease-1>", self.toggle_selection)

        # Add keyboard shortcuts
        self.tree.bind("<space>", lambda _: self.toggle_selected_rows())
        self.tree.bind("<Control-a>", lambda _: self.select_all_rows())

        # Add right-click context menu with icons
        self.context_menu = tk.Menu(self, tearoff=0)

        # Transaction operations
        self.context_menu.add_command(label="Edit Transaction", command=self.edit_transaction)
        self.context_menu.add_command(label="Split Transaction", command=self.split_transaction)
        self.context_menu.add_command(label="Set Category", command=self.set_category)
        self.context_menu.add_separator()

        # Edit options
        self.context_menu.add_command(label="Cut", command=self.cut_transaction,
                                     accelerator="Ctrl+X")
        self.context_menu.add_command(label="Copy", command=self.copy_transaction,
                                     accelerator="Ctrl+C")
        self.context_menu.add_command(label="Paste", command=self.paste_transaction,
                                     accelerator="Ctrl+V")
        self.context_menu.add_separator()

        # Row operations
        self.context_menu.add_command(label="Insert Line", command=self.insert_line)
        self.context_menu.add_command(label="Delete Line", command=self.delete_transaction)
        self.context_menu.add_command(label="Undo Line", command=self.undo_line)
        self.context_menu.add_separator()

        # Additional options
        self.context_menu.add_command(label="Spell Check", command=self.spell_check)
        self.context_menu.add_command(label="Sort A - Z", command=self.sort_transactions)
        self.context_menu.add_command(label="Find", command=self.find_transaction,
                                     accelerator="Ctrl+F")
        self.context_menu.add_command(label="Merge With", command=self.merge_transactions)

        # Bind right-click to show context menu
        self.tree.bind("<Button-3>", self.show_context_menu)

        # Double-click to edit category
        self.tree.bind("<Double-1>", self.handle_double_click)

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(5, 0))

    def load_transactions(self):
        """Load transactions from the database"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Reset selected items dictionary
        self.selected_items = {}

        # Get transactions from manager
        if self.transaction_manager:
            transactions = self.transaction_manager.get_transactions(sort_by="date", sort_order="ASC")

            # Calculate running balance
            running_balance = 0

            # Track categorization stats
            total_count = len(transactions)
            categorized_count = 0
            uncategorized_count = 0

            # Add to treeview with alternating row colors
            for i, transaction in enumerate(transactions):
                # Determine transaction type
                transaction_type = transaction.get('type', '')

                # Get amount
                amount = transaction['amount']

                # Update running balance
                if transaction_type == 'income':
                    running_balance += amount
                    income_amount = f"${amount:.2f}"
                    expense_amount = ""
                elif transaction_type == 'expense':
                    running_balance -= amount
                    income_amount = ""
                    expense_amount = f"${amount:.2f}"
                else:
                    income_amount = ""
                    expense_amount = ""

                # Format balance with currency symbol
                balance = f"${running_balance:.2f}"

                # Get category name and determine if categorized
                category = transaction.get('category_name', '')
                if category and category != 'Uncategorized':
                    categorized_count += 1
                    category_status = "categorized"
                    checkbox_state = "✓"  # Categorized indicator
                else:
                    uncategorized_count += 1
                    category_status = "uncategorized"
                    checkbox_state = "☐"  # Uncategorized indicator
                    category = "Uncategorized"

                # Format transaction type for display
                display_type = transaction_type.capitalize()

                # Get description
                description = transaction.get('description', '')

                # Determine row tag for styling (alternating rows + transaction type)
                row_tag = f"{transaction_type}"
                if i % 2 == 1:
                    row_tag = f"{row_tag}_alt"

                # Add category status tag
                row_tag = f"{row_tag} {category_status}"

                # Insert into treeview with checkbox state
                item_id = self.tree.insert(
                    "", "end",
                    values=(
                        checkbox_state,  # Checkbox/status indicator
                        transaction['id'],
                        transaction['date'],
                        description,
                        display_type,
                        category,
                        income_amount,
                        expense_amount,
                        balance
                    ),
                    tags=(row_tag,)
                )

                # Store selection state
                self.selected_items[item_id] = False

            # Configure row styling
            self.tree.tag_configure("income", background="#e6ffe6")  # Light green for income
            self.tree.tag_configure("expense", background="#fff0f0")  # Light red for expense
            self.tree.tag_configure("income_alt", background="#d6efd6")  # Darker green for alternating rows
            self.tree.tag_configure("expense_alt", background="#f0e0e0")  # Darker red for alternating rows

            # Category status styling
            self.tree.tag_configure("uncategorized", foreground="#cc0000")  # Red text for uncategorized

            # Update transaction summary
            self.total_count_var.set(f"Total: {total_count}")
            self.categorized_count_var.set(f"Categorized: {categorized_count}")
            self.uncategorized_count_var.set(f"Uncategorized: {uncategorized_count}")

            self.status_var.set(f"Loaded {len(transactions)} transactions")

    def update_summary(self):
        """Update summary information"""
        if self.transaction_manager:
            # Get summary from manager
            summary = self.transaction_manager.get_balance_summary()

            # Update labels
            self.income_label.config(text=f"${summary.get('income', 0):.2f}")
            self.expenses_label.config(text=f"${summary.get('expenses', 0):.2f}")

            balance = summary.get('balance', 0)
            self.balance_label.config(
                text=f"${balance:.2f}",
                foreground="green" if balance >= 0 else "red"
            )

    def filter_transactions(self):
        """Filter transactions based on search, date range, account, category, type, and status"""
        # Get filter values
        search_text = self.search_var.get().lower()
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        account_filter = self.account_filter_var.get()
        category_filter = self.category_filter_var.get()
        type_filter = self.type_filter_var.get()
        status_filter = self.status_filter_var.get()

        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Reset selected items dictionary
        self.selected_items = {}

        # Get transactions from manager
        if self.transaction_manager:
            transactions = self.transaction_manager.get_transactions(sort_by="date", sort_order="ASC")

            # Calculate running balance
            running_balance = 0

            # Track categorization stats
            total_count = 0
            categorized_count = 0
            uncategorized_count = 0

            # Filter and add to treeview
            filtered_count = 0
            for i, transaction in enumerate(transactions):
                # Check date range
                if from_date and transaction['date'] < from_date:
                    continue
                if to_date and transaction['date'] > to_date:
                    continue

                # Check account filter
                if account_filter != "All Accounts":
                    account_id = self.account_map_by_name.get(account_filter)
                    if account_id and transaction.get('account_id') != account_id:
                        continue

                # Check category filter
                if category_filter != "All Categories":
                    if transaction.get('category_name', '') != category_filter:
                        continue

                # Check type filter
                if type_filter != "All Types":
                    if transaction.get('type', '').capitalize() != type_filter:
                        continue

                # Check status filter (categorized/uncategorized)
                category_name = transaction.get('category_name', '')
                is_categorized = category_name and category_name != 'Uncategorized'

                if status_filter == "Categorized" and not is_categorized:
                    continue
                elif status_filter == "Uncategorized" and is_categorized:
                    continue

                # Check search text
                if search_text and not (
                   search_text in str(transaction.get('id', '')).lower() or \
                   search_text in transaction.get('date', '').lower() or \
                   search_text in transaction.get('description', '').lower() or \
                   search_text in str(transaction.get('amount', '')).lower() or \
                   search_text in transaction.get('category_name', '').lower() or \
                   search_text in transaction.get('type', '').lower() or \
                   search_text in transaction.get('account_name', '').lower()):
                    continue

                # Determine transaction type
                transaction_type = transaction.get('type', '')

                # Get amount
                amount = transaction['amount']

                # Update running balance
                if transaction_type == 'income':
                    running_balance += amount
                    income_amount = f"${amount:.2f}"
                    expense_amount = ""
                elif transaction_type == 'expense':
                    running_balance -= amount
                    income_amount = ""
                    expense_amount = f"${amount:.2f}"
                else:
                    income_amount = ""
                    expense_amount = ""

                # Format balance with currency symbol
                balance = f"${running_balance:.2f}"

                # Get category name and determine if categorized
                category = transaction.get('category_name', '')
                if category and category != 'Uncategorized':
                    categorized_count += 1
                    category_status = "categorized"
                    checkbox_state = "✓"  # Categorized indicator
                else:
                    uncategorized_count += 1
                    category_status = "uncategorized"
                    checkbox_state = "☐"  # Uncategorized indicator
                    category = "Uncategorized"

                # Get description
                description = transaction.get('description', '')

                # Format transaction type for display
                display_type = transaction_type.capitalize()

                # Determine row tag for styling (alternating rows + transaction type)
                row_tag = f"{transaction_type}"
                if i % 2 == 1:
                    row_tag = f"{row_tag}_alt"

                # Add category status tag
                row_tag = f"{row_tag} {category_status}"

                # Insert into treeview with checkbox state
                item_id = self.tree.insert(
                    "", "end",
                    values=(
                        checkbox_state,  # Checkbox/status indicator
                        transaction['id'],
                        transaction['date'],
                        description,
                        display_type,
                        category,
                        income_amount,
                        expense_amount,
                        balance
                    ),
                    tags=(row_tag,)
                )

                # Store selection state
                self.selected_items[item_id] = False

                # Increment counters
                filtered_count += 1
                total_count += 1

            # Configure row styling
            self.tree.tag_configure("income", background="#e6ffe6")  # Light green for income
            self.tree.tag_configure("expense", background="#fff0f0")  # Light red for expense
            self.tree.tag_configure("income_alt", background="#d6efd6")  # Darker green for alternating rows
            self.tree.tag_configure("expense_alt", background="#f0e0e0")  # Darker red for alternating rows

            # Category status styling
            self.tree.tag_configure("uncategorized", foreground="#cc0000")  # Red text for uncategorized

            # Update transaction summary
            self.total_count_var.set(f"Total: {total_count}")
            self.categorized_count_var.set(f"Categorized: {categorized_count}")
            self.uncategorized_count_var.set(f"Uncategorized: {uncategorized_count}")

            self.status_var.set(f"Found {filtered_count} transactions")

    def reset_filters(self):
        """Reset all filters"""
        self.search_var.set("")
        self.from_date_var.set("")
        self.to_date_var.set("")
        self.account_filter_var.set("All Accounts")
        self.filter_transactions()

    def add_transaction(self):
        """Show dialog to add a new transaction"""
        # Create a new toplevel window
        add_window = tk.Toplevel(self)
        add_window.title("Add Transaction")
        add_window.geometry("500x450")
        add_window.transient(self)  # Set to be on top of the main window
        add_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(add_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Transaction type selection
        ttk.Label(frame, text="Transaction Type:", font=("Segoe UI", 11)).grid(row=0, column=0, sticky="w", pady=(0, 10))

        type_var = tk.StringVar(value="expense")
        type_frame = ttk.Frame(frame)
        type_frame.grid(row=0, column=1, sticky="w", pady=(0, 10))

        ttk.Radiobutton(type_frame, text="Expense", variable=type_var, value="expense").pack(side="left", padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Income", variable=type_var, value="income").pack(side="left")

        # Date field with date picker
        ttk.Label(frame, text="Date:", font=("Segoe UI", 11)).grid(row=1, column=0, sticky="w", pady=(0, 10))
        date_picker = DatePicker(frame, initial_date=datetime.date.today())
        date_picker.grid(row=1, column=1, sticky="ew", pady=(0, 10))

        # Description field
        ttk.Label(frame, text="Description:", font=("Segoe UI", 11)).grid(row=2, column=0, sticky="w", pady=(0, 10))
        desc_var = tk.StringVar()
        desc_entry = ttk.Entry(frame, textvariable=desc_var)
        desc_entry.grid(row=2, column=1, sticky="ew", pady=(0, 10))

        # Amount field
        ttk.Label(frame, text="Amount ($):", font=("Segoe UI", 11)).grid(row=3, column=0, sticky="w", pady=(0, 10))
        amount_var = tk.DoubleVar(value=0.0)
        amount_entry = ttk.Entry(frame, textvariable=amount_var)
        amount_entry.grid(row=3, column=1, sticky="ew", pady=(0, 10))

        # Category dropdown
        ttk.Label(frame, text="Category:", font=("Segoe UI", 11)).grid(row=4, column=0, sticky="w", pady=(0, 10))

        # Get categories from database
        category_model = Category(self.transaction_manager.db_path)
        categories = category_model.get_all_categories()

        # Create a dictionary to map category names to IDs
        self.category_map = {f"{cat['name']} ({cat['type']})": cat['id'] for cat in categories}
        category_names = list(self.category_map.keys())

        category_var = tk.StringVar()
        if category_names:
            category_var.set(category_names[0])

        category_dropdown = ttk.Combobox(frame, textvariable=category_var, values=category_names, state="readonly")
        category_dropdown.grid(row=4, column=1, sticky="ew", pady=(0, 10))

        # Account dropdown
        ttk.Label(frame, text="Account:", font=("Segoe UI", 11)).grid(row=5, column=0, sticky="w", pady=(0, 10))

        # Get accounts from database
        account_model = Account(self.transaction_manager.db_path)
        accounts = account_model.get_all_accounts()

        # Create a dictionary to map account names to IDs
        self.account_map = {f"{acc['name']} ({acc['type']})": acc['id'] for acc in accounts}
        account_names = list(self.account_map.keys())

        account_var = tk.StringVar()
        if account_names:
            account_var.set(account_names[0])

        account_dropdown = ttk.Combobox(frame, textvariable=account_var, values=account_names, state="readonly")
        account_dropdown.grid(row=5, column=1, sticky="ew", pady=(0, 10))

        # Notes field
        ttk.Label(frame, text="Notes:", font=("Segoe UI", 11)).grid(row=6, column=0, sticky="nw", pady=(0, 10))
        notes_var = tk.StringVar()
        notes_entry = ttk.Entry(frame, textvariable=notes_var)
        notes_entry.grid(row=6, column=1, sticky="ew", pady=(0, 10))

        # Error message label
        error_var = tk.StringVar()
        error_label = ttk.Label(frame, textvariable=error_var, foreground="red")
        error_label.grid(row=7, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=8, column=0, columnspan=2, pady=(10, 0))

        def save_transaction():
            # Clear previous error
            error_var.set("")

            # Validate inputs
            try:
                # Get values
                transaction_type = type_var.get()
                date = date_picker.get_date().strftime("%Y-%m-%d")
                description = desc_var.get().strip()
                amount = amount_var.get()
                category_id = self.category_map.get(category_var.get())
                account_id = self.account_map.get(account_var.get())

                # Validate required fields
                if not description:
                    error_var.set("Description is required")
                    desc_entry.focus_set()
                    return

                if amount <= 0:
                    error_var.set("Amount must be greater than zero")
                    amount_entry.focus_set()
                    return

                if not category_id:
                    error_var.set("Category is required")
                    category_dropdown.focus_set()
                    return

                if not account_id:
                    error_var.set("Account is required")
                    account_dropdown.focus_set()
                    return

                # Add transaction
                self.transaction_manager.add_transaction(
                    date=date,
                    amount=amount,
                    account_id=account_id,
                    description=description,
                    category_id=category_id,
                    type_name=transaction_type,
                    reconciled=0
                )

                # Close window and refresh
                add_window.destroy()
                self.load_transactions()
                self.update_summary()

                self.status_var.set(f"Added new {transaction_type} transaction")

            except Exception as e:
                error_var.set(f"Error: {str(e)}")

        save_button = ttk.Button(button_frame, text="Save", command=save_transaction, bootstyle=SUCCESS)
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel", command=add_window.destroy, bootstyle=DANGER)
        cancel_button.pack(side="left")

        # Configure grid column weights
        frame.columnconfigure(1, weight=1)

        # Set focus to description field
        desc_entry.focus_set()

    def split_transaction(self):
        """Split the selected transaction into multiple categories/accounts"""
        # Get selected item
        selected = self.tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a transaction to split")
            return

        # Get transaction ID
        transaction_id = self.tree.item(selected[0], "values")[0]

        # Get transaction details
        transaction = None
        try:
            # Get transactions from manager
            transactions = self.transaction_manager.get_transactions()

            # Find the selected transaction
            for t in transactions:
                if str(t['id']) == str(transaction_id):
                    transaction = t
                    break

            if not transaction:
                messagebox.showerror("Error", f"Transaction with ID {transaction_id} not found")
                return
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get transaction details: {str(e)}")
            return

        # Check if transaction is already split
        if "(SPLIT)" in transaction.get('description', ''):
            messagebox.showwarning("Warning", "This transaction has already been split")
            return

        # Import split transaction dialog
        try:
            from view.split_transaction_dialog import SplitTransactionDialog

            # Show split transaction dialog
            dialog = SplitTransactionDialog(
                parent=self,
                db_path=self.transaction_manager.db_path,
                transaction_data=transaction,
                callback=self.refresh_after_split
            )
        except ImportError as e:
            messagebox.showerror("Error", f"Split transaction feature not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open split transaction dialog: {str(e)}")

    def refresh_after_split(self):
        """Refresh the transaction list after splitting"""
        self.load_transactions()
        self.update_summary()

    def edit_transaction(self):
        """Show dialog to edit the selected transaction"""
        # Get selected item
        selected = self.tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a transaction to edit")
            return

        # Get transaction ID
        transaction_id = self.tree.item(selected[0], "values")[0]

        # Get transaction details
        transaction = None
        try:
            # Get transactions from manager
            transactions = self.transaction_manager.get_transactions()

            # Find the selected transaction
            for t in transactions:
                if str(t['id']) == str(transaction_id):
                    transaction = t
                    break

            if not transaction:
                messagebox.showerror("Error", f"Transaction with ID {transaction_id} not found")
                return
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get transaction details: {str(e)}")
            return

        # Create a new toplevel window
        edit_window = tk.Toplevel(self)
        edit_window.title("Edit Transaction")
        edit_window.geometry("500x450")
        edit_window.transient(self)  # Set to be on top of the main window
        edit_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(edit_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Transaction type selection
        ttk.Label(frame, text="Transaction Type:", font=("Segoe UI", 11)).grid(row=0, column=0, sticky="w", pady=(0, 10))

        type_var = tk.StringVar(value=transaction.get('type', 'expense'))
        type_frame = ttk.Frame(frame)
        type_frame.grid(row=0, column=1, sticky="w", pady=(0, 10))

        ttk.Radiobutton(type_frame, text="Expense", variable=type_var, value="expense").pack(side="left", padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Income", variable=type_var, value="income").pack(side="left")

        # Date field with date picker
        ttk.Label(frame, text="Date:", font=("Segoe UI", 11)).grid(row=1, column=0, sticky="w", pady=(0, 10))

        # Parse the date
        try:
            transaction_date = datetime.datetime.strptime(transaction.get('date', ''), "%Y-%m-%d").date()
        except ValueError:
            transaction_date = datetime.date.today()

        date_picker = DatePicker(frame, initial_date=transaction_date)
        date_picker.grid(row=1, column=1, sticky="ew", pady=(0, 10))

        # Description field
        ttk.Label(frame, text="Description:", font=("Segoe UI", 11)).grid(row=2, column=0, sticky="w", pady=(0, 10))
        desc_var = tk.StringVar(value=transaction.get('description', ''))
        desc_entry = ttk.Entry(frame, textvariable=desc_var)
        desc_entry.grid(row=2, column=1, sticky="ew", pady=(0, 10))

        # Amount field
        ttk.Label(frame, text="Amount ($):", font=("Segoe UI", 11)).grid(row=3, column=0, sticky="w", pady=(0, 10))
        amount_var = tk.DoubleVar(value=transaction.get('amount', 0.0))
        amount_entry = ttk.Entry(frame, textvariable=amount_var)
        amount_entry.grid(row=3, column=1, sticky="ew", pady=(0, 10))

        # Category dropdown
        ttk.Label(frame, text="Category:", font=("Segoe UI", 11)).grid(row=4, column=0, sticky="w", pady=(0, 10))

        # Get categories from database
        category_model = Category(self.transaction_manager.db_path)
        categories = category_model.get_all_categories()

        # Create a dictionary to map category names to IDs
        self.category_map = {f"{cat['name']} ({cat['type']})": cat['id'] for cat in categories}
        category_names = list(self.category_map.keys())

        # Find the current category in the map
        current_category = None
        for name, cat_id in self.category_map.items():
            if cat_id == transaction.get('category_id'):
                current_category = name
                break

        category_var = tk.StringVar(value=current_category if current_category else (category_names[0] if category_names else ""))
        category_dropdown = ttk.Combobox(frame, textvariable=category_var, values=category_names, state="readonly")
        category_dropdown.grid(row=4, column=1, sticky="ew", pady=(0, 10))

        # Account dropdown
        ttk.Label(frame, text="Account:", font=("Segoe UI", 11)).grid(row=5, column=0, sticky="w", pady=(0, 10))

        # Get accounts from database
        account_model = Account(self.transaction_manager.db_path)
        accounts = account_model.get_all_accounts()

        # Create a dictionary to map account names to IDs
        self.account_map = {f"{acc['name']} ({acc['type']})": acc['id'] for acc in accounts}
        account_names = list(self.account_map.keys())

        # Find the current account in the map
        current_account = None
        for name, acc_id in self.account_map.items():
            if acc_id == transaction.get('account_id'):
                current_account = name
                break

        account_var = tk.StringVar(value=current_account if current_account else (account_names[0] if account_names else ""))
        account_dropdown = ttk.Combobox(frame, textvariable=account_var, values=account_names, state="readonly")
        account_dropdown.grid(row=5, column=1, sticky="ew", pady=(0, 10))

        # Notes field
        ttk.Label(frame, text="Notes:", font=("Segoe UI", 11)).grid(row=6, column=0, sticky="nw", pady=(0, 10))
        notes_var = tk.StringVar(value=transaction.get('notes', ''))
        notes_entry = ttk.Entry(frame, textvariable=notes_var)
        notes_entry.grid(row=6, column=1, sticky="ew", pady=(0, 10))

        # Error message label
        error_var = tk.StringVar()
        error_label = ttk.Label(frame, textvariable=error_var, foreground="red")
        error_label.grid(row=7, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=8, column=0, columnspan=2, pady=(10, 0))

        def save_transaction():
            # Clear previous error
            error_var.set("")

            # Validate inputs
            try:
                # Get values
                transaction_type = type_var.get()
                date = date_picker.get_date().strftime("%Y-%m-%d")
                description = desc_var.get().strip()
                amount = amount_var.get()
                category_id = self.category_map.get(category_var.get())
                account_id = self.account_map.get(account_var.get())

                # Validate required fields
                if not description:
                    error_var.set("Description is required")
                    desc_entry.focus_set()
                    return

                if amount <= 0:
                    error_var.set("Amount must be greater than zero")
                    amount_entry.focus_set()
                    return

                if not category_id:
                    error_var.set("Category is required")
                    category_dropdown.focus_set()
                    return

                if not account_id:
                    error_var.set("Account is required")
                    account_dropdown.focus_set()
                    return

                # Update transaction
                self.transaction_manager.edit_transaction(
                    transaction_id=transaction_id,
                    date=date,
                    amount=amount,
                    account_id=account_id,
                    description=description,
                    category_id=category_id,
                    type_name=transaction_type
                )

                # Close window and refresh
                edit_window.destroy()
                self.load_transactions()
                self.update_summary()

                self.status_var.set(f"Updated transaction {transaction_id}")

            except Exception as e:
                error_var.set(f"Error: {str(e)}")

        save_button = ttk.Button(button_frame, text="Save", command=save_transaction, bootstyle=SUCCESS)
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel", command=edit_window.destroy, bootstyle=DANGER)
        cancel_button.pack(side="left")

        # Configure grid column weights
        frame.columnconfigure(1, weight=1)

        # Set focus to description field
        desc_entry.focus_set()

    def delete_transaction(self):
        """Delete the selected transaction"""
        # Get selected item
        selected = self.tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a transaction to delete")
            return

        # Get transaction ID and description
        transaction_id = self.tree.item(selected[0], "values")[0]
        description = self.tree.item(selected[0], "values")[2]

        # Confirm deletion
        if not messagebox.askyesno("Confirm", f"Are you sure you want to delete the transaction '{description}'?\n\nThis action cannot be undone."):
            return

        try:
            # Delete transaction
            self.transaction_manager.delete_transaction(transaction_id)

            # Refresh transaction list
            self.load_transactions()
            self.update_summary()

            # Show success message
            self.status_var.set(f"Deleted transaction {transaction_id}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete transaction: {str(e)}")

    def import_file(self):
        """Import transactions from a file"""
        messagebox.showinfo("Import File", "This feature is not yet implemented.")

    def generate_report(self):
        """Generate a report of transactions"""
        messagebox.showinfo("Generate Report", "This feature is not yet implemented.")

    def share_data(self):
        """Share transaction data"""
        messagebox.showinfo("Share Data", "This feature is not yet implemented.")

    def toggle_selection(self, event):
        """Toggle the selection state of a row when clicking on the checkbox column"""
        # Get the item and column that was clicked
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)

        # Only process clicks on the checkbox column (column #1)
        if item and column == "#1":
            # Toggle the selection state
            current_state = self.selected_items.get(item, False)
            new_state = not current_state
            self.selected_items[item] = new_state

            # Update the checkbox display
            values = list(self.tree.item(item, "values"))
            values[0] = "☑" if new_state else "☐"
            self.tree.item(item, values=values)

            # Don't change the selection
            return "break"

    def toggle_selected_rows(self):
        """Toggle the selection state of all selected rows"""
        for item in self.tree.selection():
            # Toggle the selection state
            current_state = self.selected_items.get(item, False)
            new_state = not current_state
            self.selected_items[item] = new_state

            # Update the checkbox display
            values = list(self.tree.item(item, "values"))
            values[0] = "☑" if new_state else "☐"
            self.tree.item(item, values=values)

    def select_all_rows(self):
        """Select all rows in the treeview"""
        for item in self.tree.get_children():
            # Set selection state to True
            self.selected_items[item] = True

            # Update the checkbox display
            values = list(self.tree.item(item, "values"))
            values[0] = "☑"
            self.tree.item(item, values=values)

    def show_context_menu(self, event):
        """Show the context menu on right-click"""
        # Select the item under the cursor
        item = self.tree.identify_row(event.y)
        if item:
            # Select the item
            self.tree.selection_set(item)
            # Show the context menu
            self.context_menu.post(event.x_root, event.y_root)

    def cut_transaction(self):
        """Cut the selected transaction to clipboard"""
        # Get selected items
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("Information", "Please select a transaction to cut")
            return

        # Store in clipboard
        self.clipboard_transaction = self.get_transaction_data(selected[0])
        self.clipboard_action = "cut"

        # Mark for deletion
        self.tree.item(selected[0], tags=("cut",))
        self.tree.tag_configure("cut", background="#ffcccc")

        self.status_var.set("Transaction cut to clipboard. Use Paste to complete the operation.")

    def copy_transaction(self):
        """Copy the selected transaction to clipboard"""
        # Get selected items
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("Information", "Please select a transaction to copy")
            return

        # Store in clipboard
        self.clipboard_transaction = self.get_transaction_data(selected[0])
        self.clipboard_action = "copy"

        self.status_var.set("Transaction copied to clipboard")

    def paste_transaction(self):
        """Paste the transaction from clipboard"""
        if not hasattr(self, 'clipboard_transaction') or not self.clipboard_transaction:
            messagebox.showinfo("Information", "No transaction in clipboard")
            return

        if self.clipboard_action == "cut":
            # Delete the original transaction if it was cut
            for item in self.tree.get_children():
                if self.tree.item(item, "tags") and "cut" in self.tree.item(item, "tags"):
                    transaction_id = self.tree.item(item, "values")[1]  # ID is in the second column
                    self.transaction_manager.delete_transaction(transaction_id)

        # Create a new transaction with the clipboard data
        self.add_transaction_from_data(self.clipboard_transaction)

        # Refresh the view
        self.load_transactions()
        self.update_summary()

        self.status_var.set("Transaction pasted successfully")

    def get_transaction_data(self, item_id):
        """Get transaction data from the treeview item"""
        values = self.tree.item(item_id, "values")
        transaction_id = values[1]  # ID is in the second column

        # Get full transaction data from the database
        return self.transaction_manager.get_transaction(transaction_id)

    def add_transaction_from_data(self, transaction_data):
        """Add a new transaction based on the provided data"""
        # Create a copy of the transaction
        self.transaction_manager.add_transaction(
            date=transaction_data['date'],
            amount=transaction_data['amount'],
            account_id=transaction_data['account_id'],
            description=transaction_data['description'],
            category_id=transaction_data['category_id'],
            type_name=transaction_data['type'],
            reconciled=transaction_data['reconciled']
        )

    def insert_line(self):
        """Insert a new transaction line"""
        # This is essentially the same as add_transaction
        self.add_transaction()

    def undo_line(self):
        """Undo the last transaction change"""
        messagebox.showinfo("Information", "Undo functionality not implemented yet")
        # In a real implementation, this would restore the transaction to its previous state

    def spell_check(self):
        """Run spell check on transaction descriptions"""
        messagebox.showinfo("Information", "Spell check functionality not implemented yet")
        # In a real implementation, this would check spelling in the description field

    def sort_transactions(self):
        """Sort transactions alphabetically by description"""
        # Reload transactions with different sort order
        self.load_transactions()
        self.status_var.set("Transactions sorted by date")

    def find_transaction(self):
        """Find a transaction by search criteria"""
        # This is essentially using the existing search functionality
        search_text = self.search_var.get()
        if not search_text:
            # Show a dialog to enter search text
            search_text = simpledialog.askstring("Find Transaction", "Enter search text:")
            if search_text:
                self.search_var.set(search_text)
                self.filter_transactions()

    def merge_transactions(self):
        """Merge selected transactions"""
        # Get selected items
        selected = [item for item, is_selected in self.selected_items.items() if is_selected]

        if len(selected) < 2:
            messagebox.showinfo("Information", "Please select at least two transactions to merge")
            return

        # In a real implementation, this would merge the selected transactions
        messagebox.showinfo("Information", "Merge functionality not implemented yet")

    def handle_double_click(self, event):
        """Handle double-click on a transaction row"""
        # Get the item and column that was clicked
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)

        if not item:
            return

        # If clicked on the category column, open category editor
        if column == "#6":  # Category column
            self.set_category()
        else:
            # Otherwise, open the full transaction editor
            self.edit_transaction()

    def set_category(self):
        """Set the category for selected transactions"""
        # Get selected items
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("Information", "Please select at least one transaction")
            return

        # Get categories for dropdown
        category_model = Category(self.transaction_manager.db_path)
        categories = category_model.get_all_categories()
        category_names = [cat["name"] for cat in categories]
        category_map = {cat["name"]: cat["id"] for cat in categories}

        # Create a dialog for category selection
        dialog = tk.Toplevel(self)
        dialog.title("Set Category")
        dialog.geometry("400x300")
        dialog.transient(self)  # Set to be on top of the main window
        dialog.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(dialog, padding=20)
        frame.pack(fill="both", expand=True)

        # Header
        header_label = ttk.Label(frame, text="Set Transaction Category",
                                font=("Segoe UI", 14, "bold"))
        header_label.pack(pady=(0, 15))

        # Selected transactions count
        count_label = ttk.Label(frame,
                               text=f"Selected Transactions: {len(selected)}")
        count_label.pack(pady=(0, 15))

        # Category selection
        ttk.Label(frame, text="Category:").pack(anchor="w", pady=(0, 5))

        # Category variable
        category_var = tk.StringVar()

        # Try to get the current category of the first selected item
        if selected:
            values = self.tree.item(selected[0], "values")
            if len(values) > 5:  # Make sure we have enough values
                current_category = values[5]  # Category is at index 5
                if current_category in category_names:
                    category_var.set(current_category)

        # Category dropdown
        category_combo = ttk.Combobox(frame, textvariable=category_var,
                                     values=category_names, width=30)
        category_combo.pack(fill="x", pady=(0, 15))

        # Add new category option
        add_frame = ttk.Frame(frame)
        add_frame.pack(fill="x", pady=(0, 15))

        ttk.Label(add_frame, text="Or add new category:").pack(side="left", padx=(0, 5))

        new_category_var = tk.StringVar()
        new_category_entry = ttk.Entry(add_frame, textvariable=new_category_var, width=20)
        new_category_entry.pack(side="left", fill="x", expand=True)

        # Error message label
        error_var = tk.StringVar()
        error_label = ttk.Label(frame, textvariable=error_var, bootstyle="danger")
        error_label.pack(fill="x", pady=(0, 10))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x", pady=(10, 0))

        def apply_category():
            # Check if we're using an existing category or creating a new one
            category_name = category_var.get()
            new_category = new_category_var.get().strip()

            if new_category:
                # Create a new category
                try:
                    category_id = category_model.add_category(new_category, "expense")
                    category_name = new_category
                except Exception as e:
                    error_var.set(f"Error creating category: {str(e)}")
                    return
            elif not category_name:
                error_var.set("Please select a category or enter a new one")
                return
            else:
                # Get the ID of the selected category
                category_id = category_map.get(category_name)
                if not category_id:
                    error_var.set(f"Category '{category_name}' not found")
                    return

            # Update each selected transaction
            success_count = 0
            for item_id in selected:
                try:
                    # Get the transaction ID from the treeview
                    transaction_id = self.tree.item(item_id, "values")[1]

                    # Update the transaction in the database
                    self.transaction_manager.edit_transaction(
                        transaction_id,
                        {"category_id": category_id}
                    )
                    success_count += 1
                except Exception as e:
                    print(f"Error updating transaction {transaction_id}: {e}")

            # Close dialog and refresh
            dialog.destroy()
            self.load_transactions()

            # Show success message
            messagebox.showinfo("Success", f"Updated {success_count} transactions with category '{category_name}'")

        apply_button = ttk.Button(button_frame, text="Apply", command=apply_category,
                                 bootstyle=SUCCESS, width=10)
        apply_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel", command=dialog.destroy,
                                  bootstyle=SECONDARY, width=10)
        cancel_button.pack(side="left")

        # Focus the category dropdown
        category_combo.focus()

    def mark_as_reconciled(self):
        """Mark the selected transaction as reconciled"""
        # Get selected item
        selected = self.tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a transaction to mark as reconciled")
            return

        # Get transaction ID
        transaction_id = self.tree.item(selected[0], "values")[0]

        try:
            # Mark as reconciled
            self.transaction_manager.mark_as_reconciled(transaction_id, 1)

            # Refresh transaction list
            self.load_transactions()

            # Show success message
            self.status_var.set(f"Marked transaction {transaction_id} as reconciled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to mark transaction as reconciled: {str(e)}")

    def mark_as_unreconciled(self):
        """Mark the selected transaction as unreconciled"""
        # Get selected item
        selected = self.tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a transaction to mark as unreconciled")
            return

        # Get transaction ID
        transaction_id = self.tree.item(selected[0], "values")[0]

        try:
            # Mark as unreconciled
            self.transaction_manager.mark_as_reconciled(transaction_id, 0)

            # Refresh transaction list
            self.load_transactions()

            # Show success message
            self.status_var.set(f"Marked transaction {transaction_id} as unreconciled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to mark transaction as unreconciled: {str(e)}")

    def manage_accounts(self):
        """Open the account management screen"""
        # Get the main application container
        main_app = self.parent

        # Create account management frame
        from view.account_management_frame import AccountManagementFrame
        account_key = f"account_management_{self.company_name}"

        if account_key in main_app.screens:
            # Remove existing account management frame
            main_app.remove_screen(account_key)

        # Create new account management frame
        account_frame = AccountManagementFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(account_key, account_frame)

        # Show account management frame
        main_app.show_screen(account_key)

    def return_to_company(self):
        """Return to company view"""
        # Get the main application container
        main_app = self.parent

        # Show company frame
        company_key = f"company_{self.company_name}"
        main_app.show_screen(company_key)

    def manage_categories(self):
        """Open the category management screen"""
        # Get the main application container
        main_app = self.parent

        # Create category management frame
        from view.category_management_frame import CategoryManagementFrame
        category_key = f"category_management_{self.company_name}"

        if category_key in main_app.screens:
            # Remove existing category management frame
            main_app.remove_screen(category_key)

        # Create new category management frame
        category_frame = CategoryManagementFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(category_key, category_frame)

        # Show category management frame
        main_app.show_screen(category_key)

    def open_account_dashboard(self):
        """Open the account dashboard"""
        # Get the main application container
        main_app = self.parent

        # Create account dashboard frame
        from view.account_dashboard_frame import AccountDashboardFrame
        dashboard_key = f"account_dashboard_{self.company_name}"

        if dashboard_key in main_app.screens:
            # Remove existing dashboard frame
            main_app.remove_screen(dashboard_key)

        # Create new dashboard frame
        dashboard_frame = AccountDashboardFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company,
            self.filter_by_account
        )
        main_app.add_screen(dashboard_key, dashboard_frame)

        # Show dashboard frame
        main_app.show_screen(dashboard_key)

    def open_journal_entry(self):
        """Open the journal entry screen"""
        # Get the main application container
        main_app = self.parent

        # Create journal entry frame
        from view.journal_entry_frame import JournalEntryFrame
        journal_key = f"journal_entry_{self.company_name}"

        if journal_key in main_app.screens:
            # Remove existing journal frame
            main_app.remove_screen(journal_key)

        # Create new journal frame
        journal_frame = JournalEntryFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(journal_key, journal_frame)

        # Show journal frame
        main_app.show_screen(journal_key)

    def filter_by_account(self, _, account_name):
        """Filter transactions by account and return to company view"""
        # Return to company view
        self.return_to_company()

        # Set account filter
        self.account_filter_var.set(account_name)

        # Apply filter
        self.filter_transactions()

    def reset_filters(self):
        """Reset all filters"""
        self.search_var.set("")
        self.from_date_var.set("")
        self.to_date_var.set("")
        self.account_filter_var.set("All Accounts")
        self.category_filter_var.set("All Categories")
        self.type_filter_var.set("All Types")
        self.status_filter_var.set("All")
        self.load_transactions()

    def quick_search(self, period):
        """Quick search for transactions in a specific period"""
        today = datetime.datetime.now()

        if period == "today":
            # Set date range to today
            date_str = today.strftime("%Y-%m-%d")
            self.from_date_var.set(date_str)
            self.to_date_var.set(date_str)

        elif period == "week":
            # Set date range to this week (Monday to Sunday)
            start_of_week = today - datetime.timedelta(days=today.weekday())
            end_of_week = start_of_week + datetime.timedelta(days=6)
            self.from_date_var.set(start_of_week.strftime("%Y-%m-%d"))
            self.to_date_var.set(end_of_week.strftime("%Y-%m-%d"))

        elif period == "month":
            # Set date range to this month
            start_of_month = today.replace(day=1)
            # Get last day of month
            if today.month == 12:
                end_of_month = today.replace(day=31)
            else:
                end_of_month = today.replace(month=today.month+1, day=1) - datetime.timedelta(days=1)
            self.from_date_var.set(start_of_month.strftime("%Y-%m-%d"))
            self.to_date_var.set(end_of_month.strftime("%Y-%m-%d"))

        # Apply the filter
        self.filter_transactions()

    def add_column_separators(self):
        """Add visual separators between columns in the treeview by styling"""
        # Use ttk styling to create a more defined look for the treeview
        style = ttk.Style()

        # Configure the Treeview to have grid lines
        style.configure("Custom.Treeview",
                       rowheight=25,
                       fieldbackground="white",
                       borderwidth=1,
                       relief="solid")

        # Configure the heading style with borders
        style.configure("Custom.Treeview.Heading",
                       font=("Segoe UI", 10, "bold"),
                       borderwidth=1,
                       relief="solid")

        # Apply the style to our treeview
        self.tree.configure(style="Custom.Treeview")

        # Enable the built-in display of grid lines in the treeview
        self.tree["show"] = "headings"  # Only show column headings, not tree indicators

    def open_reconciliation(self):
        """Open the reconciliation screen"""
        # Get the main application container
        main_app = self.parent

        # Create reconciliation frame
        from view.reconciliation_frame import ReconciliationFrame
        reconciliation_key = f"reconciliation_{self.company_name}"

        if reconciliation_key in main_app.screens:
            # Remove existing reconciliation frame
            main_app.remove_screen(reconciliation_key)

        # Create new reconciliation frame
        reconciliation_frame = ReconciliationFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(reconciliation_key, reconciliation_frame)

        # Show reconciliation frame
        main_app.show_screen(reconciliation_key)

    def open_split_management(self):
        """Open the split transaction management screen"""
        # Get the main application container
        main_app = self.parent

        # Create split transaction management frame
        from view.split_transaction_frame import SplitTransactionFrame
        split_mgmt_key = f"split_management_{self.company_name}"

        if split_mgmt_key in main_app.screens:
            # Remove existing split management frame
            main_app.remove_screen(split_mgmt_key)

        # Create new split management frame
        split_mgmt_frame = SplitTransactionFrame(
            main_app,
            self.transaction_manager.db_path,
            user_role=getattr(self, 'user_role', 'admin'),
            close_callback=lambda: self.return_to_company()
        )
        main_app.add_screen(split_mgmt_key, split_mgmt_frame)

        # Show split management frame
        main_app.show_screen(split_mgmt_key)

    def return_to_company(self):
        """Return to the company view from other screens"""
        main_app = self.parent
        company_key = f"company_{self.company_name}"
        main_app.show_screen(company_key)

    def open_import(self):
        """Open the import screen"""
        # Get the main application container
        main_app = self.parent

        # Create import frame
        from view.import_frame import ImportFrame
        import_key = f"import_{self.company_name}"

        if import_key in main_app.screens:
            # Remove existing import frame
            main_app.remove_screen(import_key)

        # Create new import frame
        import_frame = ImportFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(import_key, import_frame)

        # Show import frame
        main_app.show_screen(import_key)

    def open_charts(self):
        """Open the charts screen"""
        # Get the main application container
        main_app = self.parent

        # Create charts frame
        from view.charts_frame import ChartsFrame
        charts_key = f"charts_{self.company_name}"

        if charts_key in main_app.screens:
            # Remove existing charts frame
            main_app.remove_screen(charts_key)

        # Create new charts frame
        charts_frame = ChartsFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(charts_key, charts_frame)

        # Show charts frame
        main_app.show_screen(charts_key)

    def open_bills_dashboard(self):
        """Open the bills dashboard screen"""
        # Get the main application container
        main_app = self.parent

        # Create bills dashboard frame
        from view.bills_dashboard_frame import BillsDashboardFrame
        bills_key = f"bills_dashboard_{self.company_name}"

        if bills_key in main_app.screens:
            # Remove existing bills dashboard frame
            main_app.remove_screen(bills_key)

        # Create new bills dashboard frame
        bills_frame = BillsDashboardFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(bills_key, bills_frame)

        # Show bills dashboard frame
        main_app.show_screen(bills_key)

    def open_invoice_dashboard(self):
        """Open the invoice dashboard screen"""
        # Get the main application container
        main_app = self.parent

        # Create invoice dashboard frame
        from view.invoice_dashboard_frame import InvoiceDashboardFrame
        invoices_key = f"invoice_dashboard_{self.company_name}"

        if invoices_key in main_app.screens:
            # Remove existing invoice dashboard frame
            main_app.remove_screen(invoices_key)

        # Create new invoice dashboard frame
        invoices_frame = InvoiceDashboardFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(invoices_key, invoices_frame)

        # Show invoice dashboard frame
        main_app.show_screen(invoices_key)

    def open_reminder_settings(self):
        """Open the reminder settings screen"""
        # Get the main application container
        main_app = self.parent

        # Create reminder settings frame
        from view.reminder_settings_frame import ReminderSettingsFrame
        reminder_key = f"reminder_settings_{self.company_name}"

        if reminder_key in main_app.screens:
            # Remove existing reminder settings frame
            main_app.remove_screen(reminder_key)

        # Create new reminder settings frame
        reminder_frame = ReminderSettingsFrame(
            main_app,
            self.company_name,
            self.transaction_manager.db_path,
            self.return_to_company
        )
        main_app.add_screen(reminder_key, reminder_frame)

        # Show reminder settings frame
        main_app.show_screen(reminder_key)

    def open_reports(self):
        """Open the reports screen"""
        # Get the main application container
        main_app = self.parent

        # Try to use the app controller if available
        if hasattr(main_app, 'app_controller'):
            app_controller = main_app.app_controller
            app_controller.show_reports(self.company_name)
        else:
            # Direct implementation if app_controller is not available
            from view.reports_frame import ReportsFrame
            reports_key = f"reports_{self.company_name}"

            if reports_key in main_app.screens:
                # Remove existing reports frame
                main_app.remove_screen(reports_key)

            # Create new reports frame
            reports_frame = ReportsFrame(
                main_app,
                self.company_name,
                self.transaction_manager.db_path,
                self.return_to_company
            )
            main_app.add_screen(reports_key, reports_frame)

            # Show reports frame
            main_app.show_screen(reports_key)
