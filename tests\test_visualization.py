import os
import unittest
import sqlite3
import datetime
import tempfile
import shutil
import sys

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.visualization import ChartGenerator


class TestVisualization(unittest.TestCase):
    """Test cases for visualization module"""
    
    def setUp(self):
        """Set up test database with sample data"""
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, "test_visualization.db")
        
        # Create test database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute("""
        CREATE TABLE accounts (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            currency TEXT NOT NULL,
            opening_balance REAL NOT NULL,
            current_balance REAL NOT NULL
        )
        """)
        
        cursor.execute("""
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT
        )
        """)
        
        cursor.execute("""
        CREATE TABLE transactions (
            id INTEGER PRIMARY KEY,
            date TEXT NOT NULL,
            amount REAL NOT NULL,
            description TEXT,
            category_id INTEGER,
            account_id INTEGER NOT NULL,
            type TEXT NOT NULL,
            reconciled INTEGER DEFAULT 0,
            notes TEXT,
            FOREIGN KEY (category_id) REFERENCES categories (id),
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
        """)
        
        # Insert test data
        accounts = [
            (1, "Checking", "checking", "USD", 1000.00, 1500.00),
            (2, "Savings", "savings", "USD", 5000.00, 5500.00),
            (3, "Credit Card", "credit", "USD", 0.00, -500.00)
        ]
        
        for account in accounts:
            cursor.execute(
                "INSERT INTO accounts (id, name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?, ?)",
                account
            )
        
        categories = [
            (1, "Salary", "income"),
            (2, "Interest", "income"),
            (3, "Groceries", "expense"),
            (4, "Rent", "expense"),
            (5, "Utilities", "expense")
        ]
        
        for category in categories:
            cursor.execute(
                "INSERT INTO categories (id, name, type) VALUES (?, ?, ?)",
                category
            )
        
        # Create transactions spanning multiple months
        current_year = datetime.datetime.now().year
        
        # January transactions
        jan_transactions = [
            (f"{current_year}-01-05", 2000.00, "Monthly Salary", 1, 1, "income", 1),
            (f"{current_year}-01-10", 50.00, "Interest Income", 2, 2, "income", 1),
            (f"{current_year}-01-15", 200.00, "Grocery Shopping", 3, 1, "expense", 1),
            (f"{current_year}-01-20", 1000.00, "Rent Payment", 4, 1, "expense", 1),
            (f"{current_year}-01-25", 150.00, "Electricity Bill", 5, 1, "expense", 1)
        ]
        
        # February transactions
        feb_transactions = [
            (f"{current_year}-02-05", 2000.00, "Monthly Salary", 1, 1, "income", 1),
            (f"{current_year}-02-10", 55.00, "Interest Income", 2, 2, "income", 1),
            (f"{current_year}-02-15", 220.00, "Grocery Shopping", 3, 1, "expense", 1),
            (f"{current_year}-02-20", 1000.00, "Rent Payment", 4, 1, "expense", 1),
            (f"{current_year}-02-25", 160.00, "Electricity Bill", 5, 1, "expense", 1)
        ]
        
        # March transactions
        mar_transactions = [
            (f"{current_year}-03-05", 2200.00, "Monthly Salary", 1, 1, "income", 1),
            (f"{current_year}-03-10", 60.00, "Interest Income", 2, 2, "income", 1),
            (f"{current_year}-03-15", 210.00, "Grocery Shopping", 3, 1, "expense", 1),
            (f"{current_year}-03-20", 1000.00, "Rent Payment", 4, 1, "expense", 1),
            (f"{current_year}-03-25", 155.00, "Electricity Bill", 5, 1, "expense", 1)
        ]
        
        transactions = jan_transactions + feb_transactions + mar_transactions
        
        for transaction in transactions:
            cursor.execute(
                """INSERT INTO transactions 
                   (date, amount, description, category_id, account_id, type, reconciled) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                transaction
            )
        
        conn.commit()
        conn.close()
        
        # Create chart generator
        self.chart_generator = ChartGenerator(self.db_path)
    
    def tearDown(self):
        """Clean up test files"""
        shutil.rmtree(self.test_dir)
    
    def test_create_figure(self):
        """Test creating a figure"""
        figure = self.chart_generator.create_figure()
        self.assertIsNotNone(figure)
    
    def test_income_vs_expenses_bar_chart(self):
        """Test income vs expenses bar chart generation"""
        # Set date range
        current_year = datetime.datetime.now().year
        date_from = f"{current_year}-01-01"
        date_to = f"{current_year}-03-31"
        
        # Generate chart
        figure = self.chart_generator.income_vs_expenses_bar_chart(date_from, date_to)
        
        # Verify figure was created
        self.assertIsNotNone(figure)
        
        # Verify axes were created
        self.assertEqual(len(figure.axes), 1)
    
    def test_category_pie_chart(self):
        """Test category pie chart generation"""
        # Set date range
        current_year = datetime.datetime.now().year
        date_from = f"{current_year}-01-01"
        date_to = f"{current_year}-03-31"
        
        # Generate expense chart
        figure = self.chart_generator.category_pie_chart(date_from, date_to, 'expense')
        
        # Verify figure was created
        self.assertIsNotNone(figure)
        
        # Verify axes were created
        self.assertEqual(len(figure.axes), 1)
        
        # Generate income chart
        figure = self.chart_generator.category_pie_chart(date_from, date_to, 'income')
        
        # Verify figure was created
        self.assertIsNotNone(figure)
        
        # Verify axes were created
        self.assertEqual(len(figure.axes), 1)
    
    def test_monthly_trend_chart(self):
        """Test monthly trend chart generation"""
        # Set date range
        current_year = datetime.datetime.now().year
        date_from = f"{current_year}-01-01"
        date_to = f"{current_year}-03-31"
        
        # Generate chart
        figure = self.chart_generator.monthly_trend_chart(date_from, date_to)
        
        # Verify figure was created
        self.assertIsNotNone(figure)
        
        # Verify axes were created
        self.assertEqual(len(figure.axes), 1)
    
    def test_balance_history_chart(self):
        """Test balance history chart generation"""
        # Set date range
        current_year = datetime.datetime.now().year
        date_from = f"{current_year}-01-01"
        date_to = f"{current_year}-03-31"
        
        # Generate chart for all accounts
        figure = self.chart_generator.balance_history_chart(date_from, date_to)
        
        # Verify figure was created
        self.assertIsNotNone(figure)
        
        # Verify axes were created
        self.assertEqual(len(figure.axes), 1)
        
        # Generate chart for specific account
        figure = self.chart_generator.balance_history_chart(date_from, date_to, 1)
        
        # Verify figure was created
        self.assertIsNotNone(figure)
        
        # Verify axes were created
        self.assertEqual(len(figure.axes), 1)


if __name__ == "__main__":
    unittest.main()
