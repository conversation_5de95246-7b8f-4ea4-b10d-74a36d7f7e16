import unittest
import datetime
import tkinter as tk

from view.components.date_picker import DatePicker


class TestDatePicker(unittest.TestCase):
    """Test cases for the DatePicker component"""
    
    def setUp(self):
        """Set up test environment"""
        self.root = tk.Tk()
        self.date_picker = DatePicker(self.root)
    
    def tearDown(self):
        """Clean up test environment"""
        self.root.destroy()
    
    def test_initialization(self):
        """Test that the date picker initializes correctly"""
        # Default date should be today
        today = datetime.date.today()
        self.assertEqual(self.date_picker.selected_date, today)
        
        # Date entry should show today's date in YYYY-MM-DD format
        expected_date_str = today.strftime("%Y-%m-%d")
        self.assertEqual(self.date_picker.date_var.get(), expected_date_str)
    
    def test_initialization_with_date(self):
        """Test initialization with a specific date"""
        test_date = datetime.date(2025, 1, 15)
        date_picker = DatePicker(self.root, initial_date=test_date)
        
        self.assertEqual(date_picker.selected_date, test_date)
        self.assertEqual(date_picker.date_var.get(), "2025-01-15")
    
    def test_format_date(self):
        """Test date formatting"""
        test_date = datetime.date(2025, 12, 31)
        formatted = self.date_picker.format_date(test_date)
        
        self.assertEqual(formatted, "2025-12-31")
    
    def test_parse_date(self):
        """Test date parsing"""
        # Valid date
        parsed = self.date_picker.parse_date("2025-01-15")
        self.assertEqual(parsed, datetime.date(2025, 1, 15))
        
        # Invalid date
        parsed = self.date_picker.parse_date("invalid-date")
        self.assertIsNone(parsed)
    
    def test_validate_date(self):
        """Test date validation"""
        # Set an invalid date
        self.date_picker.date_var.set("invalid-date")
        
        # Validate should reset to the previously selected date
        previous_date = self.date_picker.selected_date
        self.date_picker.validate_date()
        
        self.assertEqual(self.date_picker.date_var.get(), self.date_picker.format_date(previous_date))
        
        # Set a valid date
        self.date_picker.date_var.set("2025-02-28")
        self.date_picker.validate_date()
        
        self.assertEqual(self.date_picker.selected_date, datetime.date(2025, 2, 28))
    
    def test_get_date(self):
        """Test getting the selected date"""
        # Set a date
        test_date = datetime.date(2025, 3, 15)
        self.date_picker.selected_date = test_date
        
        # Get the date
        result = self.date_picker.get_date()
        
        self.assertEqual(result, test_date)
    
    def test_set_date(self):
        """Test setting the date"""
        # Set a date
        test_date = datetime.date(2025, 4, 30)
        self.date_picker.set_date(test_date)
        
        # Check if date was set
        self.assertEqual(self.date_picker.selected_date, test_date)
        self.assertEqual(self.date_picker.date_var.get(), "2025-04-30")


if __name__ == "__main__":
    unittest.main()
