import tkinter as tk
from tkinter import messagebox, simpledialog
from datetime import datetime
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.budget_management import BudgetManagement
from model.account import Account


class BudgetManagementFrame(ttk.Frame):
    """Frame for managing budgets and budget vs actual analysis"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Budget Management - {company_name}"

        # Create models
        self.budget_model = BudgetManagement(self.db_path)
        self.account_model = Account(self.db_path)

        # Current budget data
        self.current_budget_year = datetime.now().year
        self.current_budget_period = "Annual"

        self.create_widgets()
        self.load_budget_data()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Budget selection frame
        selection_frame = ttk.LabelFrame(main_frame, text="Budget Selection", padding=10)
        selection_frame.pack(fill="x", pady=(0, 10))

        # Year selection
        ttk.Label(selection_frame, text="Budget Year:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.year_var = tk.IntVar(value=self.current_budget_year)
        year_spinbox = ttk.Spinbox(selection_frame, from_=2020, to=2030, textvariable=self.year_var, width=10)
        year_spinbox.grid(row=0, column=1, sticky="w", padx=(0, 10))

        # Period selection
        ttk.Label(selection_frame, text="Budget Period:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        self.period_var = tk.StringVar(value=self.current_budget_period)
        periods = ["Annual", "Q1", "Q2", "Q3", "Q4", "January", "February", "March", "April", "May", "June",
                  "July", "August", "September", "October", "November", "December"]
        period_combo = ttk.Combobox(selection_frame, textvariable=self.period_var, values=periods, state="readonly", width=12)
        period_combo.grid(row=0, column=3, sticky="w", padx=(0, 10))

        # Load button
        load_button = ttk.Button(
            selection_frame,
            text="Load Budget",
            command=self.load_budget_data,
            bootstyle=PRIMARY
        )
        load_button.grid(row=0, column=4, padx=10)

        # Action buttons
        actions_frame = ttk.Frame(selection_frame)
        actions_frame.grid(row=0, column=5, padx=20)

        new_button = ttk.Button(actions_frame, text="New Budget", command=self.create_new_budget, bootstyle=SUCCESS)
        new_button.pack(side="left", padx=2)

        copy_button = ttk.Button(actions_frame, text="Copy Budget", command=self.copy_budget, bootstyle=INFO)
        copy_button.pack(side="left", padx=2)

        # Budget data frame
        data_frame = ttk.LabelFrame(main_frame, text="Budget Details", padding=10)
        data_frame.pack(fill="both", expand=True)

        # Create notebook for different views
        self.budget_notebook = ttk.Notebook(data_frame)
        self.budget_notebook.pack(fill="both", expand=True)

        # Budget entries tab
        self.entries_frame = ttk.Frame(self.budget_notebook)
        self.budget_notebook.add(self.entries_frame, text="Budget Entries")

        # Budget vs Actual tab
        self.comparison_frame = ttk.Frame(self.budget_notebook)
        self.budget_notebook.add(self.comparison_frame, text="Budget vs Actual")

        # Summary tab
        self.summary_frame = ttk.Frame(self.budget_notebook)
        self.budget_notebook.add(self.summary_frame, text="Summary")

        self.create_budget_entries_widgets()
        self.create_comparison_widgets()
        self.create_summary_widgets()

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

    def create_budget_entries_widgets(self):
        """Create widgets for budget entries management"""
        # Toolbar
        toolbar_frame = ttk.Frame(self.entries_frame)
        toolbar_frame.pack(fill="x", pady=(0, 10))

        add_button = ttk.Button(toolbar_frame, text="Add Entry", command=self.add_budget_entry, bootstyle=SUCCESS)
        add_button.pack(side="left", padx=(0, 5))

        edit_button = ttk.Button(toolbar_frame, text="Edit Entry", command=self.edit_budget_entry, bootstyle=INFO)
        edit_button.pack(side="left", padx=5)

        delete_button = ttk.Button(toolbar_frame, text="Delete Entry", command=self.delete_budget_entry, bootstyle=DANGER)
        delete_button.pack(side="left", padx=5)

        # Budget entries treeview
        columns = ("category", "account", "budgeted_amount", "description")
        self.budget_tree = ttk.Treeview(self.entries_frame, columns=columns, show="headings", height=12)

        # Define headings
        self.budget_tree.heading("category", text="Category")
        self.budget_tree.heading("account", text="Account")
        self.budget_tree.heading("budgeted_amount", text="Budgeted Amount")
        self.budget_tree.heading("description", text="Description")

        # Define columns
        self.budget_tree.column("category", width=150)
        self.budget_tree.column("account", width=200)
        self.budget_tree.column("budgeted_amount", width=120, anchor="e")
        self.budget_tree.column("description", width=200)

        # Add scrollbar
        budget_scrollbar = ttk.Scrollbar(self.entries_frame, orient="vertical", command=self.budget_tree.yview)
        self.budget_tree.configure(yscrollcommand=budget_scrollbar.set)

        self.budget_tree.pack(side="left", fill="both", expand=True)
        budget_scrollbar.pack(side="right", fill="y")

        # Bind double-click to edit
        self.budget_tree.bind("<Double-1>", lambda e: self.edit_budget_entry())

    def create_comparison_widgets(self):
        """Create widgets for budget vs actual comparison"""
        # Comparison controls
        controls_frame = ttk.Frame(self.comparison_frame)
        controls_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(controls_frame, text="Actual Period:").pack(side="left", padx=(0, 5))
        
        # Date range for actual data
        self.actual_start_var = tk.StringVar(value=f"{self.current_budget_year}-01-01")
        start_entry = ttk.Entry(controls_frame, textvariable=self.actual_start_var, width=12)
        start_entry.pack(side="left", padx=(0, 5))

        ttk.Label(controls_frame, text="to").pack(side="left", padx=5)

        self.actual_end_var = tk.StringVar(value=f"{self.current_budget_year}-12-31")
        end_entry = ttk.Entry(controls_frame, textvariable=self.actual_end_var, width=12)
        end_entry.pack(side="left", padx=(0, 10))

        compare_button = ttk.Button(controls_frame, text="Generate Comparison", 
                                   command=self.generate_comparison, bootstyle=PRIMARY)
        compare_button.pack(side="left", padx=10)

        # Comparison results treeview
        comp_columns = ("category", "budget", "actual", "variance", "variance_pct")
        self.comparison_tree = ttk.Treeview(self.comparison_frame, columns=comp_columns, show="headings", height=12)

        # Define headings
        self.comparison_tree.heading("category", text="Category")
        self.comparison_tree.heading("budget", text="Budget")
        self.comparison_tree.heading("actual", text="Actual")
        self.comparison_tree.heading("variance", text="Variance")
        self.comparison_tree.heading("variance_pct", text="Variance %")

        # Define columns
        self.comparison_tree.column("category", width=150)
        self.comparison_tree.column("budget", width=120, anchor="e")
        self.comparison_tree.column("actual", width=120, anchor="e")
        self.comparison_tree.column("variance", width=120, anchor="e")
        self.comparison_tree.column("variance_pct", width=100, anchor="e")

        # Add scrollbar
        comp_scrollbar = ttk.Scrollbar(self.comparison_frame, orient="vertical", command=self.comparison_tree.yview)
        self.comparison_tree.configure(yscrollcommand=comp_scrollbar.set)

        self.comparison_tree.pack(side="left", fill="both", expand=True)
        comp_scrollbar.pack(side="right", fill="y")

    def create_summary_widgets(self):
        """Create widgets for budget summary"""
        self.summary_text = tk.Text(self.summary_frame, wrap=tk.WORD, font=("Consolas", 10))
        summary_scrollbar = ttk.Scrollbar(self.summary_frame, orient="vertical", command=self.summary_text.yview)
        self.summary_text.configure(yscrollcommand=summary_scrollbar.set)
        
        self.summary_text.pack(side="left", fill="both", expand=True)
        summary_scrollbar.pack(side="right", fill="y")

    def load_budget_data(self):
        """Load budget data for the selected year and period"""
        try:
            year = self.year_var.get()
            period = self.period_var.get()
            
            # Clear existing data
            for item in self.budget_tree.get_children():
                self.budget_tree.delete(item)
            
            # Load budget entries
            budget_entries = self.budget_model.get_budget_by_period(year, period)
            
            for entry in budget_entries:
                self.budget_tree.insert(
                    "", "end",
                    values=(
                        entry['category'],
                        entry['account_name'] or "General",
                        f"${entry['budgeted_amount']:,.2f}",
                        entry['description'] or ""
                    ),
                    tags=(str(entry['id']),)
                )
            
            # Update summary
            self.update_summary()
            
            self.status_var.set(f"Loaded {len(budget_entries)} budget entries for {period} {year}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load budget data: {str(e)}")
            self.status_var.set("Error loading budget data")

    def add_budget_entry(self):
        """Add a new budget entry"""
        dialog = BudgetEntryDialog(self, "Add Budget Entry", self.account_model)
        if dialog.result:
            try:
                entry_id = self.budget_model.create_budget(
                    budget_name=f"{self.period_var.get()} {self.year_var.get()} Budget",
                    budget_year=self.year_var.get(),
                    budget_period=self.period_var.get(),
                    category=dialog.result['category'],
                    budgeted_amount=dialog.result['amount'],
                    account_id=dialog.result['account_id'],
                    description=dialog.result['description'],
                    created_by="admin"
                )
                
                if entry_id:
                    self.load_budget_data()
                    messagebox.showinfo("Success", "Budget entry added successfully")
                else:
                    messagebox.showerror("Error", "Failed to add budget entry")
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add budget entry: {str(e)}")

    def edit_budget_entry(self):
        """Edit the selected budget entry"""
        selection = self.budget_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a budget entry to edit")
            return
        
        # Get budget ID from tags
        item = selection[0]
        budget_id = int(self.budget_tree.item(item)['tags'][0])
        
        # Get current values
        values = self.budget_tree.item(item)['values']
        current_amount = float(values[2].replace('$', '').replace(',', ''))
        current_description = values[3]
        
        # Show edit dialog
        new_amount = simpledialog.askfloat("Edit Budget Entry", "Enter new budgeted amount:", 
                                          initialvalue=current_amount, minvalue=0)
        if new_amount is not None:
            new_description = simpledialog.askstring("Edit Budget Entry", "Enter description:", 
                                                    initialvalue=current_description)
            
            try:
                if self.budget_model.update_budget(budget_id, new_amount, new_description or ""):
                    self.load_budget_data()
                    messagebox.showinfo("Success", "Budget entry updated successfully")
                else:
                    messagebox.showerror("Error", "Failed to update budget entry")
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update budget entry: {str(e)}")

    def delete_budget_entry(self):
        """Delete the selected budget entry"""
        selection = self.budget_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a budget entry to delete")
            return
        
        if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this budget entry?"):
            try:
                item = selection[0]
                budget_id = int(self.budget_tree.item(item)['tags'][0])
                
                if self.budget_model.delete_budget(budget_id):
                    self.load_budget_data()
                    messagebox.showinfo("Success", "Budget entry deleted successfully")
                else:
                    messagebox.showerror("Error", "Failed to delete budget entry")
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete budget entry: {str(e)}")

    def create_new_budget(self):
        """Create a new budget for a different period"""
        # Implementation for creating a new budget
        messagebox.showinfo("Info", "New budget creation feature coming soon!")

    def copy_budget(self):
        """Copy budget from another period"""
        # Implementation for copying budget
        messagebox.showinfo("Info", "Budget copying feature coming soon!")

    def generate_comparison(self):
        """Generate budget vs actual comparison"""
        try:
            year = self.year_var.get()
            period = self.period_var.get()
            start_date = self.actual_start_var.get()
            end_date = self.actual_end_var.get()
            
            # Clear existing comparison data
            for item in self.comparison_tree.get_children():
                self.comparison_tree.delete(item)
            
            # Get comparison data
            comparison_data = self.budget_model.get_budget_vs_actual_data(year, period, start_date, end_date)
            
            for item in comparison_data:
                variance_color = "red" if item['variance'] < 0 else "green"
                
                self.comparison_tree.insert(
                    "", "end",
                    values=(
                        item['category'],
                        f"${item['budget_amount']:,.2f}",
                        f"${item['actual_amount']:,.2f}",
                        f"${item['variance']:,.2f}",
                        f"{item['variance_percentage']:,.1f}%"
                    ),
                    tags=(variance_color,)
                )
            
            # Configure colors
            self.comparison_tree.tag_configure("red", foreground="red")
            self.comparison_tree.tag_configure("green", foreground="green")
            
            self.status_var.set(f"Generated budget vs actual comparison for {len(comparison_data)} categories")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate comparison: {str(e)}")

    def update_summary(self):
        """Update the budget summary"""
        try:
            year = self.year_var.get()
            period = self.period_var.get()
            
            summary_data = self.budget_model.get_budget_summary_by_category(year, period)
            
            self.summary_text.delete(1.0, tk.END)
            
            summary_text = f"""
Budget Summary for {period} {year}

Category Breakdown:
"""
            total_budget = 0
            for item in summary_data:
                summary_text += f"{item['category']:<20} ${item['total_budget']:>10,.2f} ({item['item_count']} items)\n"
                total_budget += item['total_budget']
            
            summary_text += f"\n{'Total Budget:':<20} ${total_budget:>10,.2f}"
            
            self.summary_text.insert(1.0, summary_text)
            
        except Exception as e:
            print(f"Error updating summary: {e}")


class BudgetEntryDialog:
    """Dialog for adding/editing budget entries"""
    
    def __init__(self, parent, title, account_model):
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # Create form
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Category
        ttk.Label(main_frame, text="Category:").grid(row=0, column=0, sticky="w", pady=5)
        self.category_var = tk.StringVar()
        categories = ["Revenue", "Expense", "Asset", "Liability", "Equity"]
        category_combo = ttk.Combobox(main_frame, textvariable=self.category_var, values=categories, state="readonly")
        category_combo.grid(row=0, column=1, sticky="ew", pady=5)
        
        # Account
        ttk.Label(main_frame, text="Account:").grid(row=1, column=0, sticky="w", pady=5)
        self.account_var = tk.StringVar()
        accounts = account_model.get_all_accounts()
        account_names = ["General"] + [acc['name'] for acc in accounts]
        self.account_map = {"General": None}
        self.account_map.update({acc['name']: acc['id'] for acc in accounts})
        account_combo = ttk.Combobox(main_frame, textvariable=self.account_var, values=account_names, state="readonly")
        account_combo.grid(row=1, column=1, sticky="ew", pady=5)
        
        # Amount
        ttk.Label(main_frame, text="Budgeted Amount:").grid(row=2, column=0, sticky="w", pady=5)
        self.amount_var = tk.DoubleVar()
        amount_entry = ttk.Entry(main_frame, textvariable=self.amount_var)
        amount_entry.grid(row=2, column=1, sticky="ew", pady=5)
        
        # Description
        ttk.Label(main_frame, text="Description:").grid(row=3, column=0, sticky="w", pady=5)
        self.description_var = tk.StringVar()
        description_entry = ttk.Entry(main_frame, textvariable=self.description_var)
        description_entry.grid(row=3, column=1, sticky="ew", pady=5)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ok_button = ttk.Button(button_frame, text="OK", command=self.ok_clicked, bootstyle=PRIMARY)
        ok_button.pack(side="left", padx=5)
        
        cancel_button = ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked, bootstyle=SECONDARY)
        cancel_button.pack(side="left", padx=5)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        
        # Set defaults
        self.category_var.set("Expense")
        self.account_var.set("General")
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def ok_clicked(self):
        """Handle OK button click"""
        if not self.category_var.get():
            messagebox.showerror("Error", "Please select a category")
            return
        
        if self.amount_var.get() <= 0:
            messagebox.showerror("Error", "Please enter a valid amount")
            return
        
        self.result = {
            'category': self.category_var.get(),
            'account_id': self.account_map.get(self.account_var.get()),
            'amount': self.amount_var.get(),
            'description': self.description_var.get()
        }
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.dialog.destroy()
