# Cashbook Application Distribution

This package contains everything needed to build and distribute the Cashbook application as a standalone Windows executable with an installer.

## Quick Start

1. Run `setup_and_build.bat` to install dependencies, build the executable, and create the installer.
2. The installer will be created in the `Output` directory as `Cashbook_Setup.exe`.
3. Distribute `Cashbook_Setup.exe` to your clients.

## Files Included

- `setup_and_build.bat` - Main script to install dependencies and build the application
- `create_icon.py` - <PERSON><PERSON><PERSON> to create the application icon
- `cashbook.spec` - PyInstaller specification file
- `cashbook_installer.iss` - Inno Setup script for creating the installer
- `DISTRIBUTION_GUIDE.md` - Detailed guide for building and distributing the application
- `requirements.txt` - List of required Python packages

## System Requirements

### For Building
- Windows 10 or later
- Python 3.8 or later
- Inno Setup 6 or later

### For Running
- Windows 10 or later
- No additional software required (Python not needed)

## Default Login Credentials

When prompted to log in, use these default credentials:
- **Username:** admin
- **Password:** admin

## Support

If you encounter any issues with the distribution process, please refer to the `DISTRIBUTION_GUIDE.md` file for detailed troubleshooting information.
