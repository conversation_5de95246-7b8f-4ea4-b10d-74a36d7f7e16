import hashlib
import sqlite3

from model.base_model import BaseModel


class User(BaseModel):
    """
    Model class for users
    """
    def __init__(self, db_path, id=None, username=None, password=None, role=None):
        super().__init__(db_path)
        self.id = id
        self.username = username
        self.password = password  # Plain text password (temporary)
        self.role = role

    def table_name(self):
        return "users"

    def fields(self):
        return ["id", "username", "password", "role"]

    def primary_key(self):
        return "id"

    def validate(self):
        """Validate user data"""
        if not self.username:
            raise ValueError("Username is required")

        if not self.password and not self.id:
            raise ValueError("Password is required")

        if self.role not in ["Admin", "Client"]:
            raise ValueError("Role must be 'Admin' or 'Client'")

        # Check if username already exists
        if not self.id:  # Only check for new users
            conn = None
            try:
                conn = self._get_connection()
                cursor = conn.cursor()

                cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (self.username,))
                count = cursor.fetchone()[0]

                if count > 0:
                    raise ValueError(f"Username '{self.username}' already exists")
            finally:
                if conn:
                    conn.close()

    def save(self):
        """Save the user to the database"""
        self.validate()

        # Hash the password if it's not already hashed
        if self.password and not self.id:
            hashed_password = hashlib.sha256(self.password.encode()).hexdigest()
        else:
            hashed_password = self.password

        data = {
            "username": self.username,
            "role": self.role
        }

        if hashed_password:
            data["password"] = hashed_password

        if self.id:
            # Update existing user
            return self.update(self.id, data)
        else:
            # Create new user
            self.id = self.create(data)
            return self.id is not None

    def change_password(self, new_password):
        """Change the user's password"""
        if not self.id:
            raise ValueError("User must be saved before changing password")

        if not new_password:
            raise ValueError("New password is required")

        hashed_password = hashlib.sha256(new_password.encode()).hexdigest()

        return self.update(self.id, {"password": hashed_password})

    def delete(self):
        """Delete the user, with special handling for admin users"""
        if not self.id:
            return False

        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Check if this is an admin user
            cursor.execute("SELECT role FROM users WHERE id = ?", (self.id,))
            user_role = cursor.fetchone()[0]

            if user_role == "Admin":
                # Check if this is the last admin
                cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'Admin'")
                admin_count = cursor.fetchone()[0]

                if admin_count <= 1:
                    raise ValueError("Cannot delete the last admin user")

            # Delete the user
            cursor.execute("DELETE FROM users WHERE id = ?", (self.id,))
            conn.commit()

            return True
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting user: {str(e)}")
        finally:
            if conn:
                conn.close()

    @classmethod
    def authenticate(cls, db_path, username, password):
        """
        Authenticate a user with username and password

        Returns:
            User object if authentication successful, None otherwise
        """
        model = cls(db_path)

        try:
            conn = model._get_connection()
            cursor = conn.cursor()

            # Hash the password
            hashed_password = hashlib.sha256(password.encode()).hexdigest()

            cursor.execute(
                "SELECT id, username, role FROM users WHERE username = ? AND password = ?",
                (username, hashed_password)
            )

            result = cursor.fetchone()

            if result:
                # Create and return a user object
                user = cls(
                    db_path,
                    id=result[0],
                    username=result[1],
                    role=result[2]
                )
                return user

            return None
        except sqlite3.Error as e:
            raise Exception(f"Authentication error: {str(e)}")
        finally:
            if conn:
                conn.close()

    @classmethod
    def get_by_username(cls, db_path, username):
        """Get a user by username"""
        model = cls(db_path)

        try:
            conn = model._get_connection()
            cursor = conn.cursor()

            cursor.execute(
                "SELECT id, username, role FROM users WHERE username = ?",
                (username,)
            )

            result = cursor.fetchone()

            if result:
                # Create and return a user object
                user = cls(
                    db_path,
                    id=result[0],
                    username=result[1],
                    role=result[2]
                )
                return user

            return None
        except sqlite3.Error as e:
            raise Exception(f"Error fetching user: {str(e)}")
        finally:
            if conn:
                conn.close()