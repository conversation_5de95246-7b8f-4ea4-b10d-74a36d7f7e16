import sqlite3
from datetime import datetime

from model.base_model import BaseModel


class JournalEntry(BaseModel):
    """Model for managing journal entries with multiple transaction lines"""

    def __init__(self, db_path):
        super().__init__(db_path)
        self._table_name = "journal_entries"
        self._create_tables()

    def table_name(self):
        return self._table_name

    def fields(self):
        return [
            'id', 'entry_number', 'date', 'description', 'reference',
            'total_debits', 'total_credits', 'is_balanced', 'created_by',
            'tags', 'created_at', 'updated_at'
        ]

    def primary_key(self):
        return 'id'

    def _create_tables(self):
        """Create journal entries and journal lines tables"""
        # Create journal entries table
        journal_entries_query = '''
        CREATE TABLE IF NOT EXISTS journal_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            entry_number TEXT UNIQUE NOT NULL,
            date TEXT NOT NULL,
            description TEXT NOT NULL,
            reference TEXT,
            total_debits REAL NOT NULL DEFAULT 0.0,
            total_credits REAL NOT NULL DEFAULT 0.0,
            is_balanced BOOLEAN NOT NULL DEFAULT 0,
            created_by TEXT,
            tags TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.execute_query(journal_entries_query)

        # Create journal lines table
        journal_lines_query = '''
        CREATE TABLE IF NOT EXISTS journal_lines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            journal_entry_id INTEGER NOT NULL,
            line_number INTEGER NOT NULL,
            account_id INTEGER NOT NULL,
            description TEXT,
            debit_amount REAL DEFAULT 0.0,
            credit_amount REAL DEFAULT 0.0,
            category_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
            FOREIGN KEY (account_id) REFERENCES accounts (id),
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        '''
        self.execute_query(journal_lines_query)

        # Create index for better performance
        index_query = '''
        CREATE INDEX IF NOT EXISTS idx_journal_lines_entry_id
        ON journal_lines (journal_entry_id)
        '''
        self.execute_query(index_query)

    def generate_entry_number(self):
        """Generate a unique journal entry number"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get the current year
            current_year = datetime.now().year

            # Find the highest entry number for this year
            cursor.execute('''
                SELECT entry_number FROM journal_entries
                WHERE entry_number LIKE ?
                ORDER BY entry_number DESC
                LIMIT 1
            ''', (f"JE{current_year}%",))

            result = cursor.fetchone()

            if result:
                # Extract the sequence number and increment
                last_number = result[0]
                sequence = int(last_number.split('-')[1]) + 1
            else:
                # First entry of the year
                sequence = 1

            return f"JE{current_year}-{sequence:04d}"

        except sqlite3.Error as e:
            print(f"Error generating entry number: {e}")
            # Fallback to timestamp-based number
            return f"JE{datetime.now().strftime('%Y%m%d%H%M%S')}"
        finally:
            if conn:
                conn.close()

    def create_journal_entry(self, date, description, lines, reference="", tags="", created_by=""):
        """
        Create a new journal entry with multiple lines

        Args:
            date (str): Entry date in YYYY-MM-DD format
            description (str): Entry description
            lines (list): List of dictionaries with line details
                Each line should have: account_id, description, debit_amount, credit_amount, category_id
            reference (str): Optional reference number
            tags (str): Optional tags for categorization
            created_by (str): User who created the entry

        Returns:
            int: Journal entry ID if successful, None otherwise
        """
        if not lines:
            raise ValueError("Journal entry must have at least one line")

        # Validate that debits equal credits
        total_debits = sum(line.get('debit_amount', 0.0) for line in lines)
        total_credits = sum(line.get('credit_amount', 0.0) for line in lines)

        if abs(total_debits - total_credits) > 0.01:  # Allow for small rounding differences
            raise ValueError(f"Debits ({total_debits:.2f}) must equal credits ({total_credits:.2f})")

        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Generate entry number
            entry_number = self.generate_entry_number()

            # Insert journal entry
            cursor.execute('''
                INSERT INTO journal_entries
                (entry_number, date, description, reference, total_debits, total_credits,
                 is_balanced, created_by, tags, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (entry_number, date, description, reference, total_debits, total_credits,
                  True, created_by, tags, datetime.now(), datetime.now()))

            journal_entry_id = cursor.lastrowid

            # Insert journal lines
            for i, line in enumerate(lines, 1):
                cursor.execute('''
                    INSERT INTO journal_lines
                    (journal_entry_id, line_number, account_id, description,
                     debit_amount, credit_amount, category_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (journal_entry_id, i, line['account_id'], line.get('description', ''),
                      line.get('debit_amount', 0.0), line.get('credit_amount', 0.0),
                      line.get('category_id')))

                # Update account balance based on the line
                self._update_account_balance_for_line(cursor, line)

            conn.commit()

            # Log the creation in audit trail
            try:
                from model.audit_trail import AuditTrail
                audit = AuditTrail(self.db_path)
                audit.log_action(
                    table_name="journal_entries",
                    record_id=journal_entry_id,
                    action="INSERT",
                    new_values={
                        "entry_number": entry_number,
                        "date": date,
                        "description": description,
                        "total_debits": total_debits,
                        "total_credits": total_credits
                    },
                    user_id=created_by,
                    description=f"Created journal entry {entry_number}",
                    tags=tags
                )
            except Exception as e:
                print(f"Warning: Failed to log audit trail: {e}")

            return journal_entry_id

        except sqlite3.Error as e:
            conn.rollback()
            raise Exception(f"Error creating journal entry: {str(e)}")
        finally:
            conn.close()

    def _update_account_balance_for_line(self, cursor, line):
        """Update account balance based on journal line"""
        account_id = line['account_id']
        debit_amount = line.get('debit_amount', 0.0)
        credit_amount = line.get('credit_amount', 0.0)

        # Get account classification to determine how to apply the amounts
        cursor.execute("SELECT classification FROM accounts WHERE id = ?", (account_id,))
        result = cursor.fetchone()

        if result:
            classification = result[0]

            # Calculate the net effect on the account balance
            if classification in ['Asset', 'Expense']:
                # Debit accounts: debits increase balance, credits decrease balance
                net_change = debit_amount - credit_amount
            else:
                # Credit accounts (Liability, Equity, Revenue): credits increase balance, debits decrease balance
                net_change = credit_amount - debit_amount

            # Update the account balance
            cursor.execute('''
                UPDATE accounts
                SET current_balance = current_balance + ?
                WHERE id = ?
            ''', (net_change, account_id))

    def get_journal_entry(self, entry_id):
        """Get a journal entry with all its lines"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get journal entry
            cursor.execute('''
                SELECT id, entry_number, date, description, reference,
                       total_debits, total_credits, is_balanced, created_by,
                       tags, created_at, updated_at
                FROM journal_entries
                WHERE id = ?
            ''', (entry_id,))

            entry_row = cursor.fetchone()
            if not entry_row:
                return None

            entry = {
                'id': entry_row[0],
                'entry_number': entry_row[1],
                'date': entry_row[2],
                'description': entry_row[3],
                'reference': entry_row[4],
                'total_debits': entry_row[5],
                'total_credits': entry_row[6],
                'is_balanced': entry_row[7],
                'created_by': entry_row[8],
                'tags': entry_row[9],
                'created_at': entry_row[10],
                'updated_at': entry_row[11]
            }

            # Get journal lines
            cursor.execute('''
                SELECT jl.id, jl.line_number, jl.account_id, a.name as account_name,
                       jl.description, jl.debit_amount, jl.credit_amount,
                       jl.category_id, c.name as category_name
                FROM journal_lines jl
                LEFT JOIN accounts a ON jl.account_id = a.id
                LEFT JOIN categories c ON jl.category_id = c.id
                WHERE jl.journal_entry_id = ?
                ORDER BY jl.line_number
            ''', (entry_id,))

            lines = []
            for line_row in cursor.fetchall():
                lines.append({
                    'id': line_row[0],
                    'line_number': line_row[1],
                    'account_id': line_row[2],
                    'account_name': line_row[3],
                    'description': line_row[4],
                    'debit_amount': line_row[5],
                    'credit_amount': line_row[6],
                    'category_id': line_row[7],
                    'category_name': line_row[8]
                })

            entry['lines'] = lines
            return entry

        except sqlite3.Error as e:
            print(f"Error getting journal entry: {e}")
            return None
        finally:
            conn.close()

    def get_all_journal_entries(self, start_date=None, end_date=None, tags=None):
        """Get all journal entries with optional filtering"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            query = '''
                SELECT id, entry_number, date, description, reference,
                       total_debits, total_credits, is_balanced, created_by,
                       tags, created_at, updated_at
                FROM journal_entries
                WHERE 1=1
            '''
            params = []

            if start_date:
                query += " AND date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND date <= ?"
                params.append(end_date)

            if tags:
                query += " AND tags LIKE ?"
                params.append(f"%{tags}%")

            query += " ORDER BY date DESC, entry_number DESC"

            cursor.execute(query, params)

            entries = []
            for row in cursor.fetchall():
                entries.append({
                    'id': row[0],
                    'entry_number': row[1],
                    'date': row[2],
                    'description': row[3],
                    'reference': row[4],
                    'total_debits': row[5],
                    'total_credits': row[6],
                    'is_balanced': row[7],
                    'created_by': row[8],
                    'tags': row[9],
                    'created_at': row[10],
                    'updated_at': row[11]
                })

            return entries

        except sqlite3.Error as e:
            print(f"Error getting journal entries: {e}")
            return []
        finally:
            conn.close()

    def validate_journal_entry(self, entry_id):
        """Validate that a journal entry is properly balanced"""
        entry = self.get_journal_entry(entry_id)
        if not entry:
            return False, "Journal entry not found"

        total_debits = sum(line['debit_amount'] for line in entry['lines'])
        total_credits = sum(line['credit_amount'] for line in entry['lines'])

        if abs(total_debits - total_credits) > 0.01:
            return False, f"Entry is not balanced: Debits {total_debits:.2f} != Credits {total_credits:.2f}"

        return True, "Entry is balanced"

    def delete_journal_entry(self, entry_id):
        """Delete a journal entry and reverse its effects on account balances"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get the journal entry to reverse its effects
            entry = self.get_journal_entry(entry_id)
            if not entry:
                return False

            # Reverse the effects on account balances
            for line in entry['lines']:
                # Reverse the balance change
                self._reverse_account_balance_for_line(cursor, line)

            # Delete journal lines first (due to foreign key constraint)
            cursor.execute("DELETE FROM journal_lines WHERE journal_entry_id = ?", (entry_id,))

            # Delete journal entry
            cursor.execute("DELETE FROM journal_entries WHERE id = ?", (entry_id,))

            conn.commit()

            # Log the deletion in audit trail
            try:
                from model.audit_trail import AuditTrail
                audit = AuditTrail(self.db_path)
                audit.log_action(
                    table_name="journal_entries",
                    record_id=entry_id,
                    action="DELETE",
                    old_values={
                        "entry_number": entry['entry_number'],
                        "date": entry['date'],
                        "description": entry['description'],
                        "total_debits": entry['total_debits'],
                        "total_credits": entry['total_credits']
                    },
                    description=f"Deleted journal entry {entry['entry_number']}"
                )
            except Exception as e:
                print(f"Warning: Failed to log audit trail: {e}")

            return True

        except sqlite3.Error as e:
            conn.rollback()
            print(f"Error deleting journal entry: {e}")
            return False
        finally:
            conn.close()

    def _reverse_account_balance_for_line(self, cursor, line):
        """Reverse the account balance change for a journal line"""
        account_id = line['account_id']
        debit_amount = line.get('debit_amount', 0.0)
        credit_amount = line.get('credit_amount', 0.0)

        # Get account classification
        cursor.execute("SELECT classification FROM accounts WHERE id = ?", (account_id,))
        result = cursor.fetchone()

        if result:
            classification = result[0]

            # Calculate the reverse of the net effect
            if classification in ['Asset', 'Expense']:
                # Reverse: debits decrease balance, credits increase balance
                net_change = credit_amount - debit_amount
            else:
                # Reverse: credits decrease balance, debits increase balance
                net_change = debit_amount - credit_amount

            # Update the account balance
            cursor.execute('''
                UPDATE accounts
                SET current_balance = current_balance + ?
                WHERE id = ?
            ''', (net_change, account_id))
