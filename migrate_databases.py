#!/usr/bin/env python3
"""
Database Migration Script
Run this script to migrate all company databases to the latest schema
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_migration import DatabaseMigration


def main():
    """Main migration function"""
    print("=" * 60)
    print("CASHBOOK DATABASE MIGRATION UTILITY")
    print("=" * 60)
    print()
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("❌ Error: Please run this script from the Cashbook application directory")
        print("   (The directory containing main.py)")
        return
    
    print("🔍 Scanning for company databases...")
    
    # Get all company databases
    excluded_files = ['users.db', 'settings.db']
    db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]
    
    if not db_files:
        print("✅ No company databases found to migrate")
        return
    
    print(f"📊 Found {len(db_files)} company database(s):")
    for db_file in db_files:
        print(f"   - {db_file}")
    
    print()
    print("🔍 Checking database health...")
    
    # Check each database for issues
    databases_with_issues = []
    healthy_databases = []
    
    for db_file in db_files:
        print(f"   Checking {db_file}...", end=" ")
        issues = DatabaseMigration.check_database_health(db_file)
        
        if issues:
            databases_with_issues.append((db_file, issues))
            print("❌ Issues found")
            for issue in issues:
                print(f"      - {issue}")
        else:
            healthy_databases.append(db_file)
            print("✅ Healthy")
    
    print()
    print("📋 HEALTH CHECK SUMMARY:")
    print(f"   ✅ Healthy databases: {len(healthy_databases)}")
    print(f"   ❌ Databases with issues: {len(databases_with_issues)}")
    
    if not databases_with_issues:
        print()
        print("🎉 All databases are healthy! No migration needed.")
        return
    
    print()
    print("🔧 DATABASES REQUIRING MIGRATION:")
    for db_file, issues in databases_with_issues:
        print(f"   📁 {db_file}:")
        for issue in issues:
            print(f"      - {issue}")
    
    print()
    response = input("❓ Do you want to migrate all databases with issues? (y/n): ").lower().strip()
    
    if response != 'y':
        print("❌ Migration cancelled by user")
        return
    
    print()
    print("🚀 Starting database migration...")
    print("=" * 40)
    
    # Migrate databases
    success_count = 0
    total_count = len(databases_with_issues)
    
    for db_file, issues in databases_with_issues:
        print(f"🔧 Migrating {db_file}...")
        
        try:
            if DatabaseMigration.migrate_company_database(db_file):
                print(f"   ✅ Successfully migrated {db_file}")
                success_count += 1
                
                # Verify migration
                new_issues = DatabaseMigration.check_database_health(db_file)
                if new_issues:
                    print(f"   ⚠️  Warning: Some issues remain after migration:")
                    for issue in new_issues:
                        print(f"      - {issue}")
                else:
                    print(f"   ✅ Verification passed - {db_file} is now healthy")
            else:
                print(f"   ❌ Failed to migrate {db_file}")
                
        except Exception as e:
            print(f"   ❌ Error migrating {db_file}: {e}")
    
    print()
    print("=" * 40)
    print("📊 MIGRATION SUMMARY:")
    print(f"   ✅ Successfully migrated: {success_count}/{total_count}")
    print(f"   ❌ Failed migrations: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print()
        print("🎉 ALL MIGRATIONS COMPLETED SUCCESSFULLY!")
        print("   Your databases are now ready for use with the latest features.")
    else:
        print()
        print("⚠️  SOME MIGRATIONS FAILED")
        print("   Please check the error messages above and try again.")
        print("   You may need to manually fix database issues.")
    
    print()
    print("💡 NEXT STEPS:")
    print("   1. Start the Cashbook application (python main.py)")
    print("   2. Login as admin or client")
    print("   3. Try opening companies to verify they work correctly")
    print("   4. If you encounter issues, check the console output for errors")
    
    print()
    input("Press Enter to exit...")


def quick_fix():
    """Quick fix for common database issues"""
    print("🔧 QUICK FIX MODE")
    print("=" * 30)
    
    # Check for settings.db appearing as a company
    if os.path.exists('settings.db'):
        print("✅ Found settings.db - this is normal and will be excluded from company lists")
    
    # Check for missing accounts tables
    excluded_files = ['users.db', 'settings.db']
    db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]
    
    fixed_count = 0
    for db_file in db_files:
        issues = DatabaseMigration.check_database_health(db_file)
        if any("Missing required table: accounts" in issue for issue in issues):
            print(f"🔧 Fixing missing accounts table in {db_file}...")
            if DatabaseMigration.migrate_company_database(db_file):
                print(f"   ✅ Fixed {db_file}")
                fixed_count += 1
            else:
                print(f"   ❌ Failed to fix {db_file}")
    
    if fixed_count > 0:
        print(f"✅ Fixed {fixed_count} database(s)")
    else:
        print("ℹ️  No databases needed fixing")


if __name__ == "__main__":
    try:
        if len(sys.argv) > 1 and sys.argv[1] == "--quick-fix":
            quick_fix()
        else:
            main()
    except KeyboardInterrupt:
        print("\n❌ Migration cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
