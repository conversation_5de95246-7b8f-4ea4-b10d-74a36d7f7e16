import os
import sqlite3
import sys
import unittest
from datetime import datetime, timedelta

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from model.client import Client
from model.invoice import Invoice


class TestInvoice(unittest.TestCase):
    """Test cases for the Invoice model"""

    def setUp(self):
        """Set up test environment"""
        # Create a test database
        self.db_path = "tests/test_invoice.db"

        # Remove the test database if it exists
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

        # Create the invoice model
        self.invoice_model = Invoice(self.db_path)

        # Create a client model and add a test client
        self.client_model = Client(self.db_path)
        self.client_id = self.client_model.add_client({
            'name': "Test Client",
            'company_name': "Test Company",
            'email': "<EMAIL>",
            'phone': "************",
            'address': "123 Test St",
            'notes': "Test notes"
        })

        # Create a test invoice
        invoice_data = {
            'client_id': self.client_id,
            'invoice_number': "INV-001",
            'issue_date': datetime.now().strftime("%Y-%m-%d"),
            'due_date': (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
            'notes': "Test invoice",
            'status': Invoice.STATUS_DRAFT
        }

        items_data = [
            {
                'description': "Test item 1",
                'quantity': 2,
                'unit_price': 10.0,
                'tax_rate': 0.0
            },
            {
                'description': "Test item 2",
                'quantity': 1,
                'unit_price': 20.0,
                'tax_rate': 0.0
            }
        ]

        self.invoice_id = self.invoice_model.add_invoice(invoice_data, items_data)

    def tearDown(self):
        """Clean up after tests"""
        # Close database connections
        self.invoice_model = None
        self.client_model = None

        # Remove the test database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def test_create_invoice(self):
        """Test creating a new invoice"""
        # Create a new invoice
        invoice_data = {
            'client_id': self.client_id,
            'invoice_number': "INV-002",
            'issue_date': datetime.now().strftime("%Y-%m-%d"),
            'due_date': (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
            'notes': "Another test invoice",
            'status': Invoice.STATUS_DRAFT
        }

        items_data = [
            {
                'description': "New test item",
                'quantity': 3,
                'unit_price': 15.0,
                'tax_rate': 0.0
            }
        ]

        invoice_id = self.invoice_model.add_invoice(invoice_data, items_data)

        # Verify the invoice was created
        self.assertIsNotNone(invoice_id)

        # Get the invoice
        invoice = self.invoice_model.get_invoice(invoice_id)

        # Verify the invoice data
        self.assertEqual(invoice["invoice_number"], "INV-002")
        self.assertEqual(invoice["client_id"], self.client_id)
        self.assertEqual(invoice["notes"], "Another test invoice")
        self.assertEqual(invoice["status"], Invoice.STATUS_DRAFT)

    def test_get_invoice(self):
        """Test retrieving an invoice"""
        # Get the invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Verify the invoice data
        self.assertEqual(invoice["id"], self.invoice_id)
        self.assertEqual(invoice["invoice_number"], "INV-001")
        self.assertEqual(invoice["client_id"], self.client_id)
        self.assertEqual(invoice["notes"], "Test invoice")
        self.assertEqual(invoice["status"], Invoice.STATUS_DRAFT)

        # Verify the invoice items
        self.assertEqual(len(invoice["items"]), 2)
        self.assertEqual(invoice["items"][0]["description"], "Test item 1")
        self.assertEqual(invoice["items"][0]["quantity"], 2)
        self.assertEqual(invoice["items"][0]["unit_price"], 10.0)
        self.assertEqual(invoice["items"][1]["description"], "Test item 2")
        self.assertEqual(invoice["items"][1]["quantity"], 1)
        self.assertEqual(invoice["items"][1]["unit_price"], 20.0)

        # Verify the total amount
        self.assertEqual(invoice["total_amount"], 40.0)

    def test_get_all_invoices(self):
        """Test retrieving all invoices"""
        # Create another invoice
        invoice_data = {
            'client_id': self.client_id,
            'invoice_number': "INV-003",
            'issue_date': datetime.now().strftime("%Y-%m-%d"),
            'due_date': (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
            'notes': "Another test invoice",
            'status': Invoice.STATUS_DRAFT
        }

        items_data = [
            {
                'description': "New test item",
                'quantity': 3,
                'unit_price': 15.0,
                'tax_rate': 0.0
            }
        ]

        self.invoice_model.add_invoice(invoice_data, items_data)

        # Get all invoices
        invoices = self.invoice_model.get_all_invoices()

        # Verify we have 2 invoices
        self.assertEqual(len(invoices), 2)

        # Verify the invoice numbers
        invoice_numbers = [invoice["invoice_number"] for invoice in invoices]
        self.assertIn("INV-001", invoice_numbers)
        self.assertIn("INV-003", invoice_numbers)

    def test_update_invoice(self):
        """Test updating an invoice"""
        # Update the invoice
        invoice_data = {
            'invoice_number': "INV-001-UPDATED",
            'issue_date': datetime.now().strftime("%Y-%m-%d"),
            'due_date': (datetime.now() + timedelta(days=45)).strftime("%Y-%m-%d"),
            'notes': "Updated test invoice"
        }

        result = self.invoice_model.update_invoice(
            invoice_id=self.invoice_id,
            invoice_data=invoice_data
        )

        # Verify the update was successful
        self.assertTrue(result)

        # Get the updated invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Verify the invoice data was updated
        self.assertEqual(invoice["invoice_number"], "INV-001-UPDATED")
        self.assertEqual(invoice["notes"], "Updated test invoice")

    def test_delete_invoice(self):
        """Test deleting an invoice"""
        # Delete the invoice
        result = self.invoice_model.delete_invoice(self.invoice_id)

        # Verify the deletion was successful
        self.assertTrue(result)

        # Try to get the deleted invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Verify the invoice is gone
        self.assertIsNone(invoice)

        # Verify the invoice items are gone
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM invoice_items WHERE invoice_id = ?", (self.invoice_id,))
        count = cursor.fetchone()[0]
        conn.close()

        self.assertEqual(count, 0)

    def test_add_invoice_item(self):
        """Test adding an invoice item"""
        # Get the original invoice
        original_invoice = self.invoice_model.get_invoice(self.invoice_id)
        original_items = original_invoice["items"]

        # Create a new list of items with the original items plus a new one
        items_data = []
        for item in original_items:
            items_data.append({
                'description': item['description'],
                'quantity': item['quantity'],
                'unit_price': item['unit_price'],
                'tax_rate': item.get('tax_rate', 0.0)
            })

        # Add the new item
        items_data.append({
            'description': "Test item 3",
            'quantity': 3,
            'unit_price': 15.0,
            'tax_rate': 0.0
        })

        # Update the invoice with the new items
        result = self.invoice_model.update_invoice(
            invoice_id=self.invoice_id,
            invoice_data={},
            items_data=items_data
        )

        # Verify the update was successful
        self.assertTrue(result)

        # Get the updated invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Verify the item is in the invoice
        self.assertEqual(len(invoice["items"]), 3)

        # Find the new item
        new_item = None
        for item in invoice["items"]:
            if item["description"] == "Test item 3":
                new_item = item
                break

        # Verify the item data
        self.assertIsNotNone(new_item)
        self.assertEqual(new_item["quantity"], 3)
        self.assertEqual(new_item["unit_price"], 15.0)

        # Verify the total amount is updated
        self.assertEqual(invoice["total_amount"], 85.0)  # 20 + 20 + 45

    def test_update_invoice_item(self):
        """Test updating an invoice item"""
        # Get the original invoice
        original_invoice = self.invoice_model.get_invoice(self.invoice_id)
        original_items = original_invoice["items"]

        # Create a new list of items with the first item updated
        items_data = []
        for i, item in enumerate(original_items):
            if i == 0:  # Update the first item
                items_data.append({
                    'description': "Updated item",
                    'quantity': 5,
                    'unit_price': 12.0,
                    'tax_rate': item.get('tax_rate', 0.0)
                })
            else:
                items_data.append({
                    'description': item['description'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'tax_rate': item.get('tax_rate', 0.0)
                })

        # Update the invoice with the modified items
        result = self.invoice_model.update_invoice(
            invoice_id=self.invoice_id,
            invoice_data={},
            items_data=items_data
        )

        # Verify the update was successful
        self.assertTrue(result)

        # Get the updated invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Find the updated item
        updated_item = None
        for item in invoice["items"]:
            if item["description"] == "Updated item":
                updated_item = item
                break

        # Verify the item data was updated
        self.assertIsNotNone(updated_item)
        self.assertEqual(updated_item["description"], "Updated item")
        self.assertEqual(updated_item["quantity"], 5)
        self.assertEqual(updated_item["unit_price"], 12.0)

        # Verify the total amount is updated
        self.assertEqual(invoice["total_amount"], 80.0)  # 60 + 20

    def test_delete_invoice_item(self):
        """Test deleting an invoice item"""
        # Get the original invoice
        original_invoice = self.invoice_model.get_invoice(self.invoice_id)
        original_items = original_invoice["items"]

        # Create a new list of items without the first item
        items_data = []
        for i, item in enumerate(original_items):
            if i > 0:  # Skip the first item
                items_data.append({
                    'description': item['description'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'tax_rate': item.get('tax_rate', 0.0)
                })

        # Update the invoice with the modified items
        result = self.invoice_model.update_invoice(
            invoice_id=self.invoice_id,
            invoice_data={},
            items_data=items_data
        )

        # Verify the update was successful
        self.assertTrue(result)

        # Get the updated invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Verify the item is gone
        self.assertEqual(len(invoice["items"]), 1)

        # Verify the total amount is updated
        self.assertEqual(invoice["total_amount"], 20.0)  # Just the second item

    def test_update_invoice_status(self):
        """Test updating an invoice status"""
        # Update the invoice status
        result = self.invoice_model.update_invoice_status(
            invoice_id=self.invoice_id,
            status=Invoice.STATUS_SENT
        )

        # Verify the update was successful
        self.assertTrue(result)

        # Get the updated invoice
        invoice = self.invoice_model.get_invoice(self.invoice_id)

        # Verify the status was updated
        self.assertEqual(invoice["status"], Invoice.STATUS_SENT)

    def test_check_overdue_invoices(self):
        """Test checking for overdue invoices"""
        # Create an invoice with a past due date
        invoice_data = {
            'client_id': self.client_id,
            'invoice_number': "INV-OVERDUE",
            'issue_date': (datetime.now() - timedelta(days=45)).strftime("%Y-%m-%d"),
            'due_date': (datetime.now() - timedelta(days=15)).strftime("%Y-%m-%d"),
            'notes': "Overdue invoice",
            'status': Invoice.STATUS_DRAFT
        }

        items_data = [
            {
                'description': "Overdue item",
                'quantity': 1,
                'unit_price': 50.0,
                'tax_rate': 0.0
            }
        ]

        past_due_id = self.invoice_model.add_invoice(invoice_data, items_data)

        # Set the status to SENT (so it can be marked as overdue)
        self.invoice_model.update_invoice_status(
            invoice_id=past_due_id,
            status=Invoice.STATUS_SENT
        )

        # Check for overdue invoices
        count = self.invoice_model.check_overdue_invoices()

        # Verify one invoice was marked as overdue
        self.assertEqual(count, 1)

        # Get the invoice
        invoice = self.invoice_model.get_invoice(past_due_id)

        # Verify the status was updated
        self.assertEqual(invoice["status"], Invoice.STATUS_OVERDUE)

    def test_generate_invoice_number(self):
        """Test generating an invoice number"""
        # Generate an invoice number
        invoice_number = self.invoice_model.generate_invoice_number()

        # Verify the invoice number is not empty
        self.assertIsNotNone(invoice_number)
        self.assertNotEqual(invoice_number, "")

        # Verify the invoice number is unique
        self.assertNotEqual(invoice_number, "INV-001")

        # Generate another invoice number
        another_invoice_number = self.invoice_model.generate_invoice_number()

        # Verify the invoice numbers are different
        self.assertNotEqual(invoice_number, another_invoice_number)


if __name__ == '__main__':
    unittest.main()
