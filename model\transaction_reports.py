import datetime

from model.report_framework import TransactionReport


class TransactionRegisterReport(TransactionReport):
    """Transaction register report showing all transactions for a given period"""

    def __init__(self, db_path):
        super().__init__(db_path, "Transaction Register", "List of all transactions for a given period")

    def generate(self):
        """Generate the transaction register report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")
        category_id = self.get_parameter("category_id")
        transaction_type = self.get_parameter("transaction_type")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Build query
            query = """
            SELECT t.id, t.date, t.amount, t.description, t.type,
                   a.name as account_name, c.name as category_name,
                   t.reconciled, t.notes
            FROM transactions t
            LEFT JOIN accounts a ON t.account_id = a.id
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE 1=1
            """

            params = []

            # Add filters
            if date_from:
                query += " AND t.date >= ?"
                params.append(date_from)

            if date_to:
                query += " AND t.date <= ?"
                params.append(date_to)

            if account_id:
                query += " AND t.account_id = ?"
                params.append(account_id)

            if category_id:
                query += " AND t.category_id = ?"
                params.append(category_id)

            if transaction_type:
                query += " AND t.type = ?"
                params.append(transaction_type)

            # Add order by
            query += " ORDER BY t.date DESC, t.id DESC"

            # Execute query
            cursor.execute(query, params)

            # Get results
            self.results = [dict(row) for row in cursor.fetchall()]

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating transaction register report: {str(e)}")
            return None


class TransactionSummaryReport(TransactionReport):
    """Transaction summary report showing totals by category"""

    def __init__(self, db_path):
        super().__init__(db_path, "Transaction Summary", "Summary of transactions by category")

    def generate(self):
        """Generate the transaction summary report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")
        transaction_type = self.get_parameter("transaction_type")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Build query
            query = """
            SELECT c.name as category_name, t.type,
                   COUNT(t.id) as transaction_count,
                   SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE 1=1
            """

            params = []

            # Add filters
            if date_from:
                query += " AND t.date >= ?"
                params.append(date_from)

            if date_to:
                query += " AND t.date <= ?"
                params.append(date_to)

            if account_id:
                query += " AND t.account_id = ?"
                params.append(account_id)

            if transaction_type:
                query += " AND t.type = ?"
                params.append(transaction_type)

            # Group by category and type
            query += " GROUP BY c.name, t.type"

            # Add order by
            query += " ORDER BY c.name, t.type"

            # Execute query
            cursor.execute(query, params)

            # Get results
            self.results = [dict(row) for row in cursor.fetchall()]

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating transaction summary report: {str(e)}")
            return None


class CategoryBreakdownReport(TransactionReport):
    """Category breakdown report showing income and expenses by category"""

    def __init__(self, db_path):
        super().__init__(db_path, "Category Breakdown", "Breakdown of income and expenses by category")

    def generate(self):
        """Generate the category breakdown report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Build query for income
            income_query = """
            SELECT c.name as category_name, 'income' as type,
                   COUNT(t.id) as transaction_count,
                   SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.type = 'income'
            """

            income_params = []

            # Add filters
            if date_from:
                income_query += " AND t.date >= ?"
                income_params.append(date_from)

            if date_to:
                income_query += " AND t.date <= ?"
                income_params.append(date_to)

            if account_id:
                income_query += " AND t.account_id = ?"
                income_params.append(account_id)

            # Group by category
            income_query += " GROUP BY c.name"

            # Build query for expenses
            expense_query = """
            SELECT c.name as category_name, 'expense' as type,
                   COUNT(t.id) as transaction_count,
                   SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.type = 'expense'
            """

            expense_params = []

            # Add filters
            if date_from:
                expense_query += " AND t.date >= ?"
                expense_params.append(date_from)

            if date_to:
                expense_query += " AND t.date <= ?"
                expense_params.append(date_to)

            if account_id:
                expense_query += " AND t.account_id = ?"
                expense_params.append(account_id)

            # Group by category
            expense_query += " GROUP BY c.name"

            # Execute income query
            cursor.execute(income_query, income_params)
            income_results = [dict(row) for row in cursor.fetchall()]

            # Execute expense query
            cursor.execute(expense_query, expense_params)
            expense_results = [dict(row) for row in cursor.fetchall()]

            # Combine results
            self.results = income_results + expense_results

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating category breakdown report: {str(e)}")
            return None


class CustomTransactionReport(TransactionReport):
    """Custom transaction report with user-defined filters and grouping"""

    def __init__(self, db_path):
        super().__init__(db_path, "Custom Transaction Report", "Custom report with user-defined filters and grouping")

    def generate(self):
        """Generate the custom transaction report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")
        category_id = self.get_parameter("category_id")
        transaction_type = self.get_parameter("transaction_type")
        group_by = self.get_parameter("group_by", [])  # List of fields to group by
        sort_by = self.get_parameter("sort_by", "date")
        sort_order = self.get_parameter("sort_order", "DESC")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Build select clause
            select_clause = "SELECT "

            # Add group by fields to select clause
            if "date" in group_by:
                select_clause += "t.date, "
            if "month" in group_by:
                select_clause += "strftime('%Y-%m', t.date) as month, "
            if "year" in group_by:
                select_clause += "strftime('%Y', t.date) as year, "
            if "account" in group_by:
                select_clause += "a.name as account_name, "
            if "category" in group_by:
                select_clause += "c.name as category_name, "
            if "type" in group_by:
                select_clause += "t.type, "

            # Add aggregates
            select_clause += "COUNT(t.id) as transaction_count, SUM(t.amount) as total_amount "

            # Build from clause
            from_clause = """
            FROM transactions t
            LEFT JOIN accounts a ON t.account_id = a.id
            LEFT JOIN categories c ON t.category_id = c.id
            """

            # Build where clause
            where_clause = "WHERE 1=1 "
            params = []

            # Add filters
            if date_from:
                where_clause += "AND t.date >= ? "
                params.append(date_from)

            if date_to:
                where_clause += "AND t.date <= ? "
                params.append(date_to)

            if account_id:
                where_clause += "AND t.account_id = ? "
                params.append(account_id)

            if category_id:
                where_clause += "AND t.category_id = ? "
                params.append(category_id)

            if transaction_type:
                where_clause += "AND t.type = ? "
                params.append(transaction_type)

            # Build group by clause
            group_by_clause = ""
            if group_by:
                group_by_clause = "GROUP BY "
                group_by_fields = []

                if "date" in group_by:
                    group_by_fields.append("t.date")
                if "month" in group_by:
                    group_by_fields.append("strftime('%Y-%m', t.date)")
                if "year" in group_by:
                    group_by_fields.append("strftime('%Y', t.date)")
                if "account" in group_by:
                    group_by_fields.append("a.name")
                if "category" in group_by:
                    group_by_fields.append("c.name")
                if "type" in group_by:
                    group_by_fields.append("t.type")

                if group_by_fields:
                    group_by_clause = "GROUP BY " + ", ".join(group_by_fields)

            # Build order by clause
            order_by_clause = "ORDER BY "

            # Map sort_by to actual field
            sort_field_map = {
                "date": "t.date",
                "month": "month",
                "year": "year",
                "account": "a.name",
                "category": "c.name",
                "type": "t.type",
                "count": "transaction_count",
                "amount": "total_amount"
            }

            order_by_clause += sort_field_map.get(sort_by, "t.date") + " " + sort_order

            # Build final query
            if group_by:
                # Grouped report
                query = select_clause + from_clause + where_clause + group_by_clause + " " + order_by_clause
            else:
                # Detailed report (no grouping)
                select_clause = """
                SELECT t.id, t.date, t.amount, t.description, t.type,
                       a.name as account_name, c.name as category_name,
                       t.reconciled
                """
                query = select_clause + from_clause + where_clause + order_by_clause

            # Execute query
            cursor.execute(query, params)

            # Get results
            self.results = [dict(row) for row in cursor.fetchall()]

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating custom transaction report: {str(e)}")
            return None
