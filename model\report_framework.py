import os
import json
import datetime
import sqlite3
from abc import ABC, abstractmethod


class Report(ABC):
    """Base class for all reports"""
    
    def __init__(self, db_path, report_name, description=""):
        self.db_path = db_path
        self.report_name = report_name
        self.description = description
        self.parameters = {}
        self.results = None
        self.created_at = None
        self.cache_dir = os.path.join(os.path.dirname(db_path), "reports", "cache")
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def set_parameter(self, name, value):
        """Set a report parameter"""
        self.parameters[name] = value
    
    def get_parameter(self, name, default=None):
        """Get a report parameter"""
        return self.parameters.get(name, default)
    
    def get_parameters(self):
        """Get all report parameters"""
        return self.parameters
    
    @abstractmethod
    def generate(self):
        """Generate the report (must be implemented by subclasses)"""
        pass
    
    def get_results(self):
        """Get the report results"""
        return self.results
    
    def get_cache_key(self):
        """Generate a cache key based on report name and parameters"""
        # Sort parameters to ensure consistent cache keys
        sorted_params = sorted(self.parameters.items())
        param_str = json.dumps(sorted_params)
        
        # Create a cache key
        import hashlib
        cache_key = hashlib.md5(f"{self.report_name}:{param_str}".encode()).hexdigest()
        
        return cache_key
    
    def save_to_cache(self):
        """Save report results to cache"""
        if not self.results:
            return False
        
        cache_key = self.get_cache_key()
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        try:
            with open(cache_file, 'w') as f:
                json.dump({
                    'report_name': self.report_name,
                    'description': self.description,
                    'parameters': self.parameters,
                    'results': self.results,
                    'created_at': datetime.datetime.now().isoformat()
                }, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving report to cache: {str(e)}")
            return False
    
    def load_from_cache(self):
        """Load report results from cache"""
        cache_key = self.get_cache_key()
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if not os.path.exists(cache_file):
            return False
        
        try:
            with open(cache_file, 'r') as f:
                data = json.load(f)
                self.results = data.get('results')
                self.created_at = data.get('created_at')
            return True
        except Exception as e:
            print(f"Error loading report from cache: {str(e)}")
            return False
    
    def clear_cache(self):
        """Clear the report cache"""
        cache_key = self.get_cache_key()
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
                return True
            except Exception as e:
                print(f"Error clearing report cache: {str(e)}")
                return False
        return True
    
    def export_to_csv(self, file_path):
        """Export report results to CSV"""
        if not self.results:
            return False
        
        try:
            import csv
            
            # Get headers from the first result
            if isinstance(self.results, list) and len(self.results) > 0:
                if isinstance(self.results[0], dict):
                    headers = list(self.results[0].keys())
                    
                    with open(file_path, 'w', newline='') as f:
                        writer = csv.DictWriter(f, fieldnames=headers)
                        writer.writeheader()
                        writer.writerows(self.results)
                    return True
            
            return False
        except Exception as e:
            print(f"Error exporting report to CSV: {str(e)}")
            return False
    
    def export_to_excel(self, file_path):
        """Export report results to Excel"""
        if not self.results:
            return False
        
        try:
            import pandas as pd
            
            # Convert results to DataFrame
            if isinstance(self.results, list) and len(self.results) > 0:
                df = pd.DataFrame(self.results)
                df.to_excel(file_path, index=False)
                return True
            
            return False
        except Exception as e:
            print(f"Error exporting report to Excel: {str(e)}")
            return False
    
    def export_to_pdf(self, file_path):
        """Export report results to PDF"""
        if not self.results:
            return False
        
        try:
            from reportlab.lib import colors
            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            
            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=letter)
            elements = []
            
            # Add title
            styles = getSampleStyleSheet()
            elements.append(Paragraph(self.report_name, styles['Title']))
            
            # Add description
            if self.description:
                elements.append(Paragraph(self.description, styles['Normal']))
            
            # Add parameters
            if self.parameters:
                param_text = "Parameters: " + ", ".join([f"{k}={v}" for k, v in self.parameters.items()])
                elements.append(Paragraph(param_text, styles['Normal']))
            
            elements.append(Spacer(1, 12))
            
            # Add results table
            if isinstance(self.results, list) and len(self.results) > 0:
                if isinstance(self.results[0], dict):
                    # Get headers
                    headers = list(self.results[0].keys())
                    
                    # Create data for table
                    data = [headers]
                    for row in self.results:
                        data.append([str(row.get(h, "")) for h in headers])
                    
                    # Create table
                    table = Table(data)
                    
                    # Add style
                    style = TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 10),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ])
                    table.setStyle(style)
                    
                    # Add table to elements
                    elements.append(table)
            
            # Build PDF
            doc.build(elements)
            return True
            
        except Exception as e:
            print(f"Error exporting report to PDF: {str(e)}")
            return False


class TransactionReport(Report):
    """Base class for transaction reports"""
    
    def __init__(self, db_path, report_name, description=""):
        super().__init__(db_path, report_name, description)
        self.transaction_manager = None
    
    def _get_connection(self):
        """Get a database connection with proper settings"""
        conn = sqlite3.connect(self.db_path, timeout=30)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("PRAGMA busy_timeout = 10000")
        cursor.execute("PRAGMA foreign_keys = ON")
        return conn, cursor


class FinancialReport(Report):
    """Base class for financial reports"""
    
    def __init__(self, db_path, report_name, description=""):
        super().__init__(db_path, report_name, description)
    
    def _get_connection(self):
        """Get a database connection with proper settings"""
        conn = sqlite3.connect(self.db_path, timeout=30)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("PRAGMA busy_timeout = 10000")
        cursor.execute("PRAGMA foreign_keys = ON")
        return conn, cursor


class ReportManager:
    """Manager for report generation and caching"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.reports = {}
        self.cache_dir = os.path.join(os.path.dirname(db_path), "reports", "cache")
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def register_report(self, report_class, report_id=None):
        """Register a report class"""
        if report_id is None:
            report_id = report_class.__name__
        
        self.reports[report_id] = report_class
    
    def get_report(self, report_id, **kwargs):
        """Get a report instance"""
        if report_id not in self.reports:
            raise ValueError(f"Report '{report_id}' not registered")
        
        report_class = self.reports[report_id]
        return report_class(self.db_path, **kwargs)
    
    def get_available_reports(self):
        """Get a list of available reports"""
        return list(self.reports.keys())
    
    def clear_all_cache(self):
        """Clear all report caches"""
        try:
            for file in os.listdir(self.cache_dir):
                if file.endswith(".json"):
                    os.remove(os.path.join(self.cache_dir, file))
            return True
        except Exception as e:
            print(f"Error clearing report cache: {str(e)}")
            return False
