import os
import tkinter as tk
from tkinter import filedialog, messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox

from model.client import Client
from model.invoice import Invoice
from utils.email_service import EmailService
from utils.pdf_generator import InvoicePDFGenerator


class InvoiceEmailDialog(tk.Toplevel):
    """Dialog for sending invoice emails"""

    def __init__(self, parent, db_path, invoice_id, callback=None):
        """Initialize the invoice email dialog"""
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.invoice_id = invoice_id
        self.callback = callback
        self.email_service = EmailService(db_path)
        self.invoice_model = Invoice(db_path)
        self.client_model = Client(db_path)
        self.pdf_path = None

        # Set dialog properties
        self.title("Send Invoice Email")
        self.geometry("600x700")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self.on_close)

        # Create variables
        self.template_var = tk.StringVar()
        self.recipient_var = tk.StringVar()
        self.subject_var = tk.StringVar()
        self.body_var = tk.StringVar()
        self.attach_pdf_var = tk.BooleanVar(value=True)
        self.error_var = tk.StringVar()

        # Load invoice and client data
        self.load_data()

        # Create widgets
        self.create_widgets()

        # Center the dialog
        self.center_dialog()

    def load_data(self):
        """Load invoice and client data"""
        # Get invoice details
        self.invoice = self.invoice_model.get_invoice(self.invoice_id)
        if not self.invoice:
            messagebox.showerror("Error", f"Invoice {self.invoice_id} not found")
            self.destroy()
            return

        # Get client details
        self.client = self.client_model.get_client(self.invoice['client_id'])
        if not self.client:
            messagebox.showerror("Error", f"Client for invoice {self.invoice_id} not found")
            self.destroy()
            return

        # Set recipient
        self.recipient_var.set(self.client.get('email', ''))

        # Get email templates
        self.templates = self.email_service.get_email_templates()
        if not self.templates:
            messagebox.showerror("Error", "No email templates found")
            self.destroy()
            return

        # Set default template
        default_template = None
        for template in self.templates:
            if template.get('is_default'):
                default_template = template
                break

        if default_template:
            self.template_var.set(default_template['name'])
            self.update_preview()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Send Invoice Email", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20), anchor="w")

        # Error message
        error_label = ttk.Label(main_frame, textvariable=self.error_var, foreground="red")
        error_label.pack(fill="x", pady=(0, 10))

        # Invoice info
        info_frame = ttk.LabelFrame(main_frame, text="Invoice Information")
        info_frame.pack(fill="x", pady=(0, 10))

        # Invoice number
        invoice_number_frame = ttk.Frame(info_frame)
        invoice_number_frame.pack(fill="x", padx=10, pady=5)
        
        invoice_number_label = ttk.Label(invoice_number_frame, text="Invoice Number:", width=15, anchor="w")
        invoice_number_label.pack(side="left")
        
        invoice_number_value = ttk.Label(invoice_number_frame, text=self.invoice['invoice_number'])
        invoice_number_value.pack(side="left", fill="x", expand=True)

        # Client name
        client_name_frame = ttk.Frame(info_frame)
        client_name_frame.pack(fill="x", padx=10, pady=5)
        
        client_name_label = ttk.Label(client_name_frame, text="Client:", width=15, anchor="w")
        client_name_label.pack(side="left")
        
        client_name_value = ttk.Label(client_name_frame, text=self.client['name'])
        client_name_value.pack(side="left", fill="x", expand=True)

        # Total amount
        amount_frame = ttk.Frame(info_frame)
        amount_frame.pack(fill="x", padx=10, pady=5)
        
        amount_label = ttk.Label(amount_frame, text="Amount:", width=15, anchor="w")
        amount_label.pack(side="left")
        
        amount_value = ttk.Label(amount_frame, text=f"${self.invoice['total_amount']:.2f}")
        amount_value.pack(side="left", fill="x", expand=True)

        # Email settings
        email_frame = ttk.LabelFrame(main_frame, text="Email Settings")
        email_frame.pack(fill="x", pady=(0, 10))

        # Template selection
        template_frame = ttk.Frame(email_frame)
        template_frame.pack(fill="x", padx=10, pady=5)
        
        template_label = ttk.Label(template_frame, text="Template:", width=15, anchor="w")
        template_label.pack(side="left")
        
        template_options = [t['name'] for t in self.templates]
        template_dropdown = ttk.Combobox(template_frame, textvariable=self.template_var, 
                                       values=template_options, state="readonly")
        template_dropdown.pack(side="left", fill="x", expand=True)
        template_dropdown.bind("<<ComboboxSelected>>", self.update_preview)

        # Recipient
        recipient_frame = ttk.Frame(email_frame)
        recipient_frame.pack(fill="x", padx=10, pady=5)
        
        recipient_label = ttk.Label(recipient_frame, text="Recipient:", width=15, anchor="w")
        recipient_label.pack(side="left")
        
        recipient_entry = ttk.Entry(recipient_frame, textvariable=self.recipient_var)
        recipient_entry.pack(side="left", fill="x", expand=True)

        # Attach PDF checkbox
        attach_frame = ttk.Frame(email_frame)
        attach_frame.pack(fill="x", padx=10, pady=5)
        
        attach_check = ttk.Checkbutton(attach_frame, text="Attach PDF Invoice", 
                                     variable=self.attach_pdf_var)
        attach_check.pack(side="left")

        # Email preview
        preview_frame = ttk.LabelFrame(main_frame, text="Email Preview")
        preview_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Subject
        subject_frame = ttk.Frame(preview_frame)
        subject_frame.pack(fill="x", padx=10, pady=5)
        
        subject_label = ttk.Label(subject_frame, text="Subject:", width=15, anchor="w")
        subject_label.pack(side="left")
        
        subject_entry = ttk.Entry(subject_frame, textvariable=self.subject_var)
        subject_entry.pack(side="left", fill="x", expand=True)

        # Body
        body_frame = ttk.Frame(preview_frame)
        body_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        body_label = ttk.Label(body_frame, text="Body:", width=15, anchor="nw")
        body_label.pack(side="left", anchor="n")
        
        self.body_text = tk.Text(body_frame, height=10, width=50)
        self.body_text.pack(side="left", fill="both", expand=True)
        
        # Scrollbar for body text
        body_scrollbar = ttk.Scrollbar(body_frame, orient="vertical", command=self.body_text.yview)
        body_scrollbar.pack(side="right", fill="y")
        self.body_text.config(yscrollcommand=body_scrollbar.set)
        
        # Connect text widget to StringVar
        def update_body_var(event=None):
            self.body_var.set(self.body_text.get("1.0", "end-1c"))
        
        self.body_text.bind("<KeyRelease>", update_body_var)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))

        send_button = ttk.Button(button_frame, text="Send Email", 
                               command=self.send_email, bootstyle=SUCCESS)
        send_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel", 
                                 command=self.on_close, bootstyle=SECONDARY)
        cancel_button.pack(side="left")

    def update_preview(self, event=None):
        """Update the email preview based on the selected template"""
        # Get selected template
        template_name = self.template_var.get()
        if not template_name:
            return

        # Find template
        template = None
        for t in self.templates:
            if t['name'] == template_name:
                template = t
                break

        if not template:
            return

        # Get email settings
        settings = self.email_service.get_email_settings()
        if not settings:
            self.error_var.set("Email settings not configured")
            return

        # Prepare context for template rendering
        context = {
            'invoice_number': self.invoice['invoice_number'],
            'company_name': settings.get('from_name', ''),
            'client_name': self.client['name'],
            'total_amount': f"${self.invoice['total_amount']:.2f}",
            'due_date': self.invoice['due_date'],
            'signature': settings.get('signature', '')
        }

        # Render template
        subject = self.email_service._render_template(template['subject'], context)
        body = self.email_service._render_template(template['body'], context)

        # Update preview
        self.subject_var.set(subject)
        self.body_text.delete("1.0", tk.END)
        self.body_text.insert("1.0", body)
        self.body_var.set(body)

    def send_email(self):
        """Send the invoice email"""
        # Validate email settings
        settings = self.email_service.get_email_settings()
        if not settings:
            self.error_var.set("Email settings not configured")
            return

        # Validate recipient
        if not self.recipient_var.get():
            self.error_var.set("Recipient email is required")
            return

        # Validate subject and body
        if not self.subject_var.get():
            self.error_var.set("Subject is required")
            return

        if not self.body_var.get():
            self.error_var.set("Email body is required")
            return

        # Generate PDF if needed
        pdf_path = None
        if self.attach_pdf_var.get():
            try:
                # Get default template
                from model.invoice_template import InvoiceTemplate
                template_model = InvoiceTemplate(self.db_path)
                template = template_model.get_default_template()
                
                if not template:
                    self.error_var.set("No default invoice template found")
                    return
                
                # Generate PDF
                pdf_generator = InvoicePDFGenerator(template, self.invoice)
                pdf_path = pdf_generator.generate_pdf()
                self.pdf_path = pdf_path
            except Exception as e:
                self.error_var.set(f"Failed to generate PDF: {str(e)}")
                return

        # Send email
        try:
            # Create custom email
            from email.mime.application import MIMEApplication
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText
            import smtplib
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{settings['from_name']} <{settings['from_email']}>"
            msg['To'] = self.recipient_var.get()
            msg['Subject'] = self.subject_var.get()
            
            # Add body
            msg.attach(MIMEText(self.body_var.get(), 'plain'))
            
            # Add PDF attachment if needed
            if pdf_path and os.path.exists(pdf_path):
                with open(pdf_path, 'rb') as file:
                    attachment = MIMEApplication(file.read(), _subtype="pdf")
                    attachment.add_header('Content-Disposition', 'attachment', 
                                         filename=f"Invoice_{self.invoice['invoice_number']}.pdf")
                    msg.attach(attachment)
            
            # Send email
            server = smtplib.SMTP(settings['smtp_server'], settings['smtp_port'])
            server.starttls()
            server.login(settings['username'], settings['password'])
            server.send_message(msg)
            server.quit()
            
            # Log email
            self.email_service._log_email(
                self.invoice_id,
                self.recipient_var.get(),
                self.subject_var.get(),
                self.body_var.get(),
                "Sent"
            )
            
            # Update invoice status if it's in draft
            if self.invoice['status'] == Invoice.STATUS_DRAFT:
                self.invoice_model.update_invoice_status(self.invoice_id, Invoice.STATUS_SENT)
            
            # Show success message
            messagebox.showinfo("Success", "Email sent successfully")
            
            # Call callback if provided
            if self.callback:
                self.callback()
            
            # Close dialog
            self.destroy()
        except Exception as e:
            # Log failed email
            self.email_service._log_email(
                self.invoice_id,
                self.recipient_var.get(),
                self.subject_var.get(),
                self.body_var.get(),
                "Failed",
                str(e)
            )
            
            self.error_var.set(f"Failed to send email: {str(e)}")

    def center_dialog(self):
        """Center the dialog on the parent window"""
        self.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get dialog size
        dialog_width = self.winfo_width()
        dialog_height = self.winfo_height()
        
        # Calculate position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        # Set position
        self.geometry(f"+{x}+{y}")

    def on_close(self):
        """Handle dialog close"""
        # Clean up temporary PDF file
        if self.pdf_path and os.path.exists(self.pdf_path):
            try:
                os.remove(self.pdf_path)
            except:
                pass
        
        self.destroy()
