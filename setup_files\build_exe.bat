@echo off
echo ===================================================
echo Building Cashbook executable...
echo ===================================================
echo.

REM Check if PyInstaller is installed
python -c "import PyInstaller" 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: PyInstaller is not installed.
    echo Please install it using: pip install pyinstaller
    pause
    exit /b 1
)

REM Clean previous build files
if exist "build" (
    echo Cleaning previous build files...
    rmdir /s /q "build"
)
if exist "dist" (
    echo Cleaning previous distribution files...
    rmdir /s /q "dist"
)

REM Build the executable
echo Building executable with PyInstaller...
pyinstaller --clean setup_files/cashbook.spec
if %ERRORLEVEL% neq 0 (
    echo.
    echo ===================================================
    echo Error: Build failed. Please check the output above.
    echo ===================================================
    pause
    exit /b 1
)

echo.
echo ===================================================
echo Build completed successfully!
echo The executable is located in the dist/Cashbook directory.
echo ===================================================
echo.
pause
