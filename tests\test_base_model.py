import os
import unittest
import sqlite3

from model.base_model import BaseModel


class TestModel(BaseModel):
    """Test implementation of BaseModel for testing"""
    
    def __init__(self, db_path):
        super().__init__(db_path)
    
    def table_name(self):
        return "test_items"
    
    def fields(self):
        return ["id", "name", "value", "active"]
    
    def primary_key(self):
        return "id"


class TestBaseModel(unittest.TestCase):
    """Test cases for the BaseModel class"""
    
    def setUp(self):
        """Set up test database and table"""
        self.db_path = "test_base_model.db"
        
        # Create test database and table
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_items (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                value INTEGER NOT NULL,
                active INTEGER NOT NULL DEFAULT 1
            )
        ''')
        
        # Add some test data
        cursor.execute("DELETE FROM test_items")  # Clear existing data
        
        test_data = [
            ("Item 1", 100, 1),
            ("Item 2", 200, 1),
            ("Item 3", 300, 0)
        ]
        
        cursor.executemany(
            "INSERT INTO test_items (name, value, active) VALUES (?, ?, ?)",
            test_data
        )
        
        conn.commit()
        conn.close()
        
        # Create model instance
        self.model = TestModel(self.db_path)
    
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
    
    def test_create(self):
        """Test creating a new record"""
        data = {
            "name": "New Item",
            "value": 400,
            "active": 1
        }
        
        record_id = self.model.create(data)
        
        # Verify record was created
        self.assertIsNotNone(record_id)
        self.assertGreater(record_id, 0)
        
        # Verify record exists in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM test_items WHERE id = ?", (record_id,))
        record = cursor.fetchone()
        conn.close()
        
        self.assertIsNotNone(record)
        self.assertEqual(record[1], "New Item")
        self.assertEqual(record[2], 400)
        self.assertEqual(record[3], 1)
    
    def test_read(self):
        """Test reading a record"""
        # Get the ID of an existing record
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM test_items WHERE name = ?", ("Item 1",))
        record_id = cursor.fetchone()[0]
        conn.close()
        
        # Read the record using the model
        record = self.model.read(record_id)
        
        # Verify record data
        self.assertIsNotNone(record)
        self.assertEqual(record["name"], "Item 1")
        self.assertEqual(record["value"], 100)
        self.assertEqual(record["active"], 1)
    
    def test_update(self):
        """Test updating a record"""
        # Get the ID of an existing record
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM test_items WHERE name = ?", ("Item 2",))
        record_id = cursor.fetchone()[0]
        conn.close()
        
        # Update the record
        data = {
            "name": "Updated Item",
            "value": 250
        }
        
        result = self.model.update(record_id, data)
        
        # Verify update was successful
        self.assertTrue(result)
        
        # Verify record was updated in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM test_items WHERE id = ?", (record_id,))
        record = cursor.fetchone()
        conn.close()
        
        self.assertEqual(record[1], "Updated Item")
        self.assertEqual(record[2], 250)
        self.assertEqual(record[3], 1)  # active should remain unchanged
    
    def test_delete(self):
        """Test deleting a record"""
        # Get the ID of an existing record
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM test_items WHERE name = ?", ("Item 3",))
        record_id = cursor.fetchone()[0]
        conn.close()
        
        # Delete the record
        result = self.model.delete(record_id)
        
        # Verify deletion was successful
        self.assertTrue(result)
        
        # Verify record no longer exists in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM test_items WHERE id = ?", (record_id,))
        count = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(count, 0)
    
    def test_find_all(self):
        """Test finding all records"""
        # Find all records
        records = self.model.find_all()
        
        # Verify all records were returned
        self.assertEqual(len(records), 3)
    
    def test_find_all_with_conditions(self):
        """Test finding records with conditions"""
        # Find active records
        records = self.model.find_all({"active": 1})
        
        # Verify only active records were returned
        self.assertEqual(len(records), 2)
        for record in records:
            self.assertEqual(record["active"], 1)
    
    def test_find_all_with_order_by(self):
        """Test finding records with ordering"""
        # Find all records ordered by value descending
        records = self.model.find_all(order_by="-value")
        
        # Verify records are in descending order
        self.assertEqual(len(records), 3)
        self.assertEqual(records[0]["value"], 300)
        self.assertEqual(records[1]["value"], 200)
        self.assertEqual(records[2]["value"], 100)
    
    def test_find_all_with_limit(self):
        """Test finding records with limit"""
        # Find records with limit
        records = self.model.find_all(limit=2)
        
        # Verify only 2 records were returned
        self.assertEqual(len(records), 2)
    
    def test_count(self):
        """Test counting records"""
        # Count all records
        count = self.model.count()
        
        # Verify count is correct
        self.assertEqual(count, 3)
    
    def test_count_with_conditions(self):
        """Test counting records with conditions"""
        # Count active records
        count = self.model.count({"active": 1})
        
        # Verify count is correct
        self.assertEqual(count, 2)
    
    def test_execute_query(self):
        """Test executing a custom query"""
        # Execute a custom query
        results = self.model.execute_query(
            "SELECT * FROM test_items WHERE value > ? ORDER BY value",
            (150,)
        )
        
        # Verify results
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["name"], "Item 2")
        self.assertEqual(results[1]["name"], "Item 3")


if __name__ == "__main__":
    unittest.main()
