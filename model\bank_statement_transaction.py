import sqlite3
from datetime import datetime
from model.base_model import BaseModel


class BankStatementTransaction(BaseModel):
    """
    Model class for bank statement transactions
    Represents transactions from bank statements for reconciliation
    """
    
    def __init__(self, db_path, id=None, reconciliation_session_id=None, date=None,
                 description=None, amount=0.0, reference=None, transaction_type=None,
                 matched_transaction_id=None, match_confidence=0.0, match_status='unmatched'):
        super().__init__(db_path)
        self.id = id
        self.reconciliation_session_id = reconciliation_session_id
        self.date = date
        self.description = description
        self.amount = amount
        self.reference = reference
        self.transaction_type = transaction_type
        self.matched_transaction_id = matched_transaction_id
        self.match_confidence = match_confidence
        self.match_status = match_status

    def table_name(self):
        return "bank_statement_transactions"

    def fields(self):
        return ["id", "reconciliation_session_id", "date", "description", "amount",
                "reference", "transaction_type", "matched_transaction_id", 
                "match_confidence", "match_status", "created_date"]

    def primary_key(self):
        return "id"

    def add_bank_statement_transaction(self, reconciliation_session_id, date, description, 
                                     amount, reference=None, transaction_type=None):
        """Add a new bank statement transaction"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute('''
                INSERT INTO bank_statement_transactions 
                (reconciliation_session_id, date, description, amount, reference, 
                 transaction_type, match_status, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (reconciliation_session_id, date, description, amount, reference,
                  transaction_type, 'unmatched', current_date))
            
            transaction_id = cursor.lastrowid
            conn.commit()
            return transaction_id
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error adding bank statement transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def bulk_add_bank_statement_transactions(self, reconciliation_session_id, transactions):
        """Add multiple bank statement transactions in bulk"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            transaction_data = []
            for trans in transactions:
                transaction_data.append((
                    reconciliation_session_id,
                    trans.get('date'),
                    trans.get('description', ''),
                    trans.get('amount', 0.0),
                    trans.get('reference', ''),
                    trans.get('transaction_type', ''),
                    'unmatched',
                    current_date
                ))
            
            cursor.executemany('''
                INSERT INTO bank_statement_transactions 
                (reconciliation_session_id, date, description, amount, reference, 
                 transaction_type, match_status, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', transaction_data)
            
            conn.commit()
            return cursor.rowcount
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error bulk adding bank statement transactions: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_bank_statement_transactions(self, reconciliation_session_id, match_status=None):
        """Get bank statement transactions for a reconciliation session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            query = '''
                SELECT bst.*, t.description as matched_description
                FROM bank_statement_transactions bst
                LEFT JOIN transactions t ON bst.matched_transaction_id = t.id
                WHERE bst.reconciliation_session_id = ?
            '''
            params = [reconciliation_session_id]
            
            if match_status:
                query += " AND bst.match_status = ?"
                params.append(match_status)
            
            query += " ORDER BY bst.date, bst.amount"
            
            cursor.execute(query, params)
            
            transactions = []
            for row in cursor.fetchall():
                transaction = {
                    'id': row[0],
                    'reconciliation_session_id': row[1],
                    'date': row[2],
                    'description': row[3],
                    'amount': row[4],
                    'reference': row[5],
                    'transaction_type': row[6],
                    'matched_transaction_id': row[7],
                    'match_confidence': row[8],
                    'match_status': row[9],
                    'created_date': row[10],
                    'matched_description': row[11]
                }
                transactions.append(transaction)
            
            return transactions
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving bank statement transactions: {str(e)}")
        finally:
            if conn:
                conn.close()

    def update_match_status(self, transaction_id, match_status, matched_transaction_id=None, 
                           match_confidence=0.0):
        """Update the match status of a bank statement transaction"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE bank_statement_transactions 
                SET match_status = ?, matched_transaction_id = ?, match_confidence = ?
                WHERE id = ?
            ''', (match_status, matched_transaction_id, match_confidence, transaction_id))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating match status: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_unmatched_transactions(self, reconciliation_session_id):
        """Get all unmatched bank statement transactions"""
        return self.get_bank_statement_transactions(reconciliation_session_id, 'unmatched')

    def get_matched_transactions(self, reconciliation_session_id):
        """Get all matched bank statement transactions"""
        return self.get_bank_statement_transactions(reconciliation_session_id, 'matched')

    def delete_bank_statement_transaction(self, transaction_id):
        """Delete a bank statement transaction"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM bank_statement_transactions WHERE id = ?", (transaction_id,))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting bank statement transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_transaction_by_id(self, transaction_id):
        """Get a specific bank statement transaction by ID"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT bst.*, t.description as matched_description
                FROM bank_statement_transactions bst
                LEFT JOIN transactions t ON bst.matched_transaction_id = t.id
                WHERE bst.id = ?
            ''', (transaction_id,))
            
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'reconciliation_session_id': row[1],
                    'date': row[2],
                    'description': row[3],
                    'amount': row[4],
                    'reference': row[5],
                    'transaction_type': row[6],
                    'matched_transaction_id': row[7],
                    'match_confidence': row[8],
                    'match_status': row[9],
                    'created_date': row[10],
                    'matched_description': row[11]
                }
            return None
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving bank statement transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_transactions_by_amount_range(self, reconciliation_session_id, min_amount, max_amount):
        """Get bank statement transactions within an amount range"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT bst.*, t.description as matched_description
                FROM bank_statement_transactions bst
                LEFT JOIN transactions t ON bst.matched_transaction_id = t.id
                WHERE bst.reconciliation_session_id = ? 
                AND bst.amount BETWEEN ? AND ?
                ORDER BY bst.date, bst.amount
            ''', (reconciliation_session_id, min_amount, max_amount))
            
            transactions = []
            for row in cursor.fetchall():
                transaction = {
                    'id': row[0],
                    'reconciliation_session_id': row[1],
                    'date': row[2],
                    'description': row[3],
                    'amount': row[4],
                    'reference': row[5],
                    'transaction_type': row[6],
                    'matched_transaction_id': row[7],
                    'match_confidence': row[8],
                    'match_status': row[9],
                    'created_date': row[10],
                    'matched_description': row[11]
                }
                transactions.append(transaction)
            
            return transactions
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving transactions by amount range: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_transactions_by_date_range(self, reconciliation_session_id, start_date, end_date):
        """Get bank statement transactions within a date range"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT bst.*, t.description as matched_description
                FROM bank_statement_transactions bst
                LEFT JOIN transactions t ON bst.matched_transaction_id = t.id
                WHERE bst.reconciliation_session_id = ? 
                AND bst.date BETWEEN ? AND ?
                ORDER BY bst.date, bst.amount
            ''', (reconciliation_session_id, start_date, end_date))
            
            transactions = []
            for row in cursor.fetchall():
                transaction = {
                    'id': row[0],
                    'reconciliation_session_id': row[1],
                    'date': row[2],
                    'description': row[3],
                    'amount': row[4],
                    'reference': row[5],
                    'transaction_type': row[6],
                    'matched_transaction_id': row[7],
                    'match_confidence': row[8],
                    'match_status': row[9],
                    'created_date': row[10],
                    'matched_description': row[11]
                }
                transactions.append(transaction)
            
            return transactions
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving transactions by date range: {str(e)}")
        finally:
            if conn:
                conn.close()
