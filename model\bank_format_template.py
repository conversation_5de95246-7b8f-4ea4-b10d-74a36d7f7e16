import sqlite3
import json
from datetime import datetime
from model.base_model import BaseModel


class BankFormatTemplate(BaseModel):
    """
    Model class for bank format templates
    Handles predefined bank statement formats for easy import
    """
    
    def __init__(self, db_path, id=None, name=None, bank_name=None, file_type=None,
                 field_mapping=None, date_format=None, delimiter=None, has_header=True,
                 skip_lines=0, is_active=True):
        super().__init__(db_path)
        self.id = id
        self.name = name
        self.bank_name = bank_name
        self.file_type = file_type
        self.field_mapping = field_mapping
        self.date_format = date_format
        self.delimiter = delimiter
        self.has_header = has_header
        self.skip_lines = skip_lines
        self.is_active = is_active

    def table_name(self):
        return "bank_format_templates"

    def fields(self):
        return ["id", "name", "bank_name", "file_type", "field_mapping", "date_format",
                "delimiter", "has_header", "skip_lines", "created_date", "is_active"]

    def primary_key(self):
        return "id"

    def create_template(self, name, bank_name, file_type, field_mapping, date_format=None,
                       delimiter=",", has_header=True, skip_lines=0):
        """Create a new bank format template"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Convert field mapping to JSON string if it's a dict
            if isinstance(field_mapping, dict):
                field_mapping = json.dumps(field_mapping)
            
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute('''
                INSERT INTO bank_format_templates 
                (name, bank_name, file_type, field_mapping, date_format, delimiter, 
                 has_header, skip_lines, created_date, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, bank_name, file_type, field_mapping, date_format, delimiter,
                  has_header, skip_lines, current_date, True))
            
            template_id = cursor.lastrowid
            conn.commit()
            return template_id
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error creating bank format template: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_all_templates(self):
        """Get all active bank format templates"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, name, bank_name, file_type, field_mapping, date_format,
                       delimiter, has_header, skip_lines, created_date, is_active
                FROM bank_format_templates 
                WHERE is_active = 1
                ORDER BY bank_name, name
            ''')
            
            templates = []
            for row in cursor.fetchall():
                template = {
                    'id': row[0],
                    'name': row[1],
                    'bank_name': row[2],
                    'file_type': row[3],
                    'field_mapping': json.loads(row[4]) if row[4] else {},
                    'date_format': row[5],
                    'delimiter': row[6],
                    'has_header': bool(row[7]),
                    'skip_lines': row[8],
                    'created_date': row[9],
                    'is_active': bool(row[10])
                }
                templates.append(template)
            
            return templates
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving bank format templates: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_template_by_id(self, template_id):
        """Get a specific bank format template by ID"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, name, bank_name, file_type, field_mapping, date_format,
                       delimiter, has_header, skip_lines, created_date, is_active
                FROM bank_format_templates 
                WHERE id = ?
            ''', (template_id,))
            
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'name': row[1],
                    'bank_name': row[2],
                    'file_type': row[3],
                    'field_mapping': json.loads(row[4]) if row[4] else {},
                    'date_format': row[5],
                    'delimiter': row[6],
                    'has_header': bool(row[7]),
                    'skip_lines': row[8],
                    'created_date': row[9],
                    'is_active': bool(row[10])
                }
            return None
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving bank format template: {str(e)}")
        finally:
            if conn:
                conn.close()

    def initialize_default_templates(self):
        """Initialize default bank format templates for common banks"""
        default_templates = [
            {
                'name': 'ANZ CSV Standard',
                'bank_name': 'ANZ',
                'file_type': 'CSV',
                'field_mapping': {
                    'date': 0,
                    'description': 1,
                    'amount': 2,
                    'balance': 3
                },
                'date_format': '%d/%m/%Y',
                'delimiter': ',',
                'has_header': True,
                'skip_lines': 0
            },
            {
                'name': 'ASB CSV Standard',
                'bank_name': 'ASB',
                'file_type': 'CSV',
                'field_mapping': {
                    'date': 0,
                    'description': 1,
                    'amount': 2,
                    'type': 3,
                    'balance': 4
                },
                'date_format': '%d/%m/%Y',
                'delimiter': ',',
                'has_header': True,
                'skip_lines': 1
            },
            {
                'name': 'Westpac CSV Standard',
                'bank_name': 'Westpac',
                'file_type': 'CSV',
                'field_mapping': {
                    'date': 0,
                    'description': 1,
                    'debit': 2,
                    'credit': 3,
                    'balance': 4
                },
                'date_format': '%d/%m/%Y',
                'delimiter': ',',
                'has_header': True,
                'skip_lines': 0
            },
            {
                'name': 'BNZ CSV Standard',
                'bank_name': 'BNZ',
                'file_type': 'CSV',
                'field_mapping': {
                    'date': 0,
                    'description': 1,
                    'amount': 2,
                    'balance': 3,
                    'reference': 4
                },
                'date_format': '%d/%m/%Y',
                'delimiter': ',',
                'has_header': True,
                'skip_lines': 0
            },
            {
                'name': 'Generic QIF',
                'bank_name': 'Generic',
                'file_type': 'QIF',
                'field_mapping': {
                    'date': 'D',
                    'amount': 'T',
                    'payee': 'P',
                    'memo': 'M',
                    'category': 'L'
                },
                'date_format': '%m/%d/%Y',
                'delimiter': None,
                'has_header': False,
                'skip_lines': 0
            }
        ]
        
        for template_data in default_templates:
            try:
                # Check if template already exists
                conn = sqlite3.connect(self.db_path, timeout=30)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id FROM bank_format_templates 
                    WHERE name = ? AND bank_name = ?
                ''', (template_data['name'], template_data['bank_name']))
                
                if not cursor.fetchone():
                    # Template doesn't exist, create it
                    self.create_template(**template_data)
                    
                conn.close()
                
            except Exception as e:
                print(f"Error creating default template {template_data['name']}: {str(e)}")
                continue

    def update_template(self, template_id, **kwargs):
        """Update an existing bank format template"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Build update query dynamically
            update_fields = []
            values = []
            
            for field, value in kwargs.items():
                if field in ['name', 'bank_name', 'file_type', 'field_mapping', 'date_format',
                           'delimiter', 'has_header', 'skip_lines', 'is_active']:
                    update_fields.append(f"{field} = ?")
                    if field == 'field_mapping' and isinstance(value, dict):
                        values.append(json.dumps(value))
                    else:
                        values.append(value)
            
            if update_fields:
                values.append(template_id)
                query = f"UPDATE bank_format_templates SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(query, values)
                conn.commit()
                return cursor.rowcount > 0
            
            return False
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating bank format template: {str(e)}")
        finally:
            if conn:
                conn.close()

    def delete_template(self, template_id):
        """Soft delete a bank format template"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE bank_format_templates 
                SET is_active = 0 
                WHERE id = ?
            ''', (template_id,))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting bank format template: {str(e)}")
        finally:
            if conn:
                conn.close()
