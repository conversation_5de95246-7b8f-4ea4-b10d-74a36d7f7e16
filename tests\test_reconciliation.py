import os
import sqlite3
import unittest
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from model.account import Account
from model.transaction import TransactionManager
from view.reconciliation_frame import ReconciliationFrame


class TestReconciliationFrame(unittest.TestCase):
    def setUp(self):
        # Create a test database
        self.db_path = "test_reconciliation.db"
        
        # Create database tables
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create accounts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL
            )
        """)
        
        # Create transactions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                FOREIG<PERSON> KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Create categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        # Create reconciliation_history table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reconciliation_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                statement_date TEXT NOT NULL,
                statement_balance REAL NOT NULL,
                book_balance REAL NOT NULL,
                reconciled_total REAL NOT NULL,
                difference REAL NOT NULL,
                created_date TEXT NOT NULL,
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
        """)
        
        # Insert test data
        # Add test account
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?)",
            ("Test Checking", "Checking", "USD", 1000.0, 1000.0)
        )
        self.account_id = cursor.lastrowid
        
        # Add test categories
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Food", "expense"))
        self.expense_category_id = cursor.lastrowid
        
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Salary", "income"))
        self.income_category_id = cursor.lastrowid
        
        # Add test transactions
        today = datetime.now().strftime("%Y-%m-%d")
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        
        # Add unreconciled transactions
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, category_id, type, reconciled) 
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (today, 100.0, self.account_id, "Test Income", self.income_category_id, "income", 0)
        )
        self.income_transaction_id = cursor.lastrowid
        
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, category_id, type, reconciled) 
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (yesterday, 50.0, self.account_id, "Test Expense", self.expense_category_id, "expense", 0)
        )
        self.expense_transaction_id = cursor.lastrowid
        
        # Add already reconciled transaction
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, category_id, type, reconciled) 
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (yesterday, 25.0, self.account_id, "Already Reconciled", self.expense_category_id, "expense", 1)
        )
        self.reconciled_transaction_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        # Create transaction manager
        self.transaction_manager = TransactionManager(self.db_path)
        
        # Create account model
        self.account_model = Account(self.db_path)
        
        # Mock parent for the frame
        self.parent = MagicMock()
        
        # Create reconciliation frame with mocked parent
        with patch('tkinter.ttk.Frame.__init__', return_value=None):
            with patch('view.reconciliation_frame.DatePicker'):
                self.frame = ReconciliationFrame(
                    self.parent,
                    "Test Company",
                    self.db_path,
                    MagicMock()  # Mock close_callback
                )
                
                # Mock UI components
                self.frame.unreconciled_tree = MagicMock()
                self.frame.reconciled_tree = MagicMock()
                self.frame.statement_balance_var = MagicMock()
                self.frame.book_balance_var = MagicMock()
                self.frame.reconciled_total_var = MagicMock()
                self.frame.difference_var = MagicMock()
                self.frame.difference_label = MagicMock()
                self.frame.status_var = MagicMock()
                self.frame.account_var = MagicMock()
                self.frame.balance_var = MagicMock()
                self.frame.date_picker = MagicMock()
                self.frame.start_button = MagicMock()
                self.frame.finish_button = MagicMock()
                self.frame.cancel_button = MagicMock()
                self.frame.mark_button = MagicMock()
                self.frame.unmark_button = MagicMock()
                self.frame.auto_match_button = MagicMock()
                
                # Set up account map
                self.frame.account_map = {"Test Checking": self.account_id}
                self.frame.selected_account_id = self.account_id
                self.frame.selected_account_name = "Test Checking"
    
    def tearDown(self):
        # Clean up after tests
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        # Clean up report files
        reports_dir = os.path.join(os.path.dirname(self.db_path), "reports")
        if os.path.exists(reports_dir):
            for file in os.listdir(reports_dir):
                if file.startswith("reconciliation_"):
                    os.remove(os.path.join(reports_dir, file))
            os.rmdir(reports_dir)
    
    def test_load_unreconciled_transactions(self):
        """Test loading unreconciled transactions"""
        # Mock the unreconciled_tree.insert method
        self.frame.unreconciled_tree.insert = MagicMock()
        
        # Call the method
        self.frame.load_unreconciled_transactions()
        
        # Check that insert was called twice (for our two unreconciled transactions)
        self.assertEqual(self.frame.unreconciled_tree.insert.call_count, 2)
    
    def test_mark_as_reconciled(self):
        """Test marking transactions as reconciled"""
        # Mock selection
        self.frame.unreconciled_tree.selection.return_value = ["item1"]
        
        # Mock item data
        self.frame.unreconciled_tree.item.return_value = {
            "values": (self.income_transaction_id, "2023-01-01", "Test Income", "$100.00", "income"),
            "tags": ("income",)
        }
        
        # Call the method
        self.frame.mark_as_reconciled()
        
        # Check that the transaction was moved to reconciled tree
        self.frame.reconciled_tree.insert.assert_called_once()
        self.frame.unreconciled_tree.delete.assert_called_once_with("item1")
    
    def test_unmark_as_reconciled(self):
        """Test unmarking transactions as reconciled"""
        # Mock selection
        self.frame.reconciled_tree.selection.return_value = ["item1"]
        
        # Mock item data
        self.frame.reconciled_tree.item.return_value = {
            "values": (self.income_transaction_id, "2023-01-01", "Test Income", "$100.00", "income"),
            "tags": ("income",)
        }
        
        # Call the method
        self.frame.unmark_as_reconciled()
        
        # Check that the transaction was moved to unreconciled tree
        self.frame.unreconciled_tree.insert.assert_called_once()
        self.frame.reconciled_tree.delete.assert_called_once_with("item1")
    
    def test_auto_match(self):
        """Test auto-matching transactions"""
        # Mock unreconciled items
        self.frame.unreconciled_tree.get_children.return_value = ["item1", "item2"]
        
        # Mock item data for income and expense with same amount
        self.frame.unreconciled_tree.item.side_effect = lambda item, option=None: {
            "item1": {
                "values": (self.income_transaction_id, datetime.now().strftime("%Y-%m-%d"), "Test Income", "$100.00", "income"),
                "tags": ("income",)
            },
            "item2": {
                "values": (self.expense_transaction_id, datetime.now().strftime("%Y-%m-%d"), "Test Expense", "$100.00", "expense"),
                "tags": ("expense",)
            }
        }[item] if option == "values" else {
            "item1": ("income",),
            "item2": ("expense",)
        }[item]
        
        # Set session active
        self.frame.session_active = True
        
        # Call the method
        with patch('tkinter.messagebox.showinfo'):
            self.frame.auto_match()
        
        # Check that selection_set was called with both items
        self.frame.unreconciled_tree.selection_set.assert_called_once_with(["item1", "item2"])
    
    def test_get_difference(self):
        """Test calculating the difference between statement and book balance"""
        # Set up mock values
        self.frame.statement_balance = 1000.0
        self.frame.book_balance_var.get.return_value = "$900.00"
        self.frame.reconciled_total_var.get.return_value = "$50.00"
        
        # Call the method
        difference = self.frame.get_difference()
        
        # Check the result (1000 - (900 + 50) = 50)
        self.assertEqual(difference, 50.0)
    
    def test_update_summary(self):
        """Test updating the reconciliation summary"""
        # Mock reconciled items
        self.frame.reconciled_tree.get_children.return_value = ["item1", "item2"]
        
        # Mock item data for income and expense
        self.frame.reconciled_tree.item.side_effect = lambda item, option=None: {
            "item1": {
                "values": (1, "2023-01-01", "Test Income", "$100.00", "income"),
                "tags": ("income",)
            },
            "item2": {
                "values": (2, "2023-01-01", "Test Expense", "$50.00", "expense"),
                "tags": ("expense",)
            }
        }[item]
        
        # Call the method
        self.frame.update_summary()
        
        # Check that reconciled_total_var was set correctly (100 - 50 = 50)
        self.frame.reconciled_total_var.set.assert_called_once_with("$50.00")
    
    @patch('os.makedirs')
    @patch('builtins.open', new_callable=unittest.mock.mock_open)
    def test_generate_reconciliation_report(self, mock_open, mock_makedirs):
        """Test generating a reconciliation report"""
        # Mock reconciled items
        self.frame.reconciled_tree.get_children.return_value = ["item1"]
        
        # Mock item data
        self.frame.reconciled_tree.item.return_value = {
            "values": (self.income_transaction_id, "2023-01-01", "Test Income", "$100.00", "income")
        }
        
        # Set up mock values
        self.frame.statement_balance = 1000.0
        self.frame.book_balance_var.get.return_value = "$900.00"
        self.frame.reconciled_total_var.get.return_value = "$100.00"
        self.frame.statement_date = datetime.now().date()
        
        # Call the method
        with patch('tkinter.messagebox.showinfo'):
            self.frame.generate_reconciliation_report()
        
        # Check that the file was opened for writing
        mock_open.assert_called_once()
        
        # Check that the report content was written
        mock_open().write.assert_called_once()
        
        # Verify some content in the report
        write_call_args = mock_open().write.call_args[0][0]
        self.assertIn("Reconciliation Report", write_call_args)
        self.assertIn("Statement Balance: $1000.00", write_call_args)
        self.assertIn("Book Balance: $900.00", write_call_args)
        self.assertIn("Reconciled Total: $100.00", write_call_args)
    
    def test_create_reconciliation_record(self):
        """Test creating a reconciliation record in the database"""
        # Set up mock values
        self.frame.statement_balance = 1000.0
        self.frame.book_balance_var.get.return_value = "$900.00"
        self.frame.reconciled_total_var.get.return_value = "$100.00"
        self.frame.statement_date = datetime.now().date()
        
        # Call the method
        self.frame.create_reconciliation_record()
        
        # Check that a record was created in the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM reconciliation_history")
        count = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(count, 1)
    
    def test_finish_reconciliation(self):
        """Test finishing the reconciliation process"""
        # Set up mock values
        self.frame.session_active = True
        self.frame.statement_balance = 1000.0
        self.frame.book_balance_var.get.return_value = "$900.00"
        self.frame.reconciled_total_var.get.return_value = "$100.00"
        self.frame.statement_date = datetime.now().date()
        
        # Mock reconciled items
        self.frame.reconciled_tree.get_children.return_value = ["item1"]
        
        # Mock item data
        self.frame.reconciled_tree.item.return_value = {
            "values": (self.income_transaction_id, "2023-01-01", "Test Income", "$100.00", "income")
        }
        
        # Call the method
        with patch('view.reconciliation_frame.messagebox.showinfo'):
            with patch.object(self.frame, 'create_reconciliation_record'):
                with patch.object(self.frame, 'generate_reconciliation_report'):
                    with patch.object(self.frame, 'reset_workspace'):
                        self.frame.finish_reconciliation()
        
        # Check that the transaction was marked as reconciled in the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT reconciled FROM transactions WHERE id = ?", (self.income_transaction_id,))
        reconciled = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(reconciled, 1)


class TestTransactionManagerReconciliation(unittest.TestCase):
    def setUp(self):
        # Create a test database
        self.db_path = "test_transaction_manager.db"
        
        # Create database tables
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create accounts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL
            )
        """)
        
        # Create transactions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Insert test data
        # Add test account
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?)",
            ("Test Checking", "Checking", "USD", 1000.0, 1000.0)
        )
        self.account_id = cursor.lastrowid
        
        # Add test transactions
        today = datetime.now().strftime("%Y-%m-%d")
        
        # Add unreconciled transactions
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, type, reconciled) 
               VALUES (?, ?, ?, ?, ?, ?)""",
            (today, 100.0, self.account_id, "Test Income", "income", 0)
        )
        self.income_transaction_id = cursor.lastrowid
        
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, type, reconciled) 
               VALUES (?, ?, ?, ?, ?, ?)""",
            (today, 50.0, self.account_id, "Test Expense", "expense", 0)
        )
        self.expense_transaction_id = cursor.lastrowid
        
        # Add already reconciled transaction
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, type, reconciled) 
               VALUES (?, ?, ?, ?, ?, ?)""",
            (today, 25.0, self.account_id, "Already Reconciled", "expense", 1)
        )
        self.reconciled_transaction_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        # Create transaction manager
        self.manager = TransactionManager(self.db_path)
    
    def tearDown(self):
        # Clean up after tests
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
    
    def test_get_unreconciled_transactions(self):
        """Test getting unreconciled transactions for an account"""
        # Get unreconciled transactions
        transactions = self.manager.get_unreconciled_transactions(self.account_id)
        
        # Check that we got the correct number of transactions
        self.assertEqual(len(transactions), 2)
        
        # Check that all transactions are unreconciled
        for transaction in transactions:
            self.assertEqual(transaction["reconciled"], 0)
        
        # Check that the transactions have the correct account_id
        for transaction in transactions:
            self.assertEqual(transaction["account_id"], self.account_id)
    
    def test_mark_as_reconciled(self):
        """Test marking a transaction as reconciled"""
        # Mark transaction as reconciled
        result = self.manager.mark_as_reconciled(self.income_transaction_id, 1)
        
        # Check that the operation was successful
        self.assertTrue(result)
        
        # Check that the transaction is now reconciled
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT reconciled FROM transactions WHERE id = ?", (self.income_transaction_id,))
        reconciled = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(reconciled, 1)
    
    def test_mark_as_unreconciled(self):
        """Test marking a transaction as unreconciled"""
        # Mark transaction as unreconciled
        result = self.manager.mark_as_reconciled(self.reconciled_transaction_id, 0)
        
        # Check that the operation was successful
        self.assertTrue(result)
        
        # Check that the transaction is now unreconciled
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT reconciled FROM transactions WHERE id = ?", (self.reconciled_transaction_id,))
        reconciled = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(reconciled, 0)
    
    def test_transaction_methods(self):
        """Test transaction management methods"""
        # Begin transaction
        self.manager.begin_transaction()
        
        # Check that connection is not None
        self.assertIsNotNone(self.manager.connection)
        
        # Mark transaction as reconciled within the transaction
        self.manager.mark_as_reconciled(self.income_transaction_id, 1)
        
        # Commit transaction
        self.manager.commit_transaction()
        
        # Check that connection is None after commit
        self.assertIsNone(self.manager.connection)
        
        # Check that the transaction is now reconciled
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT reconciled FROM transactions WHERE id = ?", (self.income_transaction_id,))
        reconciled = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(reconciled, 1)
    
    def test_transaction_rollback(self):
        """Test transaction rollback"""
        # Begin transaction
        self.manager.begin_transaction()
        
        # Mark transaction as reconciled within the transaction
        self.manager.mark_as_reconciled(self.income_transaction_id, 1)
        
        # Rollback transaction
        self.manager.rollback_transaction()
        
        # Check that connection is None after rollback
        self.assertIsNone(self.manager.connection)
        
        # Check that the transaction is still unreconciled
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT reconciled FROM transactions WHERE id = ?", (self.income_transaction_id,))
        reconciled = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(reconciled, 0)


if __name__ == '__main__':
    unittest.main()
