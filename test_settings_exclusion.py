#!/usr/bin/env python3
"""
Test script to verify that settings.db is properly excluded from all company operations
"""

import os
import shutil
import sqlite3
import sys
import tempfile

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def create_test_environment():
    """Create a test environment with settings.db and test company databases"""
    # Create test settings.db
    settings_conn = sqlite3.connect('settings.db')
    settings_cursor = settings_conn.cursor()

    # Create settings table (not company tables)
    settings_cursor.execute('''
        CREATE TABLE IF NOT EXISTS application_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            setting_type TEXT DEFAULT 'string',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
        )
    ''')

    settings_conn.commit()
    settings_conn.close()

    # Create a test company database
    company_conn = sqlite3.connect('test_company.db')
    company_cursor = company_conn.cursor()

    # Create company tables
    company_cursor.execute('''
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            currency TEXT NOT NULL DEFAULT 'USD',
            opening_balance REAL NOT NULL DEFAULT 0,
            current_balance REAL NOT NULL DEFAULT 0,
            description TEXT,
            created_date TEXT NOT NULL
        )
    ''')

    company_cursor.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY,
            date TEXT NOT NULL,
            description TEXT NOT NULL,
            amount REAL NOT NULL,
            type TEXT NOT NULL,
            category TEXT,
            account_id INTEGER,
            reconciled BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
    ''')

    company_cursor.execute('''
        CREATE TABLE IF NOT EXISTS metadata (
            key TEXT PRIMARY KEY,
            value TEXT
        )
    ''')

    # Insert test data (use INSERT OR REPLACE to handle existing data)
    company_cursor.execute(
        "INSERT OR REPLACE INTO metadata (key, value) VALUES (?, ?)",
        ("created_date", "2025-01-29 10:00:00")
    )

    company_cursor.execute(
        "INSERT OR REPLACE INTO metadata (key, value) VALUES (?, ?)",
        ("company_name", "Test Company")
    )

    company_conn.commit()
    company_conn.close()

    print("✅ Test environment created")


def test_client_frame_exclusion():
    """Test that client frame excludes settings.db"""
    print("🧪 Testing client frame exclusion...")

    try:
        from view.client_frame import ClientFrame

        # Simulate the exclusion logic from client frame
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        if 'settings.db' in db_files:
            print("   ❌ FAILED: settings.db found in client frame company list")
            return False
        else:
            print("   ✅ PASSED: settings.db properly excluded from client frame")
            return True

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def test_admin_frame_exclusion():
    """Test that admin frame excludes settings.db"""
    print("🧪 Testing admin frame exclusion...")

    try:
        # Simulate the exclusion logic from admin frame (both methods)
        excluded_files = ['users.db', 'settings.db']

        # Test load_companies method logic
        db_files_load = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        # Test filter_companies method logic
        db_files_filter = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        if 'settings.db' in db_files_load or 'settings.db' in db_files_filter:
            print("   ❌ FAILED: settings.db found in admin frame company list")
            return False
        else:
            print("   ✅ PASSED: settings.db properly excluded from admin frame")
            return True

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def test_dashboard_exclusion():
    """Test that dashboard excludes settings.db"""
    print("🧪 Testing dashboard exclusion...")

    try:
        # Simulate the exclusion logic from dashboard
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        if 'settings.db' in db_files:
            print("   ❌ FAILED: settings.db found in dashboard company list")
            return False
        else:
            print("   ✅ PASSED: settings.db properly excluded from dashboard")
            return True

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def test_migration_exclusion():
    """Test that database migration excludes settings.db"""
    print("🧪 Testing migration exclusion...")

    try:
        from utils.database_migration import DatabaseMigration

        # Test the migration logic
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        if 'settings.db' in db_files:
            print("   ❌ FAILED: settings.db found in migration target list")
            return False
        else:
            print("   ✅ PASSED: settings.db properly excluded from migration")
            return True

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def test_settings_db_structure():
    """Test that settings.db has the correct structure (not company structure)"""
    print("🧪 Testing settings.db structure...")

    try:
        if not os.path.exists('settings.db'):
            print("   ℹ️  settings.db does not exist - this is normal")
            return True

        conn = sqlite3.connect('settings.db')
        cursor = conn.cursor()

        # Check for settings tables (should exist)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='application_settings'")
        settings_table = cursor.fetchone()

        # Check for company tables (should NOT exist)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
        transactions_table = cursor.fetchone()

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='accounts'")
        accounts_table = cursor.fetchone()

        conn.close()

        if settings_table and not transactions_table and not accounts_table:
            print("   ✅ PASSED: settings.db has correct structure (settings tables, no company tables)")
            return True
        elif not settings_table and not transactions_table and not accounts_table:
            print("   ℹ️  settings.db is empty - this is normal for new installations")
            return True
        else:
            print("   ❌ FAILED: settings.db has incorrect structure")
            print(f"      Settings table: {'✓' if settings_table else '✗'}")
            print(f"      Transactions table: {'✗ (should not exist)' if transactions_table else '✓'}")
            print(f"      Accounts table: {'✗ (should not exist)' if accounts_table else '✓'}")
            return False

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def test_company_opening_protection():
    """Test that trying to open settings.db as a company fails gracefully"""
    print("🧪 Testing company opening protection...")

    try:
        if not os.path.exists('settings.db'):
            print("   ℹ️  settings.db does not exist - skipping test")
            return True

        # Try to open settings.db as if it were a company database
        conn = sqlite3.connect('settings.db')
        cursor = conn.cursor()

        try:
            # This should fail because settings.db doesn't have transactions table
            cursor.execute("SELECT COUNT(*) FROM transactions")
            result = cursor.fetchone()
            conn.close()

            print("   ❌ FAILED: settings.db was opened as company database (it has transactions table)")
            return False

        except sqlite3.OperationalError as e:
            if "no such table: transactions" in str(e):
                conn.close()
                print("   ✅ PASSED: settings.db correctly fails when opened as company database")
                return True
            else:
                conn.close()
                print(f"   ❌ UNEXPECTED ERROR: {e}")
                return False

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def cleanup_test_environment():
    """Clean up test files"""
    test_files = ['test_company.db']

    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🧹 Cleaned up {file}")
            except PermissionError:
                print(f"⚠️  Could not remove {file} (file in use)")
            except Exception as e:
                print(f"⚠️  Error removing {file}: {e}")


def main():
    """Run all exclusion tests"""
    print("=" * 60)
    print("SETTINGS.DB EXCLUSION VERIFICATION TEST")
    print("=" * 60)
    print()

    # Create test environment
    create_test_environment()
    print()

    tests = [
        ("Client Frame Exclusion", test_client_frame_exclusion),
        ("Admin Frame Exclusion", test_admin_frame_exclusion),
        ("Dashboard Exclusion", test_dashboard_exclusion),
        ("Migration Exclusion", test_migration_exclusion),
        ("Settings DB Structure", test_settings_db_structure),
        ("Company Opening Protection", test_company_opening_protection)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"🔍 {test_name}")
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"   ❌ UNEXPECTED ERROR: {e}")
            print()

    # Cleanup
    cleanup_test_environment()

    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")

    if passed == total:
        print()
        print("🎉 ALL EXCLUSION TESTS PASSED!")
        print("settings.db is properly excluded from all company operations:")
        print("  ✅ Client dashboard won't show settings.db as a company")
        print("  ✅ Admin dashboard won't show settings.db as a company")
        print("  ✅ Dashboard won't show settings.db as a company")
        print("  ✅ Database migration won't try to migrate settings.db")
        print("  ✅ settings.db has correct structure for settings, not company data")
        print("  ✅ Attempting to open settings.db as a company fails safely")
    else:
        print()
        print("⚠️  SOME EXCLUSION TESTS FAILED")
        print("settings.db may still appear as a company in some interfaces.")
        print("Please review the failed tests and fix the exclusion logic.")

    print()
    print("💡 The error 'no such table: transactions' should no longer occur")
    print("   when trying to open settings.db as a company.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Tests cancelled by user")
        cleanup_test_environment()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        cleanup_test_environment()
        import traceback
        traceback.print_exc()
