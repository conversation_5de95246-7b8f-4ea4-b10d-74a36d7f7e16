import os
import sqlite3
import unittest
from unittest.mock import MagicMock, patch

from model.rules_engine import RulesEngine
from model.transaction import TransactionManager


class TestRulesEngine(unittest.TestCase):
    def setUp(self):
        # Create a test database
        self.db_path = "test_rules_engine.db"
        
        # Create database tables
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        # Create import_rules table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS import_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                pattern TEXT NOT NULL,
                category_id INTEGER,
                is_regex INTEGER DEFAULT 0,
                is_case_sensitive INTEGER DEFAULT 0,
                priority INTEGER DEFAULT 0,
                FOREIG<PERSON> KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Insert test categories
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Food", "expense"))
        self.expense_category_id = cursor.lastrowid
        
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Salary", "income"))
        self.income_category_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        # Create rules engine
        self.rules_engine = RulesEngine(self.db_path)
    
    def tearDown(self):
        # Clean up after tests
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
    
    def test_add_rule(self):
        """Test adding a new rule"""
        # Add a rule
        rule_id = self.rules_engine.add_rule(
            "Test Rule",
            "Grocery",
            self.expense_category_id,
            is_regex=False,
            is_case_sensitive=False,
            priority=10
        )
        
        # Check that the rule was added
        self.assertIsNotNone(rule_id)
        
        # Get the rule
        rule = self.rules_engine.get_rule(rule_id)
        
        # Check rule properties
        self.assertEqual(rule["name"], "Test Rule")
        self.assertEqual(rule["pattern"], "Grocery")
        self.assertEqual(rule["category_id"], self.expense_category_id)
        self.assertEqual(rule["is_regex"], 0)
        self.assertEqual(rule["is_case_sensitive"], 0)
        self.assertEqual(rule["priority"], 10)
    
    def test_update_rule(self):
        """Test updating an existing rule"""
        # Add a rule
        rule_id = self.rules_engine.add_rule(
            "Test Rule",
            "Grocery",
            self.expense_category_id
        )
        
        # Update the rule
        self.rules_engine.update_rule(
            rule_id,
            "Updated Rule",
            "Supermarket",
            self.expense_category_id,
            is_regex=True,
            is_case_sensitive=True,
            priority=20
        )
        
        # Get the updated rule
        rule = self.rules_engine.get_rule(rule_id)
        
        # Check updated properties
        self.assertEqual(rule["name"], "Updated Rule")
        self.assertEqual(rule["pattern"], "Supermarket")
        self.assertEqual(rule["is_regex"], 1)
        self.assertEqual(rule["is_case_sensitive"], 1)
        self.assertEqual(rule["priority"], 20)
    
    def test_delete_rule(self):
        """Test deleting a rule"""
        # Add a rule
        rule_id = self.rules_engine.add_rule(
            "Test Rule",
            "Grocery",
            self.expense_category_id
        )
        
        # Delete the rule
        result = self.rules_engine.delete_rule(rule_id)
        
        # Check that the operation was successful
        self.assertTrue(result)
        
        # Try to get the deleted rule
        rule = self.rules_engine.get_rule(rule_id)
        
        # Check that the rule is gone
        self.assertIsNone(rule)
    
    def test_get_all_rules(self):
        """Test getting all rules"""
        # Add some rules
        self.rules_engine.add_rule("Rule 1", "Grocery", self.expense_category_id, priority=10)
        self.rules_engine.add_rule("Rule 2", "Salary", self.income_category_id, priority=20)
        self.rules_engine.add_rule("Rule 3", "Restaurant", self.expense_category_id, priority=30)
        
        # Get all rules
        rules = self.rules_engine.get_all_rules()
        
        # Check that we got the correct number of rules
        self.assertEqual(len(rules), 3)
        
        # Check that rules are sorted by priority
        self.assertEqual(rules[0]["name"], "Rule 3")
        self.assertEqual(rules[1]["name"], "Rule 2")
        self.assertEqual(rules[2]["name"], "Rule 1")
    
    def test_apply_rules_simple_match(self):
        """Test applying rules with simple pattern matching"""
        # Add some rules
        self.rules_engine.add_rule("Grocery Rule", "Grocery", self.expense_category_id, priority=10)
        self.rules_engine.add_rule("Salary Rule", "Salary", self.income_category_id, priority=20)
        
        # Apply rules to matching descriptions
        category_id1 = self.rules_engine.apply_rules("Grocery Store Purchase")
        category_id2 = self.rules_engine.apply_rules("Monthly Salary Payment")
        
        # Check that the correct category IDs were returned
        self.assertEqual(category_id1, self.expense_category_id)
        self.assertEqual(category_id2, self.income_category_id)
        
        # Apply rules to non-matching description
        category_id3 = self.rules_engine.apply_rules("Gas Station")
        
        # Check that no category ID was returned
        self.assertIsNone(category_id3)
    
    def test_apply_rules_regex_match(self):
        """Test applying rules with regex pattern matching"""
        # Add some rules with regex patterns
        self.rules_engine.add_rule(
            "Grocery Regex",
            r"Groc\\w+",
            self.expense_category_id,
            is_regex=True,
            priority=10
        )
        
        self.rules_engine.add_rule(
            "Salary Regex",
            r"Sal\\w+\\s+Dep\\w+",
            self.income_category_id,
            is_regex=True,
            priority=20
        )
        
        # Apply rules to matching descriptions
        category_id1 = self.rules_engine.apply_rules("Grocery Store")
        category_id2 = self.rules_engine.apply_rules("Salary Deposit")
        
        # Check that the correct category IDs were returned
        self.assertEqual(category_id1, self.expense_category_id)
        self.assertEqual(category_id2, self.income_category_id)
    
    def test_apply_rules_case_sensitivity(self):
        """Test applying rules with case sensitivity"""
        # Add rules with different case sensitivity
        self.rules_engine.add_rule(
            "Case Sensitive",
            "Grocery",
            self.expense_category_id,
            is_case_sensitive=True,
            priority=10
        )
        
        self.rules_engine.add_rule(
            "Case Insensitive",
            "salary",
            self.income_category_id,
            is_case_sensitive=False,
            priority=20
        )
        
        # Apply rules to descriptions with different cases
        category_id1 = self.rules_engine.apply_rules("Grocery Store")  # Should match
        category_id2 = self.rules_engine.apply_rules("grocery store")  # Should not match
        category_id3 = self.rules_engine.apply_rules("Salary Payment")  # Should match
        category_id4 = self.rules_engine.apply_rules("SALARY PAYMENT")  # Should match
        
        # Check results
        self.assertEqual(category_id1, self.expense_category_id)
        self.assertIsNone(category_id2)
        self.assertEqual(category_id3, self.income_category_id)
        self.assertEqual(category_id4, self.income_category_id)
    
    def test_apply_rules_priority(self):
        """Test applying rules with different priorities"""
        # Add rules with different priorities
        self.rules_engine.add_rule(
            "Low Priority",
            "Payment",
            self.expense_category_id,
            priority=10
        )
        
        self.rules_engine.add_rule(
            "High Priority",
            "Salary Payment",
            self.income_category_id,
            priority=20
        )
        
        # Apply rules to a description that matches both patterns
        category_id = self.rules_engine.apply_rules("Salary Payment")
        
        # Check that the high priority rule was applied
        self.assertEqual(category_id, self.income_category_id)
    
    def test_test_rule(self):
        """Test the rule testing functionality"""
        # Test simple pattern matching
        result1 = self.rules_engine.test_rule("Grocery", "Grocery Store", False, False)
        result2 = self.rules_engine.test_rule("Grocery", "Restaurant", False, False)
        
        self.assertTrue(result1)
        self.assertFalse(result2)
        
        # Test regex pattern matching
        result3 = self.rules_engine.test_rule(r"Groc\\w+", "Grocery", True, False)
        result4 = self.rules_engine.test_rule(r"Sal\\w+", "Restaurant", True, False)
        
        self.assertTrue(result3)
        self.assertFalse(result4)
        
        # Test case sensitivity
        result5 = self.rules_engine.test_rule("Grocery", "GROCERY", False, False)
        result6 = self.rules_engine.test_rule("Grocery", "GROCERY", False, True)
        
        self.assertTrue(result5)
        self.assertFalse(result6)


class TestTransactionImport(unittest.TestCase):
    def setUp(self):
        # Create a test database
        self.db_path = "test_transaction_import.db"
        
        # Create database tables
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create accounts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL,
                created_date TEXT NOT NULL
            )
        """)
        
        # Create categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        # Create transactions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        # Create mapping_profiles table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mapping_profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                mapping TEXT NOT NULL
            )
        """)
        
        # Insert test data
        # Add test account
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance, created_date) VALUES (?, ?, ?, ?, ?, ?)",
            ("Test Checking", "Checking", "USD", 1000.0, 1000.0, "2023-01-01")
        )
        self.account_id = cursor.lastrowid
        
        # Add test categories
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Food", "expense"))
        self.expense_category_id = cursor.lastrowid
        
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Salary", "income"))
        self.income_category_id = cursor.lastrowid
        
        # Add test transactions
        cursor.execute(
            """INSERT INTO transactions 
               (date, amount, account_id, description, category_id, type) 
               VALUES (?, ?, ?, ?, ?, ?)""",
            ("2023-01-15", 100.0, self.account_id, "Existing Transaction", self.expense_category_id, "expense")
        )
        
        conn.commit()
        conn.close()
        
        # Create transaction manager
        self.transaction_manager = TransactionManager(self.db_path)
    
    def tearDown(self):
        # Clean up after tests
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
    
    def test_is_duplicate_transaction(self):
        """Test checking for duplicate transactions"""
        # Check for existing transaction
        is_duplicate = self.transaction_manager.is_duplicate_transaction(
            "2023-01-15", 100.0, "Existing Transaction", self.account_id
        )
        
        # Check that it's detected as a duplicate
        self.assertTrue(is_duplicate)
        
        # Check for non-existing transaction
        is_duplicate = self.transaction_manager.is_duplicate_transaction(
            "2023-01-16", 200.0, "New Transaction", self.account_id
        )
        
        # Check that it's not detected as a duplicate
        self.assertFalse(is_duplicate)
    
    def test_delete_duplicate_transaction(self):
        """Test deleting a duplicate transaction"""
        # Delete the existing transaction
        result = self.transaction_manager.delete_duplicate_transaction(
            "2023-01-15", 100.0, "Existing Transaction", self.account_id
        )
        
        # Check that the operation was successful
        self.assertTrue(result)
        
        # Check that the transaction is gone
        is_duplicate = self.transaction_manager.is_duplicate_transaction(
            "2023-01-15", 100.0, "Existing Transaction", self.account_id
        )
        
        self.assertFalse(is_duplicate)
    
    def test_get_category_by_name(self):
        """Test getting a category by name"""
        # Get existing category
        category = self.transaction_manager.get_category_by_name("Food")
        
        # Check that we got the correct category
        self.assertIsNotNone(category)
        self.assertEqual(category["id"], self.expense_category_id)
        self.assertEqual(category["name"], "Food")
        self.assertEqual(category["type"], "expense")
        
        # Get non-existing category
        category = self.transaction_manager.get_category_by_name("NonExistent")
        
        # Check that we got None
        self.assertIsNone(category)
    
    def test_get_accounts(self):
        """Test getting all accounts"""
        # Get accounts
        accounts = self.transaction_manager.get_accounts()
        
        # Check that we got the correct number of accounts
        self.assertEqual(len(accounts), 1)
        
        # Check account properties
        self.assertEqual(accounts[0]["id"], self.account_id)
        self.assertEqual(accounts[0]["name"], "Test Checking")
        self.assertEqual(accounts[0]["type"], "Checking")
        self.assertEqual(accounts[0]["currency"], "USD")
        self.assertEqual(accounts[0]["opening_balance"], 1000.0)
        self.assertEqual(accounts[0]["current_balance"], 1000.0)


if __name__ == '__main__':
    unittest.main()
