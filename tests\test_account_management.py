import os
import sqlite3
import tkinter as tk
import unittest

from model.database import Database
from view.account_management_frame import AccountManagementFrame


class TestAccountManagementFrame(unittest.TestCase):
    """Test cases for the AccountManagementFrame"""

    def setUp(self):
        """Set up test environment"""
        self.root = tk.Tk()

        # Create a test database
        self.db = Database()
        self.db_path = self.db.init_company_db("Test Company")

        # Create test accounts
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance, created_date) VALUES (?, ?, ?, ?, ?, ?)",
            ("Checking Account", "Checking", "USD", 1000.0, 1000.0, "2023-01-01")
        )

        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance, created_date) VALUES (?, ?, ?, ?, ?, ?)",
            ("Savings Account", "Savings", "EUR", 2000.0, 2000.0, "2023-01-01")
        )

        conn.commit()
        conn.close()

        # Create the account management frame
        self.close_called = False
        self.frame = AccountManagementFrame(
            self.root,
            "Test Company",
            self.db_path,
            self.mock_close_callback
        )
        self.frame.pack(fill="both", expand=True)

    def tearDown(self):
        """Clean up test environment"""
        self.root.destroy()

        # Remove test database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def mock_close_callback(self):
        """Mock callback for close button"""
        self.close_called = True

    def test_initialization(self):
        """Test that the frame initializes correctly"""
        # Check that the frame has the correct title
        self.assertEqual(self.frame.title, "Account Management - Test Company")

        # Check that accounts were loaded
        self.assertEqual(len(self.frame.account_tree.get_children()), 2)

        # Check that the form is disabled initially
        self.assertEqual(self.frame.save_button.cget("state"), "disabled")
        self.assertEqual(self.frame.cancel_button.cget("state"), "disabled")

    def test_load_accounts(self):
        """Test loading accounts into the treeview"""
        # Clear the treeview
        for item in self.frame.account_tree.get_children():
            self.frame.account_tree.delete(item)

        # Load accounts
        self.frame.load_accounts()

        # Check that accounts were loaded
        self.assertEqual(len(self.frame.account_tree.get_children()), 2)

        # Check account data
        account_names = []
        for item in self.frame.account_tree.get_children():
            account_names.append(self.frame.account_tree.item(item, "values")[1])

        self.assertIn("Checking Account", account_names)
        self.assertIn("Savings Account", account_names)

    def test_add_account_form(self):
        """Test preparing the form for adding a new account"""
        # Initially form should be disabled
        self.assertEqual(self.frame.save_button.cget("state"), "disabled")

        # Prepare form for adding
        self.frame.add_account()

        # Form should be enabled
        self.assertEqual(self.frame.save_button.cget("state"), "normal")

        # Fields should be cleared
        self.assertEqual(self.frame.name_var.get(), "")
        self.assertEqual(self.frame.type_var.get(), "Checking")
        self.assertEqual(self.frame.currency_var.get(), "USD")
        self.assertEqual(self.frame.balance_var.get(), 0.0)
        self.assertEqual(self.frame.description_var.get(), "")

        # Current operation should be "add"
        self.assertEqual(self.frame.current_operation, "add")
        self.assertIsNone(self.frame.current_account_id)

    def test_cancel_edit(self):
        """Test cancelling the edit operation"""
        # Prepare form for adding
        self.frame.add_account()

        # Form should be enabled
        self.assertEqual(self.frame.save_button.cget("state"), "normal")

        # Cancel edit
        self.frame.cancel_edit()

        # Form should be disabled
        self.assertEqual(self.frame.save_button.cget("state"), "disabled")

        # Error should be cleared
        self.assertEqual(self.frame.error_var.get(), "")

    def test_close_callback(self):
        """Test the close callback"""
        # Click the close button
        self.frame.close_callback()

        # Check that the callback was called
        self.assertTrue(self.close_called)

    def test_show_context_menu(self):
        """Test showing the context menu"""
        # Get the first account
        items = self.frame.account_tree.get_children()
        if not items:
            self.skipTest("No accounts to test with")

        # Create a mock event
        event = type('Event', (), {})()
        event.x_root = 0
        event.y_root = 0
        event.y = 10  # Position over the first item

        # Show context menu
        self.frame.show_context_menu(event)

        # Check that the item was selected
        self.assertEqual(len(self.frame.account_tree.selection()), 1)


if __name__ == "__main__":
    unittest.main()
