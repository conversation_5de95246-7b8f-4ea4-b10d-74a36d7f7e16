import sqlite3
from datetime import datetime
from model.base_model import BaseModel


class SplitTransaction(BaseModel):
    """
    Model for managing split transactions
    A split transaction allows a single transaction to be divided across multiple categories or accounts
    """
    
    def __init__(self, db_path):
        super().__init__(db_path)
        self._create_tables()
    
    def table_name(self):
        return "split_transactions"
    
    def fields(self):
        return [
            'id', 'parent_transaction_id', 'date', 'description', 'total_amount',
            'account_id', 'type', 'created_date', 'created_by', 'notes'
        ]
    
    def primary_key(self):
        return 'id'
    
    def _create_tables(self):
        """Create split transaction tables if they don't exist"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Create split transactions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS split_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    parent_transaction_id INTEGER,
                    date TEXT NOT NULL,
                    description TEXT NOT NULL,
                    total_amount REAL NOT NULL,
                    account_id INTEGER NOT NULL,
                    type TEXT NOT NULL,
                    created_date TEXT NOT NULL,
                    created_by TEXT,
                    notes TEXT,
                    FOREIGN KEY (parent_transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
                    FOREIGN KEY (account_id) REFERENCES accounts(id)
                )
            ''')
            
            # Create split transaction lines table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS split_transaction_lines (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    split_transaction_id INTEGER NOT NULL,
                    category_id INTEGER,
                    account_id INTEGER,
                    amount REAL NOT NULL,
                    description TEXT,
                    percentage REAL,
                    line_order INTEGER DEFAULT 0,
                    FOREIGN KEY (split_transaction_id) REFERENCES split_transactions(id) ON DELETE CASCADE,
                    FOREIGN KEY (category_id) REFERENCES categories(id),
                    FOREIGN KEY (account_id) REFERENCES accounts(id)
                )
            ''')
            
            # Create index for better performance
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_split_transactions_parent 
                ON split_transactions(parent_transaction_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_split_transaction_lines_split 
                ON split_transaction_lines(split_transaction_id)
            ''')
            
            conn.commit()
            
        except sqlite3.Error as e:
            raise Exception(f"Error creating split transaction tables: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def create_split_transaction(self, parent_transaction_id, date, description, total_amount, 
                                account_id, transaction_type, split_lines, created_by=None, notes=None):
        """
        Create a new split transaction with multiple lines
        
        Args:
            parent_transaction_id: ID of the original transaction (can be None for new splits)
            date: Transaction date
            description: Transaction description
            total_amount: Total amount of the transaction
            account_id: Account ID
            transaction_type: Type of transaction (income/expense)
            split_lines: List of split line dictionaries with keys: category_id, account_id, amount, description
            created_by: User who created the split
            notes: Additional notes
        
        Returns:
            Split transaction ID if successful, None otherwise
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Validate split lines total equals transaction total
            split_total = sum(line['amount'] for line in split_lines)
            if abs(split_total - total_amount) > 0.01:  # Allow for small rounding differences
                raise ValueError(f"Split lines total ({split_total:.2f}) does not match transaction total ({total_amount:.2f})")
            
            # Create split transaction record
            created_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute('''
                INSERT INTO split_transactions 
                (parent_transaction_id, date, description, total_amount, account_id, type, created_date, created_by, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (parent_transaction_id, date, description, total_amount, account_id, 
                  transaction_type, created_date, created_by, notes))
            
            split_transaction_id = cursor.lastrowid
            
            # Create split transaction lines
            for i, line in enumerate(split_lines):
                percentage = (line['amount'] / total_amount * 100) if total_amount != 0 else 0
                
                cursor.execute('''
                    INSERT INTO split_transaction_lines 
                    (split_transaction_id, category_id, account_id, amount, description, percentage, line_order)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (split_transaction_id, line.get('category_id'), line.get('account_id'), 
                      line['amount'], line.get('description', ''), percentage, i))
            
            # If this is splitting an existing transaction, mark it as split
            if parent_transaction_id:
                cursor.execute('''
                    UPDATE transactions 
                    SET description = description || ' (SPLIT)', 
                        amount = 0 
                    WHERE id = ?
                ''', (parent_transaction_id,))
            
            conn.commit()
            return split_transaction_id
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error creating split transaction: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def get_split_transaction(self, split_transaction_id):
        """Get a split transaction with its lines"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get split transaction details
            cursor.execute('''
                SELECT st.*, a.name as account_name
                FROM split_transactions st
                LEFT JOIN accounts a ON st.account_id = a.id
                WHERE st.id = ?
            ''', (split_transaction_id,))
            
            split_transaction = cursor.fetchone()
            if not split_transaction:
                return None
            
            # Convert to dictionary
            columns = [desc[0] for desc in cursor.description]
            split_data = dict(zip(columns, split_transaction))
            
            # Get split lines
            cursor.execute('''
                SELECT stl.*, c.name as category_name, a.name as account_name
                FROM split_transaction_lines stl
                LEFT JOIN categories c ON stl.category_id = c.id
                LEFT JOIN accounts a ON stl.account_id = a.id
                WHERE stl.split_transaction_id = ?
                ORDER BY stl.line_order
            ''', (split_transaction_id,))
            
            lines = []
            for row in cursor.fetchall():
                line_columns = [desc[0] for desc in cursor.description]
                line_data = dict(zip(line_columns, row))
                lines.append(line_data)
            
            split_data['lines'] = lines
            return split_data
            
        except sqlite3.Error as e:
            raise Exception(f"Error getting split transaction: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def get_all_split_transactions(self, account_id=None, start_date=None, end_date=None):
        """Get all split transactions with optional filters"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            query = '''
                SELECT st.*, a.name as account_name,
                       COUNT(stl.id) as line_count
                FROM split_transactions st
                LEFT JOIN accounts a ON st.account_id = a.id
                LEFT JOIN split_transaction_lines stl ON st.id = stl.split_transaction_id
                WHERE 1=1
            '''
            params = []
            
            if account_id:
                query += ' AND st.account_id = ?'
                params.append(account_id)
            
            if start_date:
                query += ' AND st.date >= ?'
                params.append(start_date)
            
            if end_date:
                query += ' AND st.date <= ?'
                params.append(end_date)
            
            query += ' GROUP BY st.id ORDER BY st.date DESC, st.created_date DESC'
            
            cursor.execute(query, params)
            
            split_transactions = []
            for row in cursor.fetchall():
                columns = [desc[0] for desc in cursor.description]
                split_data = dict(zip(columns, row))
                split_transactions.append(split_data)
            
            return split_transactions
            
        except sqlite3.Error as e:
            raise Exception(f"Error getting split transactions: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def update_split_transaction(self, split_transaction_id, date=None, description=None, 
                                total_amount=None, split_lines=None, notes=None):
        """Update an existing split transaction"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get current split transaction
            current_split = self.get_split_transaction(split_transaction_id)
            if not current_split:
                raise ValueError("Split transaction not found")
            
            # Update split transaction record
            update_fields = []
            params = []
            
            if date is not None:
                update_fields.append('date = ?')
                params.append(date)
            
            if description is not None:
                update_fields.append('description = ?')
                params.append(description)
            
            if total_amount is not None:
                update_fields.append('total_amount = ?')
                params.append(total_amount)
            
            if notes is not None:
                update_fields.append('notes = ?')
                params.append(notes)
            
            if update_fields:
                params.append(split_transaction_id)
                cursor.execute(f'''
                    UPDATE split_transactions 
                    SET {', '.join(update_fields)}
                    WHERE id = ?
                ''', params)
            
            # Update split lines if provided
            if split_lines is not None:
                # Validate total if amount was updated
                final_total = total_amount if total_amount is not None else current_split['total_amount']
                split_total = sum(line['amount'] for line in split_lines)
                if abs(split_total - final_total) > 0.01:
                    raise ValueError(f"Split lines total ({split_total:.2f}) does not match transaction total ({final_total:.2f})")
                
                # Delete existing lines
                cursor.execute('DELETE FROM split_transaction_lines WHERE split_transaction_id = ?', 
                             (split_transaction_id,))
                
                # Insert new lines
                for i, line in enumerate(split_lines):
                    percentage = (line['amount'] / final_total * 100) if final_total != 0 else 0
                    
                    cursor.execute('''
                        INSERT INTO split_transaction_lines 
                        (split_transaction_id, category_id, account_id, amount, description, percentage, line_order)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (split_transaction_id, line.get('category_id'), line.get('account_id'), 
                          line['amount'], line.get('description', ''), percentage, i))
            
            conn.commit()
            return True
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating split transaction: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def delete_split_transaction(self, split_transaction_id):
        """Delete a split transaction and its lines"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get split transaction details
            split_transaction = self.get_split_transaction(split_transaction_id)
            if not split_transaction:
                raise ValueError("Split transaction not found")
            
            # If this split has a parent transaction, restore it
            if split_transaction['parent_transaction_id']:
                cursor.execute('''
                    UPDATE transactions 
                    SET description = REPLACE(description, ' (SPLIT)', ''),
                        amount = ?
                    WHERE id = ?
                ''', (split_transaction['total_amount'], split_transaction['parent_transaction_id']))
            
            # Delete split transaction (lines will be deleted by CASCADE)
            cursor.execute('DELETE FROM split_transactions WHERE id = ?', (split_transaction_id,))
            
            conn.commit()
            return True
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting split transaction: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def get_transaction_splits(self, transaction_id):
        """Get all splits for a specific transaction"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT st.*, a.name as account_name
                FROM split_transactions st
                LEFT JOIN accounts a ON st.account_id = a.id
                WHERE st.parent_transaction_id = ?
                ORDER BY st.created_date DESC
            ''', (transaction_id,))
            
            splits = []
            for row in cursor.fetchall():
                columns = [desc[0] for desc in cursor.description]
                split_data = dict(zip(columns, row))
                
                # Get lines for this split
                split_data['lines'] = self._get_split_lines(split_data['id'])
                splits.append(split_data)
            
            return splits
            
        except sqlite3.Error as e:
            raise Exception(f"Error getting transaction splits: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def _get_split_lines(self, split_transaction_id):
        """Helper method to get split lines"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT stl.*, c.name as category_name, a.name as account_name
                FROM split_transaction_lines stl
                LEFT JOIN categories c ON stl.category_id = c.id
                LEFT JOIN accounts a ON stl.account_id = a.id
                WHERE stl.split_transaction_id = ?
                ORDER BY stl.line_order
            ''', (split_transaction_id,))
            
            lines = []
            for row in cursor.fetchall():
                columns = [desc[0] for desc in cursor.description]
                line_data = dict(zip(columns, row))
                lines.append(line_data)
            
            return lines
            
        except sqlite3.Error as e:
            raise Exception(f"Error getting split lines: {str(e)}")
        finally:
            if conn:
                conn.close()
