import csv
import os
import sqlite3
import tempfile
import unittest
from datetime import datetime, timedelta

from model.account import Account
from model.bank_reconciliation import BankReconciliationSession
from model.bank_statement_transaction import BankStatementTransaction
from model.transaction import TransactionManager
from utils.bank_reconciliation_manager import BankReconciliationManager
from utils.transaction_matcher import TransactionMatcher


class TestBankReconciliation(unittest.TestCase):
    """Test cases for bank reconciliation functionality"""
    
    def setUp(self):
        """Set up test database and sample data"""
        # Create temporary database
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
        
        # Initialize database
        self.init_test_database()
        
        # Create test components
        self.reconciliation_session = BankReconciliationSession(self.db_path)
        self.bank_statement_transaction = BankStatementTransaction(self.db_path)
        self.transaction_matcher = TransactionMatcher(self.db_path)
        self.reconciliation_manager = BankReconciliationManager(self.db_path)
        self.transaction_manager = TransactionManager(self.db_path)
        self.account_manager = Account(self.db_path)
        
        # Create test account
        self.test_account_id = self.account_manager.create_account(
            name="Test Checking Account",
            account_type="checking",
            currency="USD",
            opening_balance=1000.00,
            description="Test account for reconciliation"
        )
        
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def init_test_database(self):
        """Initialize test database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create all required tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL,
                description TEXT,
                created_date TEXT,
                classification TEXT,
                account_number TEXT,
                parent_id INTEGER,
                is_active INTEGER DEFAULT 1
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                currency_code TEXT DEFAULT 'USD',
                exchange_rate REAL DEFAULT 1.0,
                base_amount REAL,
                import_hash TEXT,
                bank_reference TEXT,
                import_source TEXT,
                import_date TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_reconciliation_sessions (
                id INTEGER PRIMARY KEY,
                account_id INTEGER NOT NULL,
                statement_date TEXT NOT NULL,
                statement_balance REAL NOT NULL,
                book_balance REAL NOT NULL,
                difference REAL NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                created_date TEXT NOT NULL,
                completed_date TEXT,
                created_by TEXT,
                notes TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_statement_transactions (
                id INTEGER PRIMARY KEY,
                reconciliation_session_id INTEGER NOT NULL,
                date TEXT NOT NULL,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                reference TEXT,
                transaction_type TEXT,
                matched_transaction_id INTEGER,
                match_confidence REAL DEFAULT 0.0,
                match_status TEXT DEFAULT 'unmatched',
                created_date TEXT NOT NULL,
                FOREIGN KEY (reconciliation_session_id) REFERENCES bank_reconciliation_sessions(id),
                FOREIGN KEY (matched_transaction_id) REFERENCES transactions(id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reconciliation_matches (
                id INTEGER PRIMARY KEY,
                reconciliation_session_id INTEGER NOT NULL,
                bank_statement_transaction_id INTEGER NOT NULL,
                book_transaction_id INTEGER NOT NULL,
                match_type TEXT NOT NULL,
                match_confidence REAL NOT NULL,
                match_date TEXT NOT NULL,
                matched_by TEXT,
                notes TEXT,
                FOREIGN KEY (reconciliation_session_id) REFERENCES bank_reconciliation_sessions(id),
                FOREIGN KEY (bank_statement_transaction_id) REFERENCES bank_statement_transactions(id),
                FOREIGN KEY (book_transaction_id) REFERENCES transactions(id)
            )
        """)
        
        # Insert test category
        cursor.execute(
            "INSERT INTO categories (name, type) VALUES (?, ?)",
            ("Test Expense", "expense")
        )
        
        conn.commit()
        conn.close()
    
    def test_create_reconciliation_session(self):
        """Test creating a new reconciliation session"""
        statement_date = "2024-01-31"
        statement_balance = 1500.00
        
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, statement_date, statement_balance, "test_user"
        )
        
        self.assertIsNotNone(session_id)
        
        # Retrieve and verify session
        session = self.reconciliation_session.get_reconciliation_session(session_id)
        self.assertIsNotNone(session)
        self.assertEqual(session['account_id'], self.test_account_id)
        self.assertEqual(session['statement_date'], statement_date)
        self.assertEqual(session['statement_balance'], statement_balance)
        self.assertEqual(session['status'], 'pending')
    
    def test_add_bank_statement_transactions(self):
        """Test adding bank statement transactions"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add bank statement transactions
        transactions = [
            {
                'date': '2024-01-15',
                'description': 'Grocery Store Purchase',
                'amount': -125.50,
                'reference': 'REF001'
            },
            {
                'date': '2024-01-16',
                'description': 'Salary Payment',
                'amount': 2500.00,
                'reference': 'REF002'
            }
        ]
        
        added_count = self.bank_statement_transaction.bulk_add_bank_statement_transactions(
            session_id, transactions
        )
        
        self.assertEqual(added_count, 2)
        
        # Verify transactions were added
        bank_transactions = self.bank_statement_transaction.get_bank_statement_transactions(session_id)
        self.assertEqual(len(bank_transactions), 2)
        self.assertEqual(bank_transactions[0]['description'], 'Grocery Store Purchase')
        self.assertEqual(bank_transactions[1]['description'], 'Salary Payment')
    
    def test_transaction_matching(self):
        """Test transaction matching functionality"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add book transaction
        book_transaction_id = self.transaction_manager.add_transaction(
            date="2024-01-15",
            amount=125.50,
            account_id=self.test_account_id,
            description="Grocery Store Purchase",
            type_name="expense"
        )
        
        # Add bank statement transaction
        bank_transaction_id = self.bank_statement_transaction.add_bank_statement_transaction(
            session_id, "2024-01-15", "Grocery Store Purchase", -125.50, "REF001"
        )
        
        # Test finding matches
        bank_transaction = self.bank_statement_transaction.get_transaction_by_id(bank_transaction_id)
        potential_matches = self.transaction_matcher.find_matches_for_bank_transaction(
            bank_transaction, session_id
        )
        
        self.assertGreater(len(potential_matches), 0)
        self.assertEqual(potential_matches[0]['transaction']['id'], book_transaction_id)
        self.assertGreater(potential_matches[0]['confidence'], 0.8)  # Should be high confidence
    
    def test_create_manual_match(self):
        """Test creating manual matches between transactions"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add book transaction
        book_transaction_id = self.transaction_manager.add_transaction(
            date="2024-01-15",
            amount=125.50,
            account_id=self.test_account_id,
            description="Grocery Store Purchase",
            type_name="expense"
        )
        
        # Add bank statement transaction
        bank_transaction_id = self.bank_statement_transaction.add_bank_statement_transaction(
            session_id, "2024-01-15", "Grocery Store Purchase", -125.50, "REF001"
        )
        
        # Create manual match
        success = self.transaction_matcher.create_match(
            session_id, bank_transaction_id, book_transaction_id, 'manual', 1.0, 'test_user'
        )
        
        self.assertTrue(success)
        
        # Verify match was created
        bank_transaction = self.bank_statement_transaction.get_transaction_by_id(bank_transaction_id)
        self.assertEqual(bank_transaction['match_status'], 'matched')
        self.assertEqual(bank_transaction['matched_transaction_id'], book_transaction_id)
        self.assertEqual(bank_transaction['match_confidence'], 1.0)
    
    def test_auto_match_transactions(self):
        """Test automatic transaction matching"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add book transactions
        book_trans_1 = self.transaction_manager.add_transaction(
            date="2024-01-15",
            amount=125.50,
            account_id=self.test_account_id,
            description="Grocery Store Purchase",
            type_name="expense"
        )
        
        book_trans_2 = self.transaction_manager.add_transaction(
            date="2024-01-16",
            amount=2500.00,
            account_id=self.test_account_id,
            description="Salary Payment",
            type_name="income"
        )
        
        # Add bank statement transactions
        bank_transactions = [
            {
                'date': '2024-01-15',
                'description': 'Grocery Store Purchase',
                'amount': -125.50,
                'reference': 'REF001'
            },
            {
                'date': '2024-01-16',
                'description': 'Salary Payment',
                'amount': 2500.00,
                'reference': 'REF002'
            }
        ]
        
        self.bank_statement_transaction.bulk_add_bank_statement_transactions(
            session_id, bank_transactions
        )
        
        # Auto-match transactions
        matches_made = self.transaction_matcher.auto_match_transactions(session_id, 0.8)
        
        self.assertEqual(matches_made, 2)
        
        # Verify matches
        matched_transactions = self.bank_statement_transaction.get_matched_transactions(session_id)
        self.assertEqual(len(matched_transactions), 2)
    
    def test_reconciliation_manager_workflow(self):
        """Test complete reconciliation workflow using manager"""
        # Start reconciliation
        session_id = self.reconciliation_manager.start_reconciliation(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        self.assertIsNotNone(session_id)
        
        # Create test CSV file
        test_csv = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv')
        csv_writer = csv.writer(test_csv)
        
        # Write test data
        csv_writer.writerow(["Date", "Description", "Amount"])
        csv_writer.writerow(["2024-01-15", "Grocery Store Purchase", "-125.50"])
        csv_writer.writerow(["2024-01-16", "Salary Payment", "2500.00"])
        
        test_csv.close()
        
        try:
            # Upload bank statement
            result = self.reconciliation_manager.upload_bank_statement(session_id, test_csv.name)
            
            self.assertEqual(result['transactions_added'], 2)
            
            # Get dashboard data
            dashboard = self.reconciliation_manager.get_reconciliation_dashboard(session_id)
            
            self.assertIsNotNone(dashboard)
            self.assertEqual(dashboard['summary']['bank_transactions']['total'], 2)
            self.assertEqual(len(dashboard['unmatched_bank_transactions']), 2)
            
            # Complete reconciliation (should fail due to unmatched transactions)
            with self.assertRaises(Exception):
                self.reconciliation_manager.complete_reconciliation(session_id)
            
            # Cancel reconciliation
            success = self.reconciliation_manager.cancel_reconciliation(session_id)
            self.assertTrue(success)
            
        finally:
            os.unlink(test_csv.name)
    
    def test_reconciliation_statistics(self):
        """Test reconciliation statistics and reporting"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add transactions and matches
        book_transaction_id = self.transaction_manager.add_transaction(
            date="2024-01-15",
            amount=125.50,
            account_id=self.test_account_id,
            description="Grocery Store Purchase",
            type_name="expense"
        )
        
        bank_transaction_id = self.bank_statement_transaction.add_bank_statement_transaction(
            session_id, "2024-01-15", "Grocery Store Purchase", -125.50, "REF001"
        )
        
        # Create match
        self.transaction_matcher.create_match(
            session_id, bank_transaction_id, book_transaction_id, 'auto', 0.95, 'system'
        )
        
        # Get statistics
        stats = self.transaction_matcher.get_match_statistics(session_id)
        
        self.assertEqual(stats['total_bank_transactions'], 1)
        self.assertEqual(stats['matched_transactions'], 1)
        self.assertEqual(stats['unmatched_transactions'], 0)
        self.assertEqual(stats['avg_confidence'], 0.95)
        self.assertEqual(len(stats['match_types']), 1)
        self.assertEqual(stats['match_types'][0]['type'], 'auto')
    
    def test_reconciliation_summary(self):
        """Test reconciliation session summary"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add bank statement transactions
        transactions = [
            {
                'date': '2024-01-15',
                'description': 'Transaction 1',
                'amount': -100.00,
                'reference': 'REF001'
            },
            {
                'date': '2024-01-16',
                'description': 'Transaction 2',
                'amount': 200.00,
                'reference': 'REF002'
            }
        ]
        
        self.bank_statement_transaction.bulk_add_bank_statement_transactions(
            session_id, transactions
        )
        
        # Get summary
        summary = self.reconciliation_session.get_reconciliation_summary(session_id)
        
        self.assertIsNotNone(summary)
        self.assertEqual(summary['session']['id'], session_id)
        self.assertEqual(summary['bank_transactions']['total'], 2)
        self.assertEqual(summary['bank_transactions']['matched'], 0)
        self.assertEqual(summary['bank_transactions']['unmatched'], 2)
    
    def test_remove_match(self):
        """Test removing matches between transactions"""
        # Create reconciliation session
        session_id = self.reconciliation_session.create_reconciliation_session(
            self.test_account_id, "2024-01-31", 1500.00, "test_user"
        )
        
        # Add book transaction
        book_transaction_id = self.transaction_manager.add_transaction(
            date="2024-01-15",
            amount=125.50,
            account_id=self.test_account_id,
            description="Test Transaction",
            type_name="expense"
        )
        
        # Add bank statement transaction
        bank_transaction_id = self.bank_statement_transaction.add_bank_statement_transaction(
            session_id, "2024-01-15", "Test Transaction", -125.50, "REF001"
        )
        
        # Create match
        self.transaction_matcher.create_match(
            session_id, bank_transaction_id, book_transaction_id, 'manual', 1.0, 'test_user'
        )
        
        # Verify match exists
        bank_transaction = self.bank_statement_transaction.get_transaction_by_id(bank_transaction_id)
        self.assertEqual(bank_transaction['match_status'], 'matched')
        
        # Remove match
        success = self.transaction_matcher.remove_match(session_id, bank_transaction_id)
        self.assertTrue(success)
        
        # Verify match was removed
        bank_transaction = self.bank_statement_transaction.get_transaction_by_id(bank_transaction_id)
        self.assertEqual(bank_transaction['match_status'], 'unmatched')
        self.assertIsNone(bank_transaction['matched_transaction_id'])


if __name__ == '__main__':
    unittest.main()
