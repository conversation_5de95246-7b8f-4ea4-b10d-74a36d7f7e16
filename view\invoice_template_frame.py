import os
import tkinter as tk
from tkinter import colorchooser, filedialog, messagebox

import ttkbootstrap as ttk
from PIL import Image, ImageTk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledFrame

from model.invoice_template import InvoiceTemplate
from utils.pdf_generator import InvoicePDFGenerator


class InvoiceTemplateFrame(ttk.Frame):
    """Frame for managing invoice templates"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Invoice Templates - {company_name}"

        # Initialize models
        self.template_model = InvoiceTemplate(db_path)

        # Initialize variables
        self.selected_template_id = None
        self.logo_path = None
        self.logo_image = None

        # Create UI
        self.create_widgets()

        # Load templates
        self.load_templates()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with title and close button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(header_frame, text="Invoice Templates", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")

        close_button = ttk.Button(header_frame, text="Close", command=self.close_callback, bootstyle=DANGER)
        close_button.pack(side="right")

        # Split view with template list on left and editor on right
        split_frame = ttk.Frame(main_frame)
        split_frame.pack(fill="both", expand=True)

        # Template list frame (left side)
        list_frame = ttk.LabelFrame(split_frame, text="Templates", padding=10, width=200)
        list_frame.pack(side="left", fill="y", padx=(0, 10))
        list_frame.pack_propagate(False)  # Prevent frame from shrinking

        # Template list with scrollbar
        self.template_listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE, exportselection=0)
        self.template_listbox.pack(side="left", fill="both", expand=True)

        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.template_listbox.yview)
        scrollbar.pack(side="right", fill="y")
        self.template_listbox.configure(yscrollcommand=scrollbar.set)

        # Bind selection event
        self.template_listbox.bind("<<ListboxSelect>>", self.on_template_selected)

        # Template action buttons
        action_frame = ttk.Frame(list_frame)
        action_frame.pack(fill="x", pady=(10, 0))

        add_button = ttk.Button(action_frame, text="Add", command=self.add_template, bootstyle=SUCCESS)
        add_button.pack(side="left", padx=(0, 5))

        delete_button = ttk.Button(action_frame, text="Delete", command=self.delete_template, bootstyle=DANGER)
        delete_button.pack(side="left")

        # Template editor frame (right side)
        editor_frame = ttk.LabelFrame(split_frame, text="Template Editor", padding=10)
        editor_frame.pack(side="left", fill="both", expand=True)

        # Create scrollable frame for editor
        editor_scroll = ScrolledFrame(editor_frame)
        editor_scroll.pack(fill="both", expand=True)

        # Template form
        form_frame = ttk.Frame(editor_scroll)
        form_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Basic Information section
        basic_frame = ttk.LabelFrame(form_frame, text="Basic Information", padding=10)
        basic_frame.pack(fill="x", pady=(0, 10))

        # Template name
        name_frame = ttk.Frame(basic_frame)
        name_frame.pack(fill="x", pady=5)

        ttk.Label(name_frame, text="Template Name:", width=15).pack(side="left")
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(name_frame, textvariable=self.name_var)
        name_entry.pack(side="left", fill="x", expand=True)

        # Template description
        desc_frame = ttk.Frame(basic_frame)
        desc_frame.pack(fill="x", pady=5)

        ttk.Label(desc_frame, text="Description:", width=15).pack(side="left")
        self.description_var = tk.StringVar()
        desc_entry = ttk.Entry(desc_frame, textvariable=self.description_var)
        desc_entry.pack(side="left", fill="x", expand=True)

        # Default template checkbox
        default_frame = ttk.Frame(basic_frame)
        default_frame.pack(fill="x", pady=5)

        self.is_default_var = tk.BooleanVar(value=False)
        default_check = ttk.Checkbutton(default_frame, text="Set as Default Template",
                                       variable=self.is_default_var)
        default_check.pack(side="left")

        # Company Information section
        company_frame = ttk.LabelFrame(form_frame, text="Company Information", padding=10)
        company_frame.pack(fill="x", pady=(0, 10))

        # Company name
        company_name_frame = ttk.Frame(company_frame)
        company_name_frame.pack(fill="x", pady=5)

        ttk.Label(company_name_frame, text="Company Name:", width=15).pack(side="left")
        self.company_name_var = tk.StringVar()
        company_name_entry = ttk.Entry(company_name_frame, textvariable=self.company_name_var)
        company_name_entry.pack(side="left", fill="x", expand=True)

        # Company address
        company_address_frame = ttk.Frame(company_frame)
        company_address_frame.pack(fill="x", pady=5)

        ttk.Label(company_address_frame, text="Address:", width=15).pack(side="left")
        self.company_address_var = tk.StringVar()
        company_address_entry = ttk.Entry(company_address_frame, textvariable=self.company_address_var)
        company_address_entry.pack(side="left", fill="x", expand=True)

        # Company phone
        company_phone_frame = ttk.Frame(company_frame)
        company_phone_frame.pack(fill="x", pady=5)

        ttk.Label(company_phone_frame, text="Phone:", width=15).pack(side="left")
        self.company_phone_var = tk.StringVar()
        company_phone_entry = ttk.Entry(company_phone_frame, textvariable=self.company_phone_var)
        company_phone_entry.pack(side="left", fill="x", expand=True)

        # Company email
        company_email_frame = ttk.Frame(company_frame)
        company_email_frame.pack(fill="x", pady=5)

        ttk.Label(company_email_frame, text="Email:", width=15).pack(side="left")
        self.company_email_var = tk.StringVar()
        company_email_entry = ttk.Entry(company_email_frame, textvariable=self.company_email_var)
        company_email_entry.pack(side="left", fill="x", expand=True)

        # Company website
        company_website_frame = ttk.Frame(company_frame)
        company_website_frame.pack(fill="x", pady=5)

        ttk.Label(company_website_frame, text="Website:", width=15).pack(side="left")
        self.company_website_var = tk.StringVar()
        company_website_entry = ttk.Entry(company_website_frame, textvariable=self.company_website_var)
        company_website_entry.pack(side="left", fill="x", expand=True)

        # Company tax ID
        company_tax_id_frame = ttk.Frame(company_frame)
        company_tax_id_frame.pack(fill="x", pady=5)

        ttk.Label(company_tax_id_frame, text="Tax ID:", width=15).pack(side="left")
        self.company_tax_id_var = tk.StringVar()
        company_tax_id_entry = ttk.Entry(company_tax_id_frame, textvariable=self.company_tax_id_var)
        company_tax_id_entry.pack(side="left", fill="x", expand=True)

        # Company logo
        logo_frame = ttk.Frame(company_frame)
        logo_frame.pack(fill="x", pady=5)

        ttk.Label(logo_frame, text="Logo:", width=15).pack(side="left")

        logo_button_frame = ttk.Frame(logo_frame)
        logo_button_frame.pack(side="left", fill="x", expand=True)

        self.logo_path_var = tk.StringVar()
        logo_path_label = ttk.Label(logo_button_frame, textvariable=self.logo_path_var,
                                   foreground="blue", cursor="hand2")
        logo_path_label.pack(side="left", fill="x", expand=True)

        browse_button = ttk.Button(logo_button_frame, text="Browse", command=self.browse_logo)
        browse_button.pack(side="left", padx=(5, 0))

        # Logo preview
        self.logo_preview_frame = ttk.Frame(company_frame)
        self.logo_preview_frame.pack(fill="x", pady=5)

        self.logo_preview_label = ttk.Label(self.logo_preview_frame)
        self.logo_preview_label.pack(side="left", padx=(15, 0))

        # Style section
        style_frame = ttk.LabelFrame(form_frame, text="Style Options", padding=10)
        style_frame.pack(fill="x", pady=(0, 10))

        # Primary color
        primary_color_frame = ttk.Frame(style_frame)
        primary_color_frame.pack(fill="x", pady=5)

        ttk.Label(primary_color_frame, text="Primary Color:", width=15).pack(side="left")
        self.primary_color_var = tk.StringVar(value="#3498db")
        primary_color_entry = ttk.Entry(primary_color_frame, textvariable=self.primary_color_var, width=10)
        primary_color_entry.pack(side="left")

        self.primary_color_preview = ttk.Label(primary_color_frame, text="   ", background="#3498db")
        self.primary_color_preview.pack(side="left", padx=5)

        primary_color_button = ttk.Button(primary_color_frame, text="Choose",
                                        command=lambda: self.choose_color("primary"))
        primary_color_button.pack(side="left")

        # Secondary color
        secondary_color_frame = ttk.Frame(style_frame)
        secondary_color_frame.pack(fill="x", pady=5)

        ttk.Label(secondary_color_frame, text="Secondary Color:", width=15).pack(side="left")
        self.secondary_color_var = tk.StringVar(value="#2c3e50")
        secondary_color_entry = ttk.Entry(secondary_color_frame, textvariable=self.secondary_color_var, width=10)
        secondary_color_entry.pack(side="left")

        self.secondary_color_preview = ttk.Label(secondary_color_frame, text="   ", background="#2c3e50")
        self.secondary_color_preview.pack(side="left", padx=5)

        secondary_color_button = ttk.Button(secondary_color_frame, text="Choose",
                                          command=lambda: self.choose_color("secondary"))
        secondary_color_button.pack(side="left")

        # Font family
        font_frame = ttk.Frame(style_frame)
        font_frame.pack(fill="x", pady=5)

        ttk.Label(font_frame, text="Font Family:", width=15).pack(side="left")
        self.font_family_var = tk.StringVar(value="Helvetica")
        font_values = ["Helvetica", "Times-Roman", "Courier"]
        font_combobox = ttk.Combobox(font_frame, textvariable=self.font_family_var,
                                    values=font_values, state="readonly")
        font_combobox.pack(side="left")

        # Additional Information section
        additional_frame = ttk.LabelFrame(form_frame, text="Additional Information", padding=10)
        additional_frame.pack(fill="x", pady=(0, 10))

        # Show payment info checkbox
        payment_check_frame = ttk.Frame(additional_frame)
        payment_check_frame.pack(fill="x", pady=5)

        self.show_payment_info_var = tk.BooleanVar(value=True)
        payment_check = ttk.Checkbutton(payment_check_frame, text="Show Payment Information",
                                       variable=self.show_payment_info_var)
        payment_check.pack(side="left")

        # Payment info
        payment_info_frame = ttk.Frame(additional_frame)
        payment_info_frame.pack(fill="x", pady=5)

        ttk.Label(payment_info_frame, text="Payment Info:", width=15).pack(side="top", anchor="nw")
        self.payment_info_var = tk.StringVar()
        payment_info_text = ttk.Text(payment_info_frame, height=3, width=50)
        payment_info_text.pack(fill="x", expand=True, pady=(5, 0))

        # Connect text widget to StringVar
        def update_payment_info_var(event=None):
            self.payment_info_var.set(payment_info_text.get("1.0", "end-1c"))

        payment_info_text.bind("<KeyRelease>", update_payment_info_var)

        # Footer text
        footer_frame = ttk.Frame(additional_frame)
        footer_frame.pack(fill="x", pady=5)

        ttk.Label(footer_frame, text="Footer Text:", width=15).pack(side="top", anchor="nw")
        self.footer_text_var = tk.StringVar()
        footer_text = ttk.Text(footer_frame, height=2, width=50)
        footer_text.pack(fill="x", expand=True, pady=(5, 0))

        # Connect text widget to StringVar
        def update_footer_text_var(event=None):
            self.footer_text_var.set(footer_text.get("1.0", "end-1c"))

        footer_text.bind("<KeyRelease>", update_footer_text_var)

        # Error message
        self.error_var = tk.StringVar()
        error_label = ttk.Label(form_frame, textvariable=self.error_var, foreground="red")
        error_label.pack(fill="x", pady=5)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.pack(fill="x", pady=10)

        save_button = ttk.Button(button_frame, text="Save Template", command=self.save_template, bootstyle=SUCCESS)
        save_button.pack(side="left", padx=(0, 10))

        preview_button = ttk.Button(button_frame, text="Preview Template", command=self.preview_template, bootstyle=INFO)
        preview_button.pack(side="left")

    def load_templates(self):
        """Load templates into the listbox"""
        # Clear existing items
        self.template_listbox.delete(0, tk.END)

        # Reset template IDs dictionary
        self.template_ids = {}

        # Get all templates
        templates = self.template_model.get_all_templates()

        # Add templates to listbox
        for template in templates:
            template_name = template['name']
            if template.get('is_default'):
                template_name += " (Default)"

            self.template_listbox.insert(tk.END, template_name)

            # Store template ID in a dictionary
            if not hasattr(self, 'template_ids'):
                self.template_ids = {}

            # Store the template ID with the current index
            current_index = self.template_listbox.size() - 1
            self.template_ids[current_index] = template['id']

        # Select the first template if available
        if self.template_listbox.size() > 0:
            self.template_listbox.selection_set(0)
            self.template_listbox.event_generate("<<ListboxSelect>>")

    def on_template_selected(self, event):
        """Handle template selection"""
        # Get selected index
        selection = self.template_listbox.curselection()
        if not selection:
            return

        # Get template ID from our dictionary
        index = selection[0]
        template_id = self.template_ids.get(index)

        # Store selected template ID
        self.selected_template_id = template_id

        # Load template data
        template = self.template_model.get_by_id(template_id)
        if not template:
            return

        # Set form values
        self.name_var.set(template.get('name', ''))
        self.description_var.set(template.get('description', ''))
        self.is_default_var.set(template.get('is_default', False))
        self.company_name_var.set(template.get('company_name', ''))
        self.company_address_var.set(template.get('company_address', ''))
        self.company_phone_var.set(template.get('company_phone', ''))
        self.company_email_var.set(template.get('company_email', ''))
        self.company_website_var.set(template.get('company_website', ''))
        self.company_tax_id_var.set(template.get('company_tax_id', ''))
        self.logo_path_var.set(template.get('logo_path', ''))
        self.primary_color_var.set(template.get('primary_color', '#3498db'))
        self.secondary_color_var.set(template.get('secondary_color', '#2c3e50'))
        self.font_family_var.set(template.get('font_family', 'Helvetica'))
        self.show_payment_info_var.set(template.get('show_payment_info', True))
        self.payment_info_var.set(template.get('payment_info', ''))
        self.footer_text_var.set(template.get('footer_text', ''))

        # Update color previews
        self.primary_color_preview.configure(background=self.primary_color_var.get())
        self.secondary_color_preview.configure(background=self.secondary_color_var.get())

        # Update logo preview
        self.logo_path = template.get('logo_path', '')
        self.update_logo_preview()

    def add_template(self):
        """Add a new template"""
        # Clear form
        self.selected_template_id = None
        self.name_var.set('')
        self.description_var.set('')
        self.is_default_var.set(False)
        self.company_name_var.set('')
        self.company_address_var.set('')
        self.company_phone_var.set('')
        self.company_email_var.set('')
        self.company_website_var.set('')
        self.company_tax_id_var.set('')
        self.logo_path_var.set('')
        self.logo_path = None
        self.primary_color_var.set('#3498db')
        self.secondary_color_var.set('#2c3e50')
        self.font_family_var.set('Helvetica')
        self.show_payment_info_var.set(True)
        self.payment_info_var.set('')
        self.footer_text_var.set('')

        # Update color previews
        self.primary_color_preview.configure(background=self.primary_color_var.get())
        self.secondary_color_preview.configure(background=self.secondary_color_var.get())

        # Clear logo preview
        self.logo_preview_label.configure(image='')

        # Clear selection in listbox
        self.template_listbox.selection_clear(0, tk.END)

    def delete_template(self):
        """Delete the selected template"""
        if not self.selected_template_id:
            messagebox.showwarning("No Selection", "Please select a template to delete.")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Deletion",
                                  "Are you sure you want to delete this template?\n\n"
                                  "This action cannot be undone."):
            return

        # Delete template
        try:
            self.template_model.delete_template(self.selected_template_id)
            messagebox.showinfo("Success", "Template deleted successfully.")

            # Reload templates
            self.load_templates()

            # Clear form
            self.add_template()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete template: {str(e)}")

    def save_template(self):
        """Save the template"""
        # Validate required fields
        if not self.name_var.get():
            self.error_var.set("Template name is required")
            return

        if not self.company_name_var.get():
            self.error_var.set("Company name is required")
            return

        # Prepare template data
        template_data = {
            'name': self.name_var.get(),
            'description': self.description_var.get(),
            'company_name': self.company_name_var.get(),
            'company_address': self.company_address_var.get(),
            'company_phone': self.company_phone_var.get(),
            'company_email': self.company_email_var.get(),
            'company_website': self.company_website_var.get(),
            'company_tax_id': self.company_tax_id_var.get(),
            'logo_path': self.logo_path,
            'primary_color': self.primary_color_var.get(),
            'secondary_color': self.secondary_color_var.get(),
            'font_family': self.font_family_var.get(),
            'show_payment_info': self.show_payment_info_var.get(),
            'payment_info': self.payment_info_var.get(),
            'footer_text': self.footer_text_var.get(),
            'is_default': self.is_default_var.get()
        }

        try:
            if self.selected_template_id:
                # Update existing template
                self.template_model.update_template(self.selected_template_id, template_data)
                messagebox.showinfo("Success", "Template updated successfully.")
            else:
                # Add new template
                self.template_model.add_template(template_data)
                messagebox.showinfo("Success", "Template added successfully.")

            # Clear error message
            self.error_var.set("")

            # Reload templates
            self.load_templates()
        except Exception as e:
            self.error_var.set(f"Error: {str(e)}")

    def browse_logo(self):
        """Browse for a logo file"""
        file_path = filedialog.askopenfilename(
            title="Select Logo Image",
            filetypes=[("Image Files", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )

        if file_path:
            self.logo_path = file_path
            self.logo_path_var.set(file_path)
            self.update_logo_preview()

    def update_logo_preview(self):
        """Update the logo preview"""
        if self.logo_path and os.path.exists(self.logo_path):
            try:
                # Load and resize the image
                image = Image.open(self.logo_path)
                image = image.resize((100, 50), Image.LANCZOS)

                # Convert to PhotoImage
                self.logo_image = ImageTk.PhotoImage(image)

                # Update the preview label
                self.logo_preview_label.configure(image=self.logo_image)
            except Exception as e:
                print(f"Error loading logo: {e}")
                self.logo_preview_label.configure(image='')
        else:
            self.logo_preview_label.configure(image='')

    def choose_color(self, color_type):
        """Open color chooser dialog

        Args:
            color_type (str): Type of color to choose ('primary' or 'secondary')
        """
        # Get current color
        if color_type == 'primary':
            current_color = self.primary_color_var.get()
        else:
            current_color = self.secondary_color_var.get()

        # Open color chooser
        color = colorchooser.askcolor(color=current_color, title=f"Choose {color_type.capitalize()} Color")

        # Update color if selected
        if color[1]:
            if color_type == 'primary':
                self.primary_color_var.set(color[1])
                self.primary_color_preview.configure(background=color[1])
            else:
                self.secondary_color_var.set(color[1])
                self.secondary_color_preview.configure(background=color[1])

    def preview_template(self):
        """Preview the template with sample data"""
        if not self.company_name_var.get():
            self.error_var.set("Company name is required for preview")
            return

        # Create a sample invoice for preview
        sample_invoice = {
            'invoice_number': 'INV-1001',
            'client_name': 'Sample Client',
            'client_company': 'Sample Company',
            'issue_date': '2025-05-10',
            'due_date': '2025-06-09',
            'status': 'draft',
            'subtotal': 1000.00,
            'tax_amount': 100.00,
            'total_amount': 1100.00,
            'notes': 'This is a sample invoice for template preview.',
            'terms': 'Sample terms and conditions.',
            'client': {
                'name': 'Sample Client',
                'company_name': 'Sample Company',
                'email': '<EMAIL>',
                'phone': '(*************',
                'address': '456 Client St',
                'city': 'Clientville',
                'state': 'CS',
                'zip_code': '12345',
                'country': 'United States',
                'tax_id': 'CLIENT-TAX-ID'
            },
            'items': [
                {
                    'description': 'Sample Product 1',
                    'quantity': 2,
                    'unit_price': 300.00,
                    'tax_rate': 10.0,
                    'amount': 660.00
                },
                {
                    'description': 'Sample Service 1',
                    'quantity': 4,
                    'unit_price': 100.00,
                    'tax_rate': 0.0,
                    'amount': 400.00
                }
            ]
        }

        # Prepare template data
        template_data = {
            'name': self.name_var.get(),
            'description': self.description_var.get(),
            'company_name': self.company_name_var.get(),
            'company_address': self.company_address_var.get(),
            'company_phone': self.company_phone_var.get(),
            'company_email': self.company_email_var.get(),
            'company_website': self.company_website_var.get(),
            'company_tax_id': self.company_tax_id_var.get(),
            'logo_path': self.logo_path,
            'primary_color': self.primary_color_var.get(),
            'secondary_color': self.secondary_color_var.get(),
            'font_family': self.font_family_var.get(),
            'show_payment_info': self.show_payment_info_var.get(),
            'payment_info': self.payment_info_var.get(),
            'footer_text': self.footer_text_var.get()
        }

        try:
            # Generate PDF
            pdf_generator = InvoicePDFGenerator(template_data, sample_invoice)
            pdf_path = pdf_generator.generate_pdf()

            # Open the PDF
            import os
            import platform
            import subprocess

            if platform.system() == 'Windows':
                os.startfile(pdf_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', pdf_path])
            else:  # Linux
                subprocess.call(['xdg-open', pdf_path])
        except Exception as e:
            self.error_var.set(f"Error generating preview: {str(e)}")
