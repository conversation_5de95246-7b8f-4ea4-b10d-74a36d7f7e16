import datetime
import os
import shutil
import sqlite3
import sys
import tempfile
import unittest

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model.financial_reports import (CashFlowReport, CategoryBreakdownReport,
                                     IncomeStatementReport,
                                     MonthlyComparisonReport,
                                     TaxRelatedTransactionsReport)
from model.report_framework import ReportManager


class TestFinancialReports(unittest.TestCase):
    """Test cases for financial reports"""

    def setUp(self):
        """Set up test database with sample data"""
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, "test_reports.db")

        # Create test database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create tables
        cursor.execute("""
        CREATE TABLE accounts (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            currency TEXT NOT NULL,
            opening_balance REAL NOT NULL,
            current_balance REAL NOT NULL
        )
        """)

        cursor.execute("""
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT
        )
        """)

        cursor.execute("""
        CREATE TABLE transactions (
            id INTEGER PRIMARY KEY,
            date TEXT NOT NULL,
            amount REAL NOT NULL,
            description TEXT,
            category_id INTEGER,
            account_id INTEGER NOT NULL,
            type TEXT NOT NULL,
            reconciled INTEGER DEFAULT 0,
            notes TEXT,
            FOREIGN KEY (category_id) REFERENCES categories (id),
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
        """)

        # Insert test data
        accounts = [
            (1, "Checking", "checking", "USD", 1000.00, 1500.00),
            (2, "Savings", "savings", "USD", 5000.00, 5500.00),
            (3, "Credit Card", "credit", "USD", 0.00, -500.00)
        ]

        for account in accounts:
            cursor.execute(
                "INSERT INTO accounts (id, name, type, currency, opening_balance, current_balance) VALUES (?, ?, ?, ?, ?, ?)",
                account
            )

        categories = [
            (1, "Salary", "income"),
            (2, "Interest", "income"),
            (3, "Groceries", "expense"),
            (4, "Rent", "expense"),
            (5, "Utilities", "expense"),
            (6, "Tax Refund", "income"),
            (7, "Medical Expenses", "expense"),
            (8, "Education", "expense")
        ]

        for category in categories:
            cursor.execute(
                "INSERT INTO categories (id, name, type) VALUES (?, ?, ?)",
                category
            )

        # Create transactions spanning multiple months
        current_year = datetime.datetime.now().year

        # January transactions
        jan_transactions = [
            (f"{current_year}-01-05", 2000.00, "Monthly Salary", 1, 1, "income", 1),
            (f"{current_year}-01-10", 50.00, "Interest Income", 2, 2, "income", 1),
            (f"{current_year}-01-15", 200.00, "Grocery Shopping", 3, 1, "expense", 1),
            (f"{current_year}-01-20", 1000.00, "Rent Payment", 4, 1, "expense", 1),
            (f"{current_year}-01-25", 150.00, "Electricity Bill", 5, 1, "expense", 1)
        ]

        # February transactions
        feb_transactions = [
            (f"{current_year}-02-05", 2000.00, "Monthly Salary", 1, 1, "income", 1),
            (f"{current_year}-02-10", 55.00, "Interest Income", 2, 2, "income", 1),
            (f"{current_year}-02-15", 220.00, "Grocery Shopping", 3, 1, "expense", 1),
            (f"{current_year}-02-20", 1000.00, "Rent Payment", 4, 1, "expense", 1),
            (f"{current_year}-02-25", 160.00, "Electricity Bill", 5, 1, "expense", 1),
            (f"{current_year}-02-28", 500.00, "Medical Expenses", 7, 1, "expense", 1)
        ]

        # March transactions
        mar_transactions = [
            (f"{current_year}-03-05", 2200.00, "Monthly Salary", 1, 1, "income", 1),
            (f"{current_year}-03-10", 60.00, "Interest Income", 2, 2, "income", 1),
            (f"{current_year}-03-15", 210.00, "Grocery Shopping", 3, 1, "expense", 1),
            (f"{current_year}-03-20", 1000.00, "Rent Payment", 4, 1, "expense", 1),
            (f"{current_year}-03-25", 155.00, "Electricity Bill", 5, 1, "expense", 1),
            (f"{current_year}-03-28", 1000.00, "Tax Refund", 6, 1, "income", 1),
            (f"{current_year}-03-30", 300.00, "Education Expenses", 8, 1, "expense", 1)
        ]

        transactions = jan_transactions + feb_transactions + mar_transactions

        for transaction in transactions:
            cursor.execute(
                """INSERT INTO transactions
                   (date, amount, description, category_id, account_id, type, reconciled)
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                transaction
            )

        conn.commit()
        conn.close()

        # Create report manager
        self.report_manager = ReportManager(self.db_path)
        self.report_manager.register_report(IncomeStatementReport, "income_statement")
        self.report_manager.register_report(CashFlowReport, "cash_flow")
        self.report_manager.register_report(MonthlyComparisonReport, "monthly_comparison")
        self.report_manager.register_report(CategoryBreakdownReport, "category_breakdown")
        self.report_manager.register_report(TaxRelatedTransactionsReport, "tax_related")

    def tearDown(self):
        """Clean up test files"""
        shutil.rmtree(self.test_dir)

    def test_income_statement_report(self):
        """Test income statement report generation"""
        # Create report
        report = self.report_manager.get_report("income_statement")

        # Set parameters
        current_year = datetime.datetime.now().year
        report.set_parameter("date_from", f"{current_year}-01-01")
        report.set_parameter("date_to", f"{current_year}-03-31")

        # Generate report
        results = report.generate()

        # Verify results
        self.assertIsNotNone(results)
        self.assertIn("income_categories", results)
        self.assertIn("expense_categories", results)
        self.assertIn("total_income", results)
        self.assertIn("total_expenses", results)
        self.assertIn("net_income", results)

        # Verify totals
        self.assertEqual(results["total_income"], 7365.0)  # Sum of all income transactions
        self.assertEqual(results["total_expenses"], 4895.0)  # Sum of all expense transactions
        self.assertEqual(results["net_income"], 2470.0)  # Income - Expenses

    def test_cash_flow_report(self):
        """Test cash flow report generation"""
        # Create report
        report = self.report_manager.get_report("cash_flow")

        # Set parameters
        current_year = datetime.datetime.now().year
        report.set_parameter("date_from", f"{current_year}-01-01")
        report.set_parameter("date_to", f"{current_year}-03-31")

        # Generate report
        results = report.generate()

        # Verify results
        self.assertIsNotNone(results)
        self.assertIn("monthly_flows", results)
        self.assertIn("total_inflow", results)
        self.assertIn("total_outflow", results)
        self.assertIn("total_net_flow", results)

        # Verify monthly data
        self.assertEqual(len(results["monthly_flows"]), 3)  # 3 months of data

        # Verify totals
        self.assertEqual(results["total_inflow"], 7365.0)  # Sum of all income
        self.assertEqual(results["total_outflow"], 4895.0)  # Sum of all expenses
        self.assertEqual(results["total_net_flow"], 2470.0)  # Inflow - Outflow

    def test_monthly_comparison_report(self):
        """Test monthly comparison report generation"""
        # Create report
        report = self.report_manager.get_report("monthly_comparison")

        # Set parameters
        current_year = datetime.datetime.now().year
        report.set_parameter("date_from", f"{current_year}-01-01")
        report.set_parameter("date_to", f"{current_year}-03-31")

        # Generate report
        results = report.generate()

        # Verify results
        self.assertIsNotNone(results)
        self.assertIn("monthly_data", results)
        self.assertIn("total_income", results)
        self.assertIn("total_expense", results)
        self.assertIn("total_net", results)
        self.assertIn("avg_income", results)
        self.assertIn("avg_expense", results)
        self.assertIn("avg_net", results)

        # Verify monthly data
        self.assertEqual(len(results["monthly_data"]), 3)  # 3 months of data

        # Verify totals
        self.assertEqual(results["total_income"], 7365.0)
        self.assertEqual(results["total_expense"], 4895.0)
        self.assertEqual(results["total_net"], 2470.0)

        # Verify averages
        self.assertAlmostEqual(results["avg_income"], 7365.0 / 3)
        self.assertAlmostEqual(results["avg_expense"], 4895.0 / 3)
        self.assertAlmostEqual(results["avg_net"], 2470.0 / 3)

    def test_category_breakdown_report(self):
        """Test category breakdown report generation"""
        # Create report
        report = self.report_manager.get_report("category_breakdown")

        # Set parameters
        current_year = datetime.datetime.now().year
        report.set_parameter("date_from", f"{current_year}-01-01")
        report.set_parameter("date_to", f"{current_year}-03-31")

        # Generate report
        results = report.generate()

        # Verify results
        self.assertIsNotNone(results)
        self.assertIn("income_categories", results)
        self.assertIn("expense_categories", results)
        self.assertIn("total_income", results)
        self.assertIn("total_expense", results)
        self.assertIn("net_income", results)

        # Verify category counts
        self.assertEqual(len(results["income_categories"]), 3)  # Salary, Interest, Tax Refund
        self.assertEqual(len(results["expense_categories"]), 5)  # Groceries, Rent, Utilities, Medical, Education

        # Verify totals
        self.assertEqual(results["total_income"], 7365.0)
        self.assertEqual(results["total_expense"], 4895.0)
        self.assertEqual(results["net_income"], 2470.0)

    def test_tax_related_transactions_report(self):
        """Test tax-related transactions report generation"""
        # Create report
        report = self.report_manager.get_report("tax_related")

        # Set parameters
        current_year = datetime.datetime.now().year
        report.set_parameter("tax_year", current_year)

        # Generate report
        results = report.generate()

        # Verify results
        self.assertIsNotNone(results)
        self.assertIn("tax_year", results)
        self.assertIn("income_transactions", results)
        self.assertIn("expense_transactions", results)
        self.assertIn("total_income", results)
        self.assertIn("total_expenses", results)
        self.assertIn("net_income", results)

        # Verify tax year
        self.assertEqual(results["tax_year"], current_year)

        # Verify transactions (should include tax refund, medical expenses, education)
        tax_related_count = len(results["income_transactions"]) + len(results["expense_transactions"])
        self.assertGreater(tax_related_count, 0)

        # At least tax refund should be included in income
        self.assertGreaterEqual(len(results["income_transactions"]), 1)

        # At least medical and education expenses should be included
        self.assertGreaterEqual(len(results["expense_transactions"]), 2)


if __name__ == "__main__":
    unittest.main()
