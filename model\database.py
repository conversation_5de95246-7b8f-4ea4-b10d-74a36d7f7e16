import hashlib
import os
import sqlite3
import time


class Database:
    def __init__(self):
        self.users_db = "users.db"
        self.init_users_db()

    def init_users_db(self):
        """Initialize the users database if it doesn't exist"""
        try:
            conn = sqlite3.connect(self.users_db, timeout=30)
            cursor = conn.cursor()

            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")

            # Set busy timeout to 10 seconds
            cursor.execute("PRAGMA busy_timeout = 10000")

            # Create users table if it doesn't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL
            )
            ''')

            # Add default admin if no users exist
            cursor.execute("SELECT COUNT(*) FROM users")
            if cursor.fetchone()[0] == 0:
                # Create default admin with password 'admin'
                hashed_password = hashlib.sha256("admin".encode()).hexdigest()
                cursor.execute(
                    "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                    ("admin", hashed_password, "Admin")
                )

            conn.commit()
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def init_company_db(self, company_name):
        """Create a new company database with all required tables"""
        db_path = f"{company_name.lower().replace(' ', '_')}.db"

        # Check if database already exists
        if os.path.exists(db_path):
            return db_path

        try:
            conn = sqlite3.connect(db_path, timeout=30)
            cursor = conn.cursor()

            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")

            # Set busy timeout to 10 seconds
            cursor.execute("PRAGMA busy_timeout = 10000")

            # Create accounts table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL,
                description TEXT,
                created_date TEXT NOT NULL
            )
            ''')

            # Create categories table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
            ''')

            # Create transactions table with foreign keys
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                category_id INTEGER,
                account_id INTEGER NOT NULL,
                reconciled INTEGER DEFAULT 0,
                type TEXT NOT NULL,
                import_hash TEXT,
                bank_reference TEXT,
                import_source TEXT,
                import_date TEXT,
                FOREIGN KEY (category_id) REFERENCES categories(id),
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
            ''')

            # Create bank format templates table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_format_templates (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                bank_name TEXT NOT NULL,
                file_type TEXT NOT NULL,
                field_mapping TEXT NOT NULL,
                date_format TEXT,
                delimiter TEXT,
                has_header INTEGER DEFAULT 1,
                skip_lines INTEGER DEFAULT 0,
                created_date TEXT NOT NULL,
                is_active INTEGER DEFAULT 1
            )
            ''')

            # Create bank statement imports table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_statement_imports (
                id INTEGER PRIMARY KEY,
                filename TEXT NOT NULL,
                account_id INTEGER NOT NULL,
                import_date TEXT NOT NULL,
                total_transactions INTEGER DEFAULT 0,
                successful_imports INTEGER DEFAULT 0,
                duplicates_found INTEGER DEFAULT 0,
                errors_count INTEGER DEFAULT 0,
                template_id INTEGER,
                status TEXT DEFAULT 'completed',
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (template_id) REFERENCES bank_format_templates(id)
            )
            ''')

            # Create bank reconciliation sessions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_reconciliation_sessions (
                id INTEGER PRIMARY KEY,
                account_id INTEGER NOT NULL,
                statement_date TEXT NOT NULL,
                statement_balance REAL NOT NULL,
                book_balance REAL NOT NULL,
                difference REAL NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                created_date TEXT NOT NULL,
                completed_date TEXT,
                created_by TEXT,
                notes TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
            ''')

            # Create bank statement transactions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_statement_transactions (
                id INTEGER PRIMARY KEY,
                reconciliation_session_id INTEGER NOT NULL,
                date TEXT NOT NULL,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                reference TEXT,
                transaction_type TEXT,
                matched_transaction_id INTEGER,
                match_confidence REAL DEFAULT 0.0,
                match_status TEXT DEFAULT 'unmatched',
                created_date TEXT NOT NULL,
                FOREIGN KEY (reconciliation_session_id) REFERENCES bank_reconciliation_sessions(id),
                FOREIGN KEY (matched_transaction_id) REFERENCES transactions(id)
            )
            ''')

            # Create reconciliation matches table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS reconciliation_matches (
                id INTEGER PRIMARY KEY,
                reconciliation_session_id INTEGER NOT NULL,
                bank_statement_transaction_id INTEGER NOT NULL,
                book_transaction_id INTEGER NOT NULL,
                match_type TEXT NOT NULL,
                match_confidence REAL NOT NULL,
                match_date TEXT NOT NULL,
                matched_by TEXT,
                notes TEXT,
                FOREIGN KEY (reconciliation_session_id) REFERENCES bank_reconciliation_sessions(id),
                FOREIGN KEY (bank_statement_transaction_id) REFERENCES bank_statement_transactions(id),
                FOREIGN KEY (book_transaction_id) REFERENCES transactions(id)
            )
            ''')

            # Create bills table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS bills (
                id INTEGER PRIMARY KEY,
                payee TEXT NOT NULL,
                amount REAL NOT NULL,
                due_date TEXT NOT NULL,
                status TEXT NOT NULL,
                category_id INTEGER,
                account_id INTEGER,
                recurring INTEGER DEFAULT 0,
                recurrence_pattern TEXT,
                description TEXT,
                FOREIGN KEY (category_id) REFERENCES categories(id),
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
            ''')

            # Create clients table for invoices
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                company_name TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                city TEXT,
                state TEXT,
                zip_code TEXT,
                country TEXT,
                tax_id TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create invoices table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT NOT NULL UNIQUE,
                client_id INTEGER NOT NULL,
                issue_date TEXT NOT NULL,
                due_date TEXT NOT NULL,
                status TEXT NOT NULL,
                subtotal REAL NOT NULL,
                tax_amount REAL NOT NULL,
                total_amount REAL NOT NULL,
                notes TEXT,
                terms TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
            ''')

            # Create invoice_items table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                description TEXT NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                tax_rate REAL NOT NULL,
                amount REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
            )
            ''')

            # Create invoice_settings table for storing next invoice number
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Initialize next_invoice_number if it doesn't exist
            cursor.execute('''
            INSERT OR IGNORE INTO invoice_settings (setting_name, setting_value)
            VALUES ('next_invoice_number', '1001')
            ''')

            # Create invoice_templates table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                company_name TEXT NOT NULL,
                company_address TEXT,
                company_phone TEXT,
                company_email TEXT,
                company_website TEXT,
                company_tax_id TEXT,
                logo_path TEXT,
                primary_color TEXT DEFAULT '#3498db',
                secondary_color TEXT DEFAULT '#2c3e50',
                font_family TEXT DEFAULT 'Helvetica',
                show_payment_info BOOLEAN DEFAULT 1,
                payment_info TEXT,
                footer_text TEXT,
                is_default BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create default template if none exists
            cursor.execute('''
            SELECT COUNT(*) FROM invoice_templates
            ''')
            if cursor.fetchone()[0] == 0:
                cursor.execute('''
                INSERT INTO invoice_templates (
                    name, description, company_name, company_address,
                    company_phone, company_email, company_website,
                    company_tax_id, primary_color, secondary_color,
                    font_family, show_payment_info, payment_info,
                    footer_text, is_default, created_at, updated_at
                ) VALUES (
                    'Default Template', 'Default invoice template',
                    'Your Company Name', '123 Business St, City, State, ZIP',
                    '(*************', '<EMAIL>', 'www.yourcompany.com',
                    'TAX-ID-12345', '#3498db', '#2c3e50',
                    'Helvetica', 1, 'Please make payment within 30 days.',
                    'Thank you for your business!', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                ''')

            # Create metadata table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS metadata (
                key TEXT PRIMARY KEY,
                value TEXT
            )
            ''')

            # Add creation timestamp
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            cursor.execute(
                "INSERT INTO metadata (key, value) VALUES (?, ?)",
                ("created_date", current_time)
            )

            # Add company name
            cursor.execute(
                "INSERT INTO metadata (key, value) VALUES (?, ?)",
                ("company_name", company_name)
            )

            # Create default categories
            default_income_categories = ["Salary", "Interest", "Dividends", "Sales", "Other Income"]
            default_expense_categories = ["Food", "Rent", "Transportation", "Utilities", "Entertainment"]

            for category in default_income_categories:
                cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)",
                              (category, "income"))

            for category in default_expense_categories:
                cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)",
                              (category, "expense"))

            conn.commit()
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

        return db_path

    def authenticate_user(self, username, password):
        """Authenticate a user and return role if successful"""
        hashed_password = hashlib.sha256(password.encode()).hexdigest()

        try:
            conn = sqlite3.connect(self.users_db, timeout=30)
            cursor = conn.cursor()

            # Set busy timeout
            cursor.execute("PRAGMA busy_timeout = 10000")

            cursor.execute(
                "SELECT role FROM users WHERE username = ? AND password = ?",
                (username, hashed_password)
            )

            result = cursor.fetchone()

            if result:
                return result[0]  # Return role
            return None

        except sqlite3.Error as e:
            print(f"Authentication error: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def get_all_users(self):
        """Get all users from the database"""
        try:
            conn = sqlite3.connect(self.users_db, timeout=30)
            conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            cursor = conn.cursor()

            # Set busy timeout
            cursor.execute("PRAGMA busy_timeout = 10000")

            cursor.execute("SELECT id, username, role FROM users ORDER BY username")

            users = [dict(row) for row in cursor.fetchall()]
            return users

        except sqlite3.Error as e:
            print(f"Error getting users: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def add_user(self, username, password, role):
        """Add a new user to the database"""
        if not username or not password or not role:
            raise ValueError("Username, password, and role are required")

        if role not in ["Admin", "Client"]:
            raise ValueError("Role must be 'Admin' or 'Client'")

        hashed_password = hashlib.sha256(password.encode()).hexdigest()

        try:
            conn = sqlite3.connect(self.users_db, timeout=30)
            cursor = conn.cursor()

            # Set busy timeout
            cursor.execute("PRAGMA busy_timeout = 10000")

            # Check if username already exists
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
            if cursor.fetchone()[0] > 0:
                raise ValueError(f"Username '{username}' already exists")

            # Insert new user
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                (username, hashed_password, role)
            )

            conn.commit()
            return cursor.lastrowid

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Error adding user: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def update_user(self, user_id, username, password=None, role=None):
        """Update a user in the database"""
        try:
            conn = sqlite3.connect(self.users_db, timeout=30)
            cursor = conn.cursor()

            # Set busy timeout
            cursor.execute("PRAGMA busy_timeout = 10000")

            # Check if user exists
            cursor.execute("SELECT username, role FROM users WHERE id = ?", (user_id,))
            result = cursor.fetchone()
            if not result:
                raise ValueError(f"User with ID {user_id} not found")

            current_username, current_role = result

            # Check if new username already exists (if changing username)
            if username != current_username:
                cursor.execute("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?", (username, user_id))
                if cursor.fetchone()[0] > 0:
                    raise ValueError(f"Username '{username}' already exists")

            # Check if this would remove the last admin
            if role != current_role and current_role == "Admin" and role == "Client":
                cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'Admin' AND id != ?", (user_id,))
                if cursor.fetchone()[0] == 0:
                    raise ValueError("Cannot change the last admin user to a client")

            # Build update query based on provided parameters
            update_fields = []
            params = []

            if username:
                update_fields.append("username = ?")
                params.append(username)

            if password:
                hashed_password = hashlib.sha256(password.encode()).hexdigest()
                update_fields.append("password = ?")
                params.append(hashed_password)

            if role:
                update_fields.append("role = ?")
                params.append(role)

            if not update_fields:
                return False  # Nothing to update

            # Add user_id to params
            params.append(user_id)

            # Update the user
            cursor.execute(
                f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?",
                params
            )

            conn.commit()
            return cursor.rowcount > 0

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Error updating user: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def get_company_db_path(self, company_name):
        """Get the database path for a company"""
        # Convert company name to database file name
        db_path = f"{company_name.lower().replace(' ', '_')}.db"

        # Check if database exists
        if not os.path.exists(db_path):
            raise ValueError(f"Company database for '{company_name}' not found")

        return db_path

    def delete_user(self, user_id):
        """Delete a user from the database"""
        try:
            conn = sqlite3.connect(self.users_db, timeout=30)
            cursor = conn.cursor()

            # Set busy timeout
            cursor.execute("PRAGMA busy_timeout = 10000")

            # Check if user exists
            cursor.execute("SELECT username FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            if not user:
                raise ValueError(f"User with ID {user_id} not found")

            # Prevent deleting the last admin user
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'Admin'")
            admin_count = cursor.fetchone()[0]

            cursor.execute("SELECT role FROM users WHERE id = ?", (user_id,))
            user_role = cursor.fetchone()[0]

            if admin_count <= 1 and user_role == "Admin":
                raise ValueError("Cannot delete the last admin user")

            # Delete the user
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

            conn.commit()
            return True

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Error deleting user: {e}")
            raise
        finally:
            if conn:
                conn.close()


def initialize_database():
    connection = sqlite3.connect("cashbook.db")
    cursor = connection.cursor()

    # Create accounts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            currency TEXT NOT NULL,
            opening_balance REAL NOT NULL,
            current_balance REAL NOT NULL
        )
    ''')

    # Create transactions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            amount REAL NOT NULL,
            description TEXT,
            category_id INTEGER,
            account_id INTEGER,
            reconciled BOOLEAN NOT NULL DEFAULT 0,
            FOREIGN KEY (category_id) REFERENCES categories (id),
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
    ''')

    # Create categories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL
        )
    ''')

    # Create bills table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS bills (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            payee TEXT NOT NULL,
            amount REAL NOT NULL,
            due_date TEXT NOT NULL,
            status TEXT NOT NULL
        )
    ''')

    connection.commit()
    connection.close()

if __name__ == "__main__":
    initialize_database()
