import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.chart_of_accounts import ChartOfAccounts
from model.account import Account


class ChartOfAccountsFrame(ttk.Frame):
    """Frame for managing the chart of accounts"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Chart of Accounts - {company_name}"

        # Create models
        self.chart_model = ChartOfAccounts(self.db_path)
        self.account_model = Account(self.db_path)

        self.create_widgets()
        self.load_chart_of_accounts()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Toolbar
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.pack(fill="x", pady=(0, 10))

        # Initialize Standard Chart button
        init_button = ttk.Button(
            toolbar_frame,
            text="Initialize Standard Chart",
            command=self.initialize_standard_chart,
            bootstyle=PRIMARY
        )
        init_button.pack(side="left", padx=(0, 5))

        # Validate Equation button
        validate_button = ttk.Button(
            toolbar_frame,
            text="Validate Accounting Equation",
            command=self.validate_accounting_equation,
            bootstyle=INFO
        )
        validate_button.pack(side="left", padx=5)

        # Export button
        export_button = ttk.Button(
            toolbar_frame,
            text="Export Chart",
            command=self.export_chart,
            bootstyle=SUCCESS
        )
        export_button.pack(side="left", padx=5)

        # Chart of Accounts Treeview
        tree_frame = ttk.LabelFrame(main_frame, text="Chart of Accounts", padding=10)
        tree_frame.pack(fill="both", expand=True)

        # Create treeview with hierarchical structure
        columns = ("number", "name", "classification", "type", "balance", "status")
        self.chart_tree = ttk.Treeview(tree_frame, columns=columns, show="tree headings", height=15)

        # Define headings
        self.chart_tree.heading("#0", text="Account Hierarchy")
        self.chart_tree.heading("number", text="Number")
        self.chart_tree.heading("name", text="Account Name")
        self.chart_tree.heading("classification", text="Classification")
        self.chart_tree.heading("type", text="Type")
        self.chart_tree.heading("balance", text="Balance")
        self.chart_tree.heading("status", text="Status")

        # Define columns
        self.chart_tree.column("#0", width=200)
        self.chart_tree.column("number", width=80, stretch=False)
        self.chart_tree.column("name", width=150)
        self.chart_tree.column("classification", width=100)
        self.chart_tree.column("type", width=120)
        self.chart_tree.column("balance", width=100, anchor="e")
        self.chart_tree.column("status", width=80, anchor="center")

        # Add scrollbars
        scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical", command=self.chart_tree.yview)
        scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.chart_tree.xview)
        self.chart_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Pack treeview and scrollbars
        self.chart_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

    def load_chart_of_accounts(self):
        """Load the chart of accounts into the treeview"""
        # Clear existing items
        for item in self.chart_tree.get_children():
            self.chart_tree.delete(item)

        try:
            # Get chart of accounts
            chart_accounts = self.chart_model.get_chart_of_accounts()
            
            # Get account balances from the accounts table
            all_accounts = self.account_model.get_all_accounts()
            balance_dict = {acc['account_number']: acc['current_balance'] for acc in all_accounts if acc.get('account_number')}

            # Create a dictionary to track parent items
            parent_items = {}

            # Group accounts by classification for better organization
            classifications = {}
            for account in chart_accounts:
                classification = account['classification']
                if classification not in classifications:
                    classifications[classification] = []
                classifications[classification].append(account)

            # Insert accounts by classification
            for classification in ['Asset', 'Liability', 'Equity', 'Revenue', 'Expense']:
                if classification in classifications:
                    # Create classification header
                    class_item = self.chart_tree.insert(
                        "", "end",
                        text=f"{classification.upper()}S",
                        values=("", "", classification, "Header", "", ""),
                        open=True
                    )
                    parent_items[f"{classification}_header"] = class_item

                    # Add accounts under this classification
                    for account in classifications[classification]:
                        account_number = account['account_number']
                        balance = balance_dict.get(account_number, 0.0)
                        status = "Active" if account['is_active'] else "Inactive"

                        # Determine parent based on level
                        parent = class_item
                        if account['parent_id']:
                            # Find parent item
                            for item_id, item_data in parent_items.items():
                                if item_id.startswith(str(account['parent_id'])):
                                    parent = item_data
                                    break

                        item = self.chart_tree.insert(
                            parent, "end",
                            text=account['account_name'],
                            values=(
                                account_number,
                                account['account_name'],
                                account['classification'],
                                account['account_type'],
                                f"${balance:.2f}",
                                status
                            )
                        )
                        parent_items[f"{account['id']}_{account_number}"] = item

            self.status_var.set(f"Loaded {len(chart_accounts)} accounts")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load chart of accounts: {str(e)}")
            self.status_var.set("Error loading chart")

    def initialize_standard_chart(self):
        """Initialize the standard chart of accounts"""
        if messagebox.askyesno("Confirm", "This will create a standard chart of accounts. Continue?"):
            try:
                self.chart_model.create_standard_chart_of_accounts()
                self.load_chart_of_accounts()
                messagebox.showinfo("Success", "Standard chart of accounts created successfully!")
                self.status_var.set("Standard chart of accounts initialized")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to initialize chart: {str(e)}")

    def validate_accounting_equation(self):
        """Validate the accounting equation"""
        try:
            validation = self.chart_model.validate_accounting_equation()
            
            message = f"Accounting Equation Validation:\n\n"
            message += f"Assets: ${validation['assets']:.2f}\n"
            message += f"Liabilities: ${validation['liabilities']:.2f}\n"
            message += f"Equity: ${validation['equity']:.2f}\n\n"
            message += f"Assets = Liabilities + Equity\n"
            message += f"${validation['assets']:.2f} = ${validation['liabilities'] + validation['equity']:.2f}\n\n"
            
            if validation['is_balanced']:
                message += "✓ The accounting equation is BALANCED!"
                messagebox.showinfo("Validation Result", message)
            else:
                message += f"✗ The accounting equation is OUT OF BALANCE!\n"
                message += f"Difference: ${validation['difference']:.2f}"
                messagebox.showwarning("Validation Result", message)
                
            self.status_var.set(f"Equation {'balanced' if validation['is_balanced'] else 'unbalanced'}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to validate equation: {str(e)}")

    def export_chart(self):
        """Export the chart of accounts"""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="Export Chart of Accounts",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                # Export logic would go here
                # For now, just show a success message
                messagebox.showinfo("Success", f"Chart of accounts exported to {file_path}")
                self.status_var.set("Chart exported successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export chart: {str(e)}")
