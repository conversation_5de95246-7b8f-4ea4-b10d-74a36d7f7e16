import os
import unittest
from unittest.mock import patch, MagicMock

import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.email_service import EmailService
from model.invoice import Invoice
from model.client import Client


class TestEmailService(unittest.TestCase):
    """Test cases for the EmailService utility"""

    def setUp(self):
        """Set up test environment"""
        # Create a test database
        self.db_path = "tests/test_email_service.db"
        
        # Remove the test database if it exists
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
            
        # Create the email service
        self.email_service = EmailService(self.db_path)
        
        # Create test data
        self.setup_test_data()

    def setup_test_data(self):
        """Set up test data for email service tests"""
        # Create a client
        self.client_model = Client(self.db_path)
        self.client_id = self.client_model.add_client(
            name="Test Client",
            company_name="Test Company",
            email="<EMAIL>",
            phone="************",
            address="123 Test St",
            notes="Test notes"
        )
        
        # Create an invoice
        self.invoice_model = Invoice(self.db_path)
        self.invoice_id = self.invoice_model.create_invoice(
            client_id=self.client_id,
            invoice_number="INV-001",
            issue_date="2025-05-15",
            due_date="2025-06-15",
            notes="Test invoice"
        )
        
        # Add some invoice items
        self.invoice_model.add_invoice_item(
            invoice_id=self.invoice_id,
            description="Test item 1",
            quantity=2,
            unit_price=10.0
        )
        
        # Save email settings
        self.email_service.save_email_settings({
            'smtp_server': 'smtp.example.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'password123',
            'from_email': '<EMAIL>',
            'from_name': 'Test Sender',
            'signature': 'Test Signature'
        })

    def tearDown(self):
        """Clean up after tests"""
        # Close database connections
        self.email_service = None
        self.client_model = None
        self.invoice_model = None
        
        # Remove the test database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def test_create_tables(self):
        """Test creating the necessary tables"""
        # Connect to the database
        conn = self.email_service._get_connection()
        cursor = conn.cursor()
        
        # Check if the tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='email_settings'")
        self.assertIsNotNone(cursor.fetchone())
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='email_templates'")
        self.assertIsNotNone(cursor.fetchone())
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='email_history'")
        self.assertIsNotNone(cursor.fetchone())
        
        # Close the connection
        conn.close()

    def test_create_default_templates(self):
        """Test creating default email templates"""
        # Get all templates
        templates = self.email_service.get_email_templates()
        
        # Verify there are templates
        self.assertGreater(len(templates), 0)
        
        # Verify the template names
        template_names = [template["name"] for template in templates]
        self.assertIn("Invoice Sent", template_names)
        self.assertIn("Payment Reminder", template_names)
        self.assertIn("Payment Received", template_names)

    def test_get_email_settings(self):
        """Test retrieving email settings"""
        # Get the settings
        settings = self.email_service.get_email_settings()
        
        # Verify the settings
        self.assertEqual(settings["smtp_server"], "smtp.example.com")
        self.assertEqual(settings["smtp_port"], 587)
        self.assertEqual(settings["username"], "<EMAIL>")
        self.assertEqual(settings["password"], "password123")
        self.assertEqual(settings["from_email"], "<EMAIL>")
        self.assertEqual(settings["from_name"], "Test Sender")
        self.assertEqual(settings["signature"], "Test Signature")

    def test_save_email_settings(self):
        """Test saving email settings"""
        # Update the settings
        result = self.email_service.save_email_settings({
            'smtp_server': 'smtp.updated.com',
            'smtp_port': 465,
            'username': '<EMAIL>',
            'password': 'newpassword',
            'from_email': '<EMAIL>',
            'from_name': 'Updated Sender',
            'signature': 'Updated Signature'
        })
        
        # Verify the operation was successful
        self.assertTrue(result)
        
        # Get the updated settings
        settings = self.email_service.get_email_settings()
        
        # Verify the settings were updated
        self.assertEqual(settings["smtp_server"], "smtp.updated.com")
        self.assertEqual(settings["smtp_port"], 465)
        self.assertEqual(settings["username"], "<EMAIL>")
        self.assertEqual(settings["password"], "newpassword")
        self.assertEqual(settings["from_email"], "<EMAIL>")
        self.assertEqual(settings["from_name"], "Updated Sender")
        self.assertEqual(settings["signature"], "Updated Signature")

    def test_get_email_templates(self):
        """Test retrieving email templates"""
        # Get all templates
        templates = self.email_service.get_email_templates()
        
        # Verify there are templates
        self.assertGreater(len(templates), 0)
        
        # Verify the template data
        for template in templates:
            self.assertIn("id", template)
            self.assertIn("name", template)
            self.assertIn("subject", template)
            self.assertIn("body", template)
            self.assertIn("is_default", template)

    def test_get_template_by_name(self):
        """Test retrieving a template by name"""
        # Get a template by name
        template = self.email_service.get_template_by_name("Invoice Sent")
        
        # Verify the template exists
        self.assertIsNotNone(template)
        self.assertEqual(template["name"], "Invoice Sent")
        
        # Try to get a non-existent template
        template = self.email_service.get_template_by_name("Non-existent Template")
        
        # Verify the template doesn't exist
        self.assertIsNone(template)

    @patch('smtplib.SMTP')
    def test_send_invoice_email(self, mock_smtp):
        """Test sending an invoice email"""
        # Set up the mock
        mock_smtp_instance = MagicMock()
        mock_smtp.return_value = mock_smtp_instance
        
        # Call the method
        result, message = self.email_service.send_invoice_email(
            invoice_id=self.invoice_id,
            template_name="Invoice Sent",
            pdf_path=None
        )
        
        # Verify the operation was successful
        self.assertTrue(result)
        self.assertEqual(message, "Email sent successfully")
        
        # Verify the SMTP methods were called
        mock_smtp.assert_called_once_with("smtp.example.com", 587)
        mock_smtp_instance.starttls.assert_called_once()
        mock_smtp_instance.login.assert_called_once_with("<EMAIL>", "password123")
        mock_smtp_instance.send_message.assert_called_once()
        mock_smtp_instance.quit.assert_called_once()
        
        # Verify the email was logged
        history = self.email_service.get_email_history(self.invoice_id)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["invoice_id"], self.invoice_id)
        self.assertEqual(history[0]["recipient"], "<EMAIL>")
        self.assertEqual(history[0]["status"], "Sent")

    def test_render_template(self):
        """Test rendering a template with context"""
        # Create a template and context
        template = "Hello, {name}! Your invoice #{invoice_number} for {amount} is due on {due_date}."
        context = {
            "name": "Test Client",
            "invoice_number": "INV-001",
            "amount": "$100.00",
            "due_date": "2025-06-15"
        }
        
        # Render the template
        result = self.email_service._render_template(template, context)
        
        # Verify the result
        expected = "Hello, Test Client! Your invoice #INV-001 for $100.00 is due on 2025-06-15."
        self.assertEqual(result, expected)

    def test_log_email(self):
        """Test logging an email"""
        # Log an email
        self.email_service._log_email(
            invoice_id=self.invoice_id,
            recipient="<EMAIL>",
            subject="Test Subject",
            body="Test Body",
            status="Sent"
        )
        
        # Get the email history
        history = self.email_service.get_email_history(self.invoice_id)
        
        # Verify the email was logged
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["invoice_id"], self.invoice_id)
        self.assertEqual(history[0]["recipient"], "<EMAIL>")
        self.assertEqual(history[0]["subject"], "Test Subject")
        self.assertEqual(history[0]["body"], "Test Body")
        self.assertEqual(history[0]["status"], "Sent")
        self.assertIsNone(history[0]["error_message"])
        
        # Log another email with an error
        self.email_service._log_email(
            invoice_id=self.invoice_id,
            recipient="<EMAIL>",
            subject="Error Subject",
            body="Error Body",
            status="Failed",
            error_message="Test error message"
        )
        
        # Get the updated email history
        history = self.email_service.get_email_history(self.invoice_id)
        
        # Verify both emails are logged
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["status"], "Failed")
        self.assertEqual(history[0]["error_message"], "Test error message")

    def test_get_email_history(self):
        """Test retrieving email history"""
        # Log some emails
        self.email_service._log_email(
            invoice_id=self.invoice_id,
            recipient="<EMAIL>",
            subject="Test Subject 1",
            body="Test Body 1",
            status="Sent"
        )
        
        self.email_service._log_email(
            invoice_id=self.invoice_id,
            recipient="<EMAIL>",
            subject="Test Subject 2",
            body="Test Body 2",
            status="Sent"
        )
        
        # Create another invoice
        invoice_id2 = self.invoice_model.create_invoice(
            client_id=self.client_id,
            invoice_number="INV-002",
            issue_date="2025-05-16",
            due_date="2025-06-16",
            notes="Another test invoice"
        )
        
        # Log an email for the second invoice
        self.email_service._log_email(
            invoice_id=invoice_id2,
            recipient="<EMAIL>",
            subject="Test Subject 3",
            body="Test Body 3",
            status="Sent"
        )
        
        # Get history for the first invoice
        history1 = self.email_service.get_email_history(self.invoice_id)
        
        # Verify the history
        self.assertEqual(len(history1), 2)
        
        # Get history for the second invoice
        history2 = self.email_service.get_email_history(invoice_id2)
        
        # Verify the history
        self.assertEqual(len(history2), 1)
        
        # Get all history
        all_history = self.email_service.get_email_history()
        
        # Verify all history
        self.assertEqual(len(all_history), 3)


if __name__ == '__main__':
    unittest.main()
