["tests/test_bank_import.py::TestBankImport::test_bank_template_creation", "tests/test_bank_import.py::TestBankImport::test_csv_import_with_template", "tests/test_bank_import.py::TestBankImport::test_default_templates_initialization", "tests/test_bank_import.py::TestBankImport::test_duplicate_detection", "tests/test_bank_import.py::TestBankImport::test_qif_import", "tests/test_bank_import.py::TestBankImport::test_transaction_hash_generation", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_add_bank_statement_transactions", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_auto_match_transactions", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_create_manual_match", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_create_reconciliation_session", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_reconciliation_manager_workflow", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_reconciliation_statistics", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_reconciliation_summary", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_remove_match", "tests/test_bank_reconciliation.py::TestBankReconciliation::test_transaction_matching", "tests/test_import.py::TestRulesEngine::test_add_rule", "tests/test_import.py::TestRulesEngine::test_apply_rules_case_sensitivity", "tests/test_import.py::TestRulesEngine::test_apply_rules_priority", "tests/test_import.py::TestRulesEngine::test_apply_rules_regex_match", "tests/test_import.py::TestRulesEngine::test_apply_rules_simple_match", "tests/test_import.py::TestRulesEngine::test_delete_rule", "tests/test_import.py::TestRulesEngine::test_get_all_rules", "tests/test_import.py::TestRulesEngine::test_test_rule", "tests/test_import.py::TestRulesEngine::test_update_rule", "tests/test_import.py::TestTransactionImport::test_delete_duplicate_transaction", "tests/test_import.py::TestTransactionImport::test_get_accounts", "tests/test_import.py::TestTransactionImport::test_get_category_by_name", "tests/test_import.py::TestTransactionImport::test_is_duplicate_transaction"]