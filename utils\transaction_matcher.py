import re
import sqlite3
from datetime import datetime, timedelta
from difflib import SequenceMatcher


class TransactionMatcher:
    """
    Advanced transaction matching engine for bank reconciliation
    Uses multiple algorithms to match bank statement transactions with book transactions
    """
    
    def __init__(self, db_path):
        self.db_path = db_path
        
        # Matching configuration
        self.exact_match_threshold = 1.0
        self.high_confidence_threshold = 0.9
        self.medium_confidence_threshold = 0.7
        self.low_confidence_threshold = 0.5
        
        # Date tolerance for matching (days)
        self.date_tolerance_days = 3
        
        # Amount tolerance (percentage)
        self.amount_tolerance_percent = 0.01

    def find_matches_for_bank_transaction(self, bank_transaction, reconciliation_session_id):
        """
        Find potential matches for a bank statement transaction
        Returns a list of potential matches with confidence scores
        """
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get session details
            cursor.execute('''
                SELECT account_id, statement_date 
                FROM bank_reconciliation_sessions 
                WHERE id = ?
            ''', (reconciliation_session_id,))
            
            session = cursor.fetchone()
            if not session:
                return []
            
            account_id, statement_date = session
            
            # Get candidate book transactions
            candidates = self._get_candidate_transactions(
                cursor, account_id, bank_transaction, statement_date
            )
            
            # Score each candidate
            matches = []
            for candidate in candidates:
                confidence = self._calculate_match_confidence(bank_transaction, candidate)
                if confidence >= self.low_confidence_threshold:
                    matches.append({
                        'transaction': candidate,
                        'confidence': confidence,
                        'match_type': self._get_match_type(confidence)
                    })
            
            # Sort by confidence (highest first)
            matches.sort(key=lambda x: x['confidence'], reverse=True)
            
            return matches
            
        except sqlite3.Error as e:
            raise Exception(f"Error finding matches: {str(e)}")
        finally:
            if conn:
                conn.close()

    def _get_candidate_transactions(self, cursor, account_id, bank_transaction, statement_date):
        """Get candidate book transactions for matching"""
        
        # Calculate date range
        bank_date = datetime.strptime(bank_transaction['date'], '%Y-%m-%d')
        start_date = (bank_date - timedelta(days=self.date_tolerance_days)).strftime('%Y-%m-%d')
        end_date = (bank_date + timedelta(days=self.date_tolerance_days)).strftime('%Y-%m-%d')
        
        # Calculate amount range
        bank_amount = abs(float(bank_transaction['amount']))
        amount_tolerance = bank_amount * self.amount_tolerance_percent
        min_amount = bank_amount - amount_tolerance
        max_amount = bank_amount + amount_tolerance
        
        # Query for candidate transactions
        cursor.execute('''
            SELECT t.id, t.date, t.amount, t.description, t.category_id, t.account_id,
                   t.reconciled, t.type, c.name as category_name
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.account_id = ?
            AND t.date BETWEEN ? AND ?
            AND ABS(t.amount) BETWEEN ? AND ?
            AND t.reconciled = 0
            AND t.id NOT IN (
                SELECT COALESCE(matched_transaction_id, 0)
                FROM bank_statement_transactions
                WHERE reconciliation_session_id IN (
                    SELECT id FROM bank_reconciliation_sessions
                    WHERE account_id = ? AND status != 'cancelled'
                ) AND matched_transaction_id IS NOT NULL
            )
            ORDER BY t.date, ABS(ABS(t.amount) - ?)
        ''', (account_id, start_date, end_date, min_amount, max_amount,
              account_id, bank_amount))

        candidates = []
        for row in cursor.fetchall():
            candidate = {
                'id': row[0],
                'date': row[1],
                'amount': row[2],
                'description': row[3] if row[3] is not None else '',
                'category_id': row[4],
                'account_id': row[5],
                'reconciled': row[6],
                'type': row[7],
                'category_name': row[8] if row[8] is not None else None
            }
            candidates.append(candidate)
        
        return candidates

    def _calculate_match_confidence(self, bank_transaction, book_transaction):
        """Calculate confidence score for a potential match"""
        
        # Initialize confidence components
        date_score = 0.0
        amount_score = 0.0
        description_score = 0.0
        
        # Date matching (30% weight)
        date_score = self._calculate_date_score(
            bank_transaction['date'], book_transaction['date']
        )
        
        # Amount matching (40% weight)
        amount_score = self._calculate_amount_score(
            bank_transaction['amount'], book_transaction['amount']
        )
        
        # Description matching (30% weight)
        description_score = self._calculate_description_score(
            bank_transaction.get('description', ''), 
            book_transaction.get('description', '')
        )
        
        # Calculate weighted confidence
        confidence = (date_score * 0.3) + (amount_score * 0.4) + (description_score * 0.3)
        
        return min(confidence, 1.0)  # Cap at 1.0

    def _calculate_date_score(self, bank_date, book_date):
        """Calculate date matching score"""
        try:
            bank_dt = datetime.strptime(bank_date, '%Y-%m-%d')
            book_dt = datetime.strptime(book_date, '%Y-%m-%d')
            
            date_diff = abs((bank_dt - book_dt).days)
            
            if date_diff == 0:
                return 1.0
            elif date_diff <= 1:
                return 0.9
            elif date_diff <= 2:
                return 0.7
            elif date_diff <= 3:
                return 0.5
            else:
                return 0.0
                
        except ValueError:
            return 0.0

    def _calculate_amount_score(self, bank_amount, book_amount):
        """Calculate amount matching score"""
        try:
            bank_amt = abs(float(bank_amount))
            book_amt = abs(float(book_amount))
            
            if bank_amt == 0 and book_amt == 0:
                return 1.0
            
            if bank_amt == 0 or book_amt == 0:
                return 0.0
            
            # Calculate percentage difference
            diff_percent = abs(bank_amt - book_amt) / max(bank_amt, book_amt)
            
            if diff_percent == 0:
                return 1.0
            elif diff_percent <= 0.01:  # 1% tolerance
                return 0.95
            elif diff_percent <= 0.05:  # 5% tolerance
                return 0.8
            elif diff_percent <= 0.1:   # 10% tolerance
                return 0.6
            else:
                return 0.0
                
        except (ValueError, ZeroDivisionError):
            return 0.0

    def _calculate_description_score(self, bank_description, book_description):
        """Calculate description matching score using fuzzy matching"""
        if not bank_description and not book_description:
            return 1.0
        
        if not bank_description or not book_description:
            return 0.0
        
        # Normalize descriptions
        bank_desc = self._normalize_description(bank_description)
        book_desc = self._normalize_description(book_description)
        
        # Calculate similarity using SequenceMatcher
        similarity = SequenceMatcher(None, bank_desc, book_desc).ratio()
        
        # Check for exact keyword matches
        keyword_bonus = self._calculate_keyword_bonus(bank_desc, book_desc)
        
        # Combine similarity and keyword bonus
        final_score = min(similarity + keyword_bonus, 1.0)
        
        return final_score

    def _normalize_description(self, description):
        """Normalize description for better matching"""
        if not description:
            return ""
        
        # Convert to lowercase
        normalized = description.lower()
        
        # Remove common banking terms
        banking_terms = [
            'eftpos', 'atm', 'withdrawal', 'deposit', 'transfer', 'payment',
            'purchase', 'transaction', 'fee', 'charge', 'direct', 'credit',
            'debit', 'online', 'pos', 'card'
        ]
        
        for term in banking_terms:
            normalized = re.sub(r'\b' + term + r'\b', '', normalized)
        
        # Remove extra whitespace and special characters
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized

    def _calculate_keyword_bonus(self, desc1, desc2):
        """Calculate bonus score for matching keywords"""
        if not desc1 or not desc2:
            return 0.0
        
        words1 = set(desc1.split())
        words2 = set(desc2.split())
        
        # Remove very short words
        words1 = {w for w in words1 if len(w) > 2}
        words2 = {w for w in words2 if len(w) > 2}
        
        if not words1 or not words2:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        if union == 0:
            return 0.0
        
        jaccard_similarity = intersection / union
        
        # Return bonus (max 0.2)
        return min(jaccard_similarity * 0.2, 0.2)

    def _get_match_type(self, confidence):
        """Determine match type based on confidence score"""
        if confidence >= self.exact_match_threshold:
            return 'exact'
        elif confidence >= self.high_confidence_threshold:
            return 'high_confidence'
        elif confidence >= self.medium_confidence_threshold:
            return 'medium_confidence'
        else:
            return 'low_confidence'

    def auto_match_transactions(self, reconciliation_session_id, min_confidence=None):
        """
        Automatically match transactions above a certain confidence threshold
        """
        if min_confidence is None:
            min_confidence = self.high_confidence_threshold
        
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get unmatched bank statement transactions
            cursor.execute('''
                SELECT * FROM bank_statement_transactions 
                WHERE reconciliation_session_id = ? AND match_status = 'unmatched'
                ORDER BY date, amount
            ''', (reconciliation_session_id,))
            
            unmatched_transactions = []
            for row in cursor.fetchall():
                transaction = {
                    'id': row[0],
                    'reconciliation_session_id': row[1],
                    'date': row[2],
                    'description': row[3],
                    'amount': row[4],
                    'reference': row[5],
                    'transaction_type': row[6]
                }
                unmatched_transactions.append(transaction)
            
            matches_made = 0
            
            for bank_transaction in unmatched_transactions:
                # Find potential matches
                potential_matches = self.find_matches_for_bank_transaction(
                    bank_transaction, reconciliation_session_id
                )
                
                # Auto-match if we have a high-confidence match
                if potential_matches and potential_matches[0]['confidence'] >= min_confidence:
                    best_match = potential_matches[0]
                    
                    # Create the match
                    success = self.create_match(
                        reconciliation_session_id,
                        bank_transaction['id'],
                        best_match['transaction']['id'],
                        best_match['match_type'],
                        best_match['confidence'],
                        'auto'
                    )
                    
                    if success:
                        matches_made += 1
            
            conn.close()
            return matches_made
            
        except sqlite3.Error as e:
            raise Exception(f"Error auto-matching transactions: {str(e)}")

    def create_match(self, reconciliation_session_id, bank_transaction_id, 
                    book_transaction_id, match_type, confidence, matched_by='manual'):
        """Create a match between bank statement and book transactions"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Insert match record
            cursor.execute('''
                INSERT INTO reconciliation_matches 
                (reconciliation_session_id, bank_statement_transaction_id, book_transaction_id,
                 match_type, match_confidence, match_date, matched_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (reconciliation_session_id, bank_transaction_id, book_transaction_id,
                  match_type, confidence, current_date, matched_by))
            
            # Update bank statement transaction
            cursor.execute('''
                UPDATE bank_statement_transactions 
                SET matched_transaction_id = ?, match_confidence = ?, match_status = 'matched'
                WHERE id = ?
            ''', (book_transaction_id, confidence, bank_transaction_id))
            
            # Mark book transaction as reconciled
            cursor.execute('''
                UPDATE transactions 
                SET reconciled = 1 
                WHERE id = ?
            ''', (book_transaction_id,))
            
            conn.commit()
            return True
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error creating match: {str(e)}")
        finally:
            if conn:
                conn.close()

    def remove_match(self, reconciliation_session_id, bank_transaction_id):
        """Remove a match between transactions"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get the matched book transaction ID
            cursor.execute('''
                SELECT matched_transaction_id 
                FROM bank_statement_transactions 
                WHERE id = ?
            ''', (bank_transaction_id,))
            
            result = cursor.fetchone()
            if not result or not result[0]:
                return False
            
            book_transaction_id = result[0]
            
            # Remove match record
            cursor.execute('''
                DELETE FROM reconciliation_matches 
                WHERE reconciliation_session_id = ? 
                AND bank_statement_transaction_id = ?
            ''', (reconciliation_session_id, bank_transaction_id))
            
            # Update bank statement transaction
            cursor.execute('''
                UPDATE bank_statement_transactions 
                SET matched_transaction_id = NULL, match_confidence = 0.0, match_status = 'unmatched'
                WHERE id = ?
            ''', (bank_transaction_id,))
            
            # Mark book transaction as unreconciled
            cursor.execute('''
                UPDATE transactions 
                SET reconciled = 0 
                WHERE id = ?
            ''', (book_transaction_id,))
            
            conn.commit()
            return True
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error removing match: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_match_statistics(self, reconciliation_session_id):
        """Get matching statistics for a reconciliation session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get overall statistics
            cursor.execute('''
                SELECT
                    COUNT(*) as total_bank_transactions,
                    COUNT(CASE WHEN match_status = 'matched' THEN 1 END) as matched_transactions,
                    COUNT(CASE WHEN match_status = 'unmatched' THEN 1 END) as unmatched_transactions,
                    AVG(CASE WHEN match_status = 'matched' THEN match_confidence END) as avg_confidence
                FROM bank_statement_transactions
                WHERE reconciliation_session_id = ?
            ''', (reconciliation_session_id,))

            stats = cursor.fetchone()

            # Get match type breakdown
            cursor.execute('''
                SELECT
                    match_type,
                    COUNT(*) as count,
                    AVG(match_confidence) as avg_confidence
                FROM reconciliation_matches
                WHERE reconciliation_session_id = ?
                GROUP BY match_type
                ORDER BY avg_confidence DESC
            ''', (reconciliation_session_id,))

            match_types = []
            for row in cursor.fetchall():
                match_types.append({
                    'type': row[0],
                    'count': row[1],
                    'avg_confidence': row[2]
                })

            return {
                'total_bank_transactions': stats[0] if stats else 0,
                'matched_transactions': stats[1] if stats else 0,
                'unmatched_transactions': stats[2] if stats else 0,
                'avg_confidence': stats[3] if stats else 0.0,
                'match_types': match_types
            }

        except sqlite3.Error as e:
            raise Exception(f"Error getting match statistics: {str(e)}")
        finally:
            if conn:
                conn.close()
