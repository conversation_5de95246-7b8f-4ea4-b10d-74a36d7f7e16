import tkinter as tk
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import Scrolled<PERSON>rame

from utils.email_service import EmailService


class EmailTemplatesFrame(ttk.Frame):
    """Frame for managing email templates"""

    def __init__(self, parent, db_path, close_callback):
        """Initialize the email templates frame"""
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.close_callback = close_callback
        self.email_service = EmailService(db_path)

        # Create variables
        self.name_var = tk.StringVar()
        self.subject_var = tk.StringVar()
        self.body_var = tk.StringVar()
        self.is_default_var = tk.BooleanVar()
        self.error_var = tk.StringVar()
        self.selected_template_id = None

        # Create widgets
        self.create_widgets()

        # Load templates
        self.load_templates()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Email Templates", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20), anchor="w")

        # Error message
        error_label = ttk.Label(main_frame, textvariable=self.error_var, foreground="red")
        error_label.pack(fill="x", pady=(0, 10))

        # Split view: templates list on left, editor on right
        split_frame = ttk.Frame(main_frame)
        split_frame.pack(fill="both", expand=True)

        # Templates list (left side)
        list_frame = ttk.LabelFrame(split_frame, text="Templates")
        list_frame.pack(side="left", fill="y", padx=(0, 10), pady=10, expand=False)

        # Template listbox
        self.template_listbox = tk.Listbox(list_frame, width=30, height=15)
        self.template_listbox.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        self.template_listbox.bind("<<ListboxSelect>>", self.on_template_selected)

        # Listbox scrollbar
        listbox_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.template_listbox.yview)
        listbox_scrollbar.pack(side="right", fill="y", pady=10)
        self.template_listbox.config(yscrollcommand=listbox_scrollbar.set)

        # Buttons under listbox
        list_buttons_frame = ttk.Frame(list_frame)
        list_buttons_frame.pack(fill="x", padx=10, pady=(0, 10))

        add_button = ttk.Button(list_buttons_frame, text="New Template", 
                              command=self.add_template, bootstyle=SUCCESS)
        add_button.pack(side="left", padx=(0, 5))

        delete_button = ttk.Button(list_buttons_frame, text="Delete", 
                                 command=self.delete_template, bootstyle=DANGER)
        delete_button.pack(side="left")

        # Template editor (right side)
        editor_frame = ttk.LabelFrame(split_frame, text="Template Editor")
        editor_frame.pack(side="right", fill="both", expand=True, pady=10)

        # Create scrolled frame for editor
        editor_scrolled = ScrolledFrame(editor_frame)
        editor_scrolled.pack(fill="both", expand=True, padx=10, pady=10)

        # Template name
        name_frame = ttk.Frame(editor_scrolled)
        name_frame.pack(fill="x", pady=5)
        
        name_label = ttk.Label(name_frame, text="Template Name:", width=15, anchor="w")
        name_label.pack(side="left")
        
        name_entry = ttk.Entry(name_frame, textvariable=self.name_var)
        name_entry.pack(side="left", fill="x", expand=True)

        # Is default checkbox
        default_frame = ttk.Frame(editor_scrolled)
        default_frame.pack(fill="x", pady=5)
        
        default_check = ttk.Checkbutton(default_frame, text="Set as default template", 
                                      variable=self.is_default_var)
        default_check.pack(side="left")

        # Subject
        subject_frame = ttk.Frame(editor_scrolled)
        subject_frame.pack(fill="x", pady=5)
        
        subject_label = ttk.Label(subject_frame, text="Subject:", width=15, anchor="w")
        subject_label.pack(side="left")
        
        subject_entry = ttk.Entry(subject_frame, textvariable=self.subject_var)
        subject_entry.pack(side="left", fill="x", expand=True)

        # Available variables
        variables_frame = ttk.LabelFrame(editor_scrolled, text="Available Variables")
        variables_frame.pack(fill="x", pady=10)
        
        variables_text = "You can use these variables in your template:\n" + \
                        "{invoice_number}, {company_name}, {client_name}, " + \
                        "{total_amount}, {due_date}, {signature}"
        
        variables_label = ttk.Label(variables_frame, text=variables_text, justify="left", wraplength=400)
        variables_label.pack(fill="x", padx=10, pady=10)

        # Email body
        body_frame = ttk.LabelFrame(editor_scrolled, text="Email Body")
        body_frame.pack(fill="x", pady=10)

        self.body_text = tk.Text(body_frame, height=15, width=50)
        self.body_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Connect text widget to StringVar
        def update_body_var(event=None):
            self.body_var.set(self.body_text.get("1.0", "end-1c"))
        
        self.body_text.bind("<KeyRelease>", update_body_var)

        # Buttons
        button_frame = ttk.Frame(editor_scrolled)
        button_frame.pack(fill="x", pady=10)

        save_button = ttk.Button(button_frame, text="Save Template", 
                               command=self.save_template, bootstyle=SUCCESS)
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel", 
                                 command=self.close_callback, bootstyle=SECONDARY)
        cancel_button.pack(side="left")

    def load_templates(self):
        """Load templates into the listbox"""
        # Clear existing items
        self.template_listbox.delete(0, tk.END)
        
        # Reset template IDs dictionary
        self.template_ids = {}
        
        # Get all templates
        templates = self.email_service.get_email_templates()
        
        # Add templates to listbox
        for template in templates:
            template_name = template['name']
            if template.get('is_default'):
                template_name += " (Default)"
            
            self.template_listbox.insert(tk.END, template_name)
            
            # Store the template ID with the current index
            current_index = self.template_listbox.size() - 1
            self.template_ids[current_index] = template['id']
        
        # Select the first template if available
        if self.template_listbox.size() > 0:
            self.template_listbox.selection_set(0)
            self.template_listbox.event_generate("<<ListboxSelect>>")

    def on_template_selected(self, event=None):
        """Handle template selection"""
        # Get selected index
        selection = self.template_listbox.curselection()
        if not selection:
            return
        
        # Get template ID from our dictionary
        index = selection[0]
        template_id = self.template_ids.get(index)
        
        # Get all templates
        templates = self.email_service.get_email_templates()
        
        # Find the selected template
        template = None
        for t in templates:
            if t['id'] == template_id:
                template = t
                break
        
        if not template:
            return
        
        # Store selected template ID
        self.selected_template_id = template_id
        
        # Set form values
        self.name_var.set(template.get('name', ''))
        self.subject_var.set(template.get('subject', ''))
        self.is_default_var.set(template.get('is_default', 0) == 1)
        
        # Set body text
        self.body_text.delete("1.0", tk.END)
        self.body_text.insert("1.0", template.get('body', ''))
        self.body_var.set(template.get('body', ''))

    def add_template(self):
        """Add a new template"""
        # Clear form
        self.selected_template_id = None
        self.name_var.set('')
        self.subject_var.set('')
        self.body_var.set('')
        self.is_default_var.set(False)
        
        # Clear body text
        self.body_text.delete("1.0", tk.END)
        
        # Clear selection in listbox
        self.template_listbox.selection_clear(0, tk.END)

    def delete_template(self):
        """Delete the selected template"""
        if not self.selected_template_id:
            messagebox.showwarning("No Selection", "Please select a template to delete.")
            return
        
        # Confirm deletion
        if not messagebox.askyesno("Confirm Deletion", 
                                  "Are you sure you want to delete this template?\n\n"
                                  "This action cannot be undone."):
            return
        
        # Get all templates
        templates = self.email_service.get_email_templates()
        
        # Check if this is the only template
        if len(templates) <= 1:
            messagebox.showerror("Error", "Cannot delete the only template.")
            return
        
        # Check if this is the default template
        is_default = False
        for t in templates:
            if t['id'] == self.selected_template_id and t.get('is_default', 0) == 1:
                is_default = True
                break
        
        if is_default:
            messagebox.showerror("Error", "Cannot delete the default template. "
                               "Please set another template as default first.")
            return
        
        # Delete template from database
        conn = self.email_service._get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("DELETE FROM email_templates WHERE id = ?", (self.selected_template_id,))
            conn.commit()
            messagebox.showinfo("Success", "Template deleted successfully.")
            
            # Reload templates
            self.load_templates()
            
            # Clear form
            self.add_template()
        except Exception as e:
            conn.rollback()
            self.error_var.set(f"Error deleting template: {str(e)}")
        finally:
            conn.close()

    def save_template(self):
        """Save the template"""
        # Validate required fields
        if not self.name_var.get():
            self.error_var.set("Template name is required")
            return
        
        if not self.subject_var.get():
            self.error_var.set("Subject is required")
            return
        
        if not self.body_var.get():
            self.error_var.set("Email body is required")
            return
        
        # Prepare template data
        template_data = {
            'name': self.name_var.get(),
            'subject': self.subject_var.get(),
            'body': self.body_var.get(),
            'is_default': 1 if self.is_default_var.get() else 0
        }
        
        # Get database connection
        conn = self.email_service._get_connection()
        cursor = conn.cursor()
        
        try:
            # If this is being set as default, unset any existing default
            if template_data['is_default']:
                cursor.execute("UPDATE email_templates SET is_default = 0")
            
            if self.selected_template_id:
                # Update existing template
                cursor.execute("""
                UPDATE email_templates
                SET name = ?, subject = ?, body = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """, (
                    template_data['name'],
                    template_data['subject'],
                    template_data['body'],
                    template_data['is_default'],
                    self.selected_template_id
                ))
            else:
                # Add new template
                cursor.execute("""
                INSERT INTO email_templates (name, subject, body, is_default)
                VALUES (?, ?, ?, ?)
                """, (
                    template_data['name'],
                    template_data['subject'],
                    template_data['body'],
                    template_data['is_default']
                ))
            
            conn.commit()
            messagebox.showinfo("Success", "Template saved successfully.")
            
            # Reload templates
            self.load_templates()
        except Exception as e:
            conn.rollback()
            self.error_var.set(f"Error saving template: {str(e)}")
        finally:
            conn.close()
