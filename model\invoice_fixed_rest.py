    def get_invoice(self, invoice_id):
        """Get an invoice by ID with its items

        Args:
            invoice_id (int): ID of the invoice to retrieve

        Returns:
            dict: Dictionary containing invoice information and items, or None if not found
        """
        # Get invoice
        query = f'''
        SELECT i.*, c.name as client_name, c.company_name as client_company
        FROM {self.table_name()} i
        LEFT JOIN clients c ON i.client_id = c.id
        WHERE i.id = ?
        '''
        result = self.execute_query(query, [invoice_id])

        if not result or len(result) == 0:
            return None

        # Get the first row
        invoice = result[0]

        # Get invoice items
        query = '''
        SELECT * FROM invoice_items WHERE invoice_id = ?
        '''
        items = self.execute_query(query, [invoice_id])

        invoice['items'] = items
        return invoice

    def get_all_invoices(self, status=None, client_id=None, order_by="issue_date", order="DESC"):
        """Get all invoices from the database

        Args:
            status (str, optional): Filter by invoice status
            client_id (int, optional): Filter by client ID
            order_by (str): Column to order by (default: "issue_date")
            order (str): Order direction, "ASC" or "DESC" (default: "DESC")

        Returns:
            list: List of dictionaries containing invoice information
        """
        # Validate order_by column
        valid_columns = ['id', 'invoice_number', 'client_id', 'issue_date',
                         'due_date', 'status', 'total_amount', 'created_at']
        if order_by not in valid_columns:
            order_by = "issue_date"

        # Validate order direction
        order = order.upper()
        if order not in ["ASC", "DESC"]:
            order = "DESC"

        # Build the query
        query = f'''
        SELECT i.*, c.name as client_name, c.company_name as client_company
        FROM {self.table_name()} i
        LEFT JOIN clients c ON i.client_id = c.id
        '''

        conditions = []
        params = []

        if status:
            conditions.append("i.status = ?")
            params.append(status)

        if client_id:
            conditions.append("i.client_id = ?")
            params.append(client_id)

        if conditions:
            query += f" WHERE {' AND '.join(conditions)}"

        query += f" ORDER BY i.{order_by} {order}"

        result = self.execute_query(query, params)

        # For SELECT queries, execute_query returns a list of dictionaries
        invoices = result

        return invoices

    def update_invoice_status(self, invoice_id, status):
        """Update the status of an invoice

        Args:
            invoice_id (int): ID of the invoice to update
            status (str): New status for the invoice

        Returns:
            bool: True if the update was successful, False otherwise
        """
        # Validate status
        valid_statuses = [self.STATUS_DRAFT, self.STATUS_SENT,
                          self.STATUS_PAID, self.STATUS_OVERDUE,
                          self.STATUS_CANCELLED]

        if status not in valid_statuses:
            raise ValueError(f"Invalid status: {status}")

        query = f'''
        UPDATE {self.table_name()}
        SET status = ?, updated_at = ?
        WHERE id = ?
        '''

        result = self.execute_query(query, [status, datetime.now(), invoice_id])
        # For UPDATE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False

    def check_overdue_invoices(self):
        """Check for invoices that are past due and update their status

        Returns:
            int: Number of invoices updated to overdue status
        """
        today = datetime.now().strftime("%Y-%m-%d")

        query = f'''
        UPDATE {self.table_name()}
        SET status = ?, updated_at = ?
        WHERE due_date < ? AND status = ?
        '''

        result = self.execute_query(query, [
            self.STATUS_OVERDUE,
            datetime.now(),
            today,
            self.STATUS_SENT
        ])

        # For UPDATE queries, execute_query returns the number of affected rows
        return result if isinstance(result, int) else False
