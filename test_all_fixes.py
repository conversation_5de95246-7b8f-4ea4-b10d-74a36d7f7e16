#!/usr/bin/env python3
"""
Comprehensive test script to verify all reported issues have been fixed
"""

import os
import subprocess
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def run_test_script(script_name, description):
    """Run a test script and return the result"""
    print(f"🧪 Running {description}...")
    print(f"   Script: {script_name}")

    try:
        result = subprocess.run([sys.executable, script_name],
                              capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print(f"   ✅ PASSED: {description}")
            return True
        else:
            print(f"   ❌ FAILED: {description}")
            print(f"   Error output: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print(f"   ⏰ TIMEOUT: {description} took too long")
        return False
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False


def check_file_exists(file_path, description):
    """Check if a file exists"""
    if os.path.exists(file_path):
        print(f"   ✅ {description}: {file_path}")
        return True
    else:
        print(f"   ❌ {description}: {file_path} not found")
        return False


def main():
    """Run comprehensive tests for all fixes"""
    print("=" * 70)
    print("COMPREHENSIVE CASHBOOK FIXES VERIFICATION")
    print("=" * 70)
    print()
    print("This script verifies that all reported issues have been fixed:")
    print("1. Admin login geometry manager conflicts")
    print("2. Settings.db appearing as a company")
    print("3. Missing accounts table errors")
    print("4. Application Settings integration")
    print()

    # Check required files exist
    print("🔍 Checking required files...")
    required_files = [
        ("view/admin_frame.py", "Admin frame"),
        ("view/application_settings_frame.py", "Application settings frame"),
        ("model/application_settings.py", "Application settings model"),
        ("utils/database_migration.py", "Database migration utility"),
        ("tests/test_fixes.py", "Main fixes test script"),
        ("test_settings_exclusion.py", "Settings exclusion test script"),
        ("migrate_databases.py", "Database migration script")
    ]

    files_ok = True
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            files_ok = False

    if not files_ok:
        print("\n❌ Some required files are missing. Cannot proceed with tests.")
        return

    print("\n✅ All required files found")
    print()

    # Run test scripts
    print("🧪 Running test scripts...")
    print()

    tests = [
        ("tests/test_fixes.py", "Main fixes verification"),
        ("test_settings_exclusion.py", "Settings exclusion verification")
    ]

    passed_tests = 0
    total_tests = len(tests)

    for script, description in tests:
        if run_test_script(script, description):
            passed_tests += 1
        print()

    # Check database migration utility
    print("🔧 Testing database migration utility...")
    try:
        result = subprocess.run([sys.executable, "migrate_databases.py", "--quick-fix"],
                              capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("   ✅ PASSED: Database migration utility works")
            migration_ok = True
        else:
            print("   ❌ FAILED: Database migration utility error")
            print(f"   Error: {result.stderr}")
            migration_ok = False
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        migration_ok = False

    print()

    # Summary
    print("=" * 70)
    print("COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    print(f"📁 Required Files: {'✅ All found' if files_ok else '❌ Missing files'}")
    print(f"🧪 Test Scripts: ✅ {passed_tests}/{total_tests} passed")
    print(f"🔧 Migration Utility: {'✅ Working' if migration_ok else '❌ Failed'}")

    all_passed = files_ok and (passed_tests == total_tests) and migration_ok

    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print()
        print("✅ ISSUES FIXED:")
        print("   • Admin login geometry manager conflicts - RESOLVED")
        print("   • Settings.db appearing as a company - RESOLVED")
        print("   • Missing accounts table errors - RESOLVED")
        print("   • Application Settings integration - COMPLETED")
        print()
        print("🚀 READY FOR USE:")
        print("   • Admin can login and access Application Settings")
        print("   • Settings.db is properly excluded from company lists")
        print("   • Database migration handles missing tables automatically")
        print("   • Application Settings are admin-only and fully functional")
        print()
        print("💡 TO TEST THE APPLICATION:")
        print("   1. Run: python main.py")
        print("   2. Login as admin (username: admin, password: admin)")
        print("   3. Verify Application Settings tab is available")
        print("   4. Login as client and verify settings.db doesn't appear")
        print("   5. Try opening companies to verify they work correctly")

    else:
        print("⚠️  SOME TESTS FAILED")
        print()
        print("❌ ISSUES REMAINING:")
        if not files_ok:
            print("   • Missing required files")
        if passed_tests < total_tests:
            print(f"   • {total_tests - passed_tests} test script(s) failed")
        if not migration_ok:
            print("   • Database migration utility not working")
        print()
        print("🔧 NEXT STEPS:")
        print("   • Review failed tests above")
        print("   • Fix any remaining issues")
        print("   • Re-run this comprehensive test")

    print()
    print("📋 TEST SCRIPTS AVAILABLE:")
    print("   • test_fixes.py - Main fixes verification")
    print("   • test_settings_exclusion.py - Settings exclusion verification")
    print("   • migrate_databases.py - Database migration utility")
    print("   • test_all_fixes.py - This comprehensive test (current)")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Tests cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
