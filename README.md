# Cashbook Application

## Overview

Cashbook is a simple personal finance management application that helps you track income and expenses, generate reports, and import transactions from various file formats.

## Features

- Track income and expenses with categories
- Manage multiple accounts with different currencies
- Filter transactions by date range, account, and search terms
- Reconcile transactions with bank statements
- Import transactions from various formats (CSV, Excel, QIF, CB)
- Generate financial reports
- User-friendly interface for managing personal finances

## Installation

### Prerequisites

- Python 3.7+ installed on your system
- Git (optional, for cloning the repository)

### Setup Instructions

1. Clone the repository or download the source code:

   ```
   git clone https://github.com/yourusername/cashbook.git
   cd cashbook
   ```

2. Create and activate a virtual environment:

   **Windows**

   ```
   python -m venv venv
   .venv\Scripts\activate
   ```

   **macOS/Linux**

   ```
   python -m venv venv
   source venv/bin/activate
   ```

3. Install the required dependencies:

   ```
   pip install -r requirements.txt
   ```

   Note: If you don't need Excel file support, you can install only the basic requirements.
   The pandas library is made optional to reduce dependency requirements.

4. Run the application:
   ```
   python main.py
   ```

## Usage

### Login Credentials

When prompted to log in, use these default credentials:

- **Username:** admin
- **Password:** admin

### Importing Transactions

The application supports importing transactions from:

- CSV files
- Excel files (requires pandas and openpyxl)
- QIF files (Quicken Interchange Format)
- CB files (Cashbook native format)

### Optional Dependencies

- pandas and openpyxl: Required for Excel file import support
- qif: Required for QIF file import support

## Development

### Project Structure

- `main.py` - Application entry point
- `model/` - Data models and database interactions
  - `account.py` - Account management functionality
  - `base_model.py` - Base model class with common CRUD operations
  - `category.py` - Category management functionality
  - `database.py` - Database initialization and user management
  - `transaction.py` - Transaction management functionality
- `view/` - User interface components
  - `account_management_frame.py` - Account management UI
  - `category_management_frame.py` - Category management UI
  - `company_frame.py` - Main transaction management UI
  - `components/` - Reusable UI components
    - `date_picker.py` - Custom date picker component
- `tests/` - Unit and integration tests

### Adding New Features

Contributions are welcome! Please make sure to test your changes before submitting pull requests.

## Troubleshooting

If you encounter import errors related to pandas or other optional dependencies, make sure to install the relevant packages:

```
pip install pandas openpyxl
pip install qifparse
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
