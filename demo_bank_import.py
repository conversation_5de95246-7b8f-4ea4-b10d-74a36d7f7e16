#!/usr/bin/env python3
"""
Demo script for Phase 11.1 Bank Statement Import functionality
Demonstrates the new bank import features including templates, duplicate detection, and enhanced import capabilities.
"""

import csv
import os
import tempfile
from datetime import datetime

from model.account import Account
from model.bank_format_template import BankFormatTemplate
from model.database import Database
from model.transaction import TransactionManager
from utils.bank_statement_importer import BankStatementImporter
from utils.database_migration import DatabaseMigration
from utils.duplicate_detector import DuplicateDetector


def get_existing_company_or_create_temp():
    """Get an existing company database or create a temporary one for demo"""
    print("Looking for existing companies...")

    db_manager = Database()
    companies = db_manager.get_companies()

    if companies:
        # Use the first existing company
        company = companies[0]
        db_path = company['db_path']
        print(f"Using existing company: {company['name']}")

        # Run database migration to ensure all new fields are present
        print("Running database migration...")
        DatabaseMigration.migrate_company_database(db_path)

        return db_path, False  # False = not temporary

    else:
        # Create temporary database for demo
        print("No existing companies found. Creating temporary demo database...")
        import tempfile

        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        db_path = temp_db.name

        # Create basic database structure
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Create basic tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL,
                description TEXT,
                created_date TEXT,
                classification TEXT,
                account_number TEXT,
                parent_id INTEGER,
                is_active INTEGER DEFAULT 1
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                currency_code TEXT DEFAULT 'USD',
                exchange_rate REAL DEFAULT 1.0,
                base_amount REAL,
                import_hash TEXT,
                bank_reference TEXT,
                import_source TEXT,
                import_date TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)

        # Insert default category
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("General", "expense"))

        conn.commit()
        conn.close()

        # Run database migration to add import tables
        print("Running database migration...")
        DatabaseMigration.migrate_company_database(db_path)

        print("Created temporary demo database")
        return db_path, True  # True = temporary


def setup_demo_account(db_path):
    """Set up a demo account for import testing"""
    account_manager = Account(db_path)

    # Check if accounts already exist
    accounts = account_manager.get_all_accounts()
    if accounts:
        print(f"Using existing account: {accounts[0]['name']}")
        return accounts[0]['id']

    # Create demo account
    account_id = account_manager.create_account(
        name="Demo Checking Account",
        account_type="checking",
        currency="USD",
        opening_balance=3000.00,
        description="Demo account for bank import testing"
    )
    print("Created demo checking account")
    return account_id


def create_sample_csv_files():
    """Create sample CSV files for different bank formats"""
    print("Creating sample CSV files...")
    
    # ANZ format CSV
    anz_csv = "sample_anz_statement.csv"
    with open(anz_csv, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["Date", "Description", "Amount", "Balance"])
        writer.writerow(["15/01/2024", "EFTPOS Purchase - Grocery Store", "-125.50", "2874.50"])
        writer.writerow(["16/01/2024", "Direct Credit - Salary Payment", "2500.00", "5374.50"])
        writer.writerow(["17/01/2024", "ATM Withdrawal", "-100.00", "5274.50"])
        writer.writerow(["18/01/2024", "Online Transfer - Rent Payment", "-1200.00", "4074.50"])
        writer.writerow(["19/01/2024", "EFTPOS Purchase - Gas Station", "-65.75", "4008.75"])
    
    # ASB format CSV (with separate debit/credit columns)
    asb_csv = "sample_asb_statement.csv"
    with open(asb_csv, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["Date", "Description", "Debit", "Credit", "Balance"])
        writer.writerow(["20/01/2024", "Supermarket Purchase", "89.45", "", "3919.30"])
        writer.writerow(["21/01/2024", "Interest Payment", "", "15.25", "3934.55"])
        writer.writerow(["22/01/2024", "Utility Bill Payment", "156.78", "", "3777.77"])
        writer.writerow(["23/01/2024", "Freelance Payment", "", "750.00", "4527.77"])
    
    # Create QIF file
    qif_file = "sample_bank_statement.qif"
    with open(qif_file, 'w') as f:
        f.write("""!Type:Bank
D01/24/2024
T-45.00
PGas Station
MFuel for car
^
D01/25/2024
T1500.00
PClient Payment
MInvoice #123 payment
^
D01/26/2024
T-25.50
PCoffee Shop
MBusiness meeting
^
""")
    
    return anz_csv, asb_csv, qif_file


def demo_bank_templates(db_path):
    """Demonstrate bank template functionality"""
    print("\n" + "="*60)
    print("DEMO: Bank Format Templates")
    print("="*60)
    
    template_manager = BankFormatTemplate(db_path)
    
    # Initialize default templates
    print("Initializing default bank templates...")
    template_manager.initialize_default_templates()
    
    # Get all templates
    templates = template_manager.get_all_templates()
    print(f"\nAvailable templates ({len(templates)}):")
    for template in templates:
        print(f"  - {template['bank_name']}: {template['name']} ({template['file_type']})")
    
    # Create a custom template
    print("\nCreating custom template...")
    custom_template_id = template_manager.create_template(
        name="Custom Bank CSV",
        bank_name="Custom Bank",
        file_type="CSV",
        field_mapping={
            "date": 0,
            "description": 1,
            "amount": 2,
            "balance": 3
        },
        date_format="%d/%m/%Y",
        delimiter=",",
        has_header=True
    )
    print(f"Created custom template with ID: {custom_template_id}")
    
    return templates


def demo_duplicate_detection(db_path):
    """Demonstrate duplicate detection functionality"""
    print("\n" + "="*60)
    print("DEMO: Duplicate Detection")
    print("="*60)
    
    duplicate_detector = DuplicateDetector(db_path)
    transaction_manager = TransactionManager(db_path)
    
    # Add some sample transactions
    print("Adding sample transactions...")
    trans_id1 = transaction_manager.add_transaction(
        date="2024-01-15",
        amount=125.50,
        account_id=1,
        description="Grocery Store Purchase",
        type_name="expense"
    )
    
    trans_id2 = transaction_manager.add_transaction(
        date="2024-01-16",
        amount=2500.00,
        account_id=1,
        description="Salary Payment",
        type_name="income"
    )
    
    # Update transaction hashes
    duplicate_detector.update_transaction_hash(trans_id1, "2024-01-15", 125.50, "Grocery Store Purchase", 1)
    duplicate_detector.update_transaction_hash(trans_id2, "2024-01-16", 2500.00, "Salary Payment", 1)
    
    # Test exact duplicate detection
    print("\nTesting exact duplicate detection...")
    has_exact, exact_dups = duplicate_detector.check_exact_duplicate(
        "2024-01-15", 125.50, "Grocery Store Purchase", 1
    )
    print(f"Exact duplicate found: {has_exact}")
    if has_exact:
        print(f"Number of exact duplicates: {len(exact_dups)}")
    
    # Test fuzzy duplicate detection
    print("\nTesting fuzzy duplicate detection...")
    has_fuzzy, fuzzy_dups = duplicate_detector.check_fuzzy_duplicates(
        "2024-01-15", 125.50, "Grocery Store", 1
    )
    print(f"Fuzzy duplicates found: {has_fuzzy}")
    if has_fuzzy:
        for dup in fuzzy_dups:
            print(f"  - Similarity: {dup['similarity_score']:.2f}, Description: {dup['description']}")
    
    # Test comprehensive duplicate check
    print("\nTesting comprehensive duplicate check...")
    duplicate_report = duplicate_detector.check_all_duplicates(
        "2024-01-17", 125.00, "Grocery Store Shopping", 1
    )
    print(f"Recommendation: {duplicate_report['recommendation']}")
    print(f"Has exact duplicates: {duplicate_report['has_exact_duplicates']}")
    print(f"Has fuzzy duplicates: {duplicate_report['has_fuzzy_duplicates']}")


def demo_csv_import(db_path, csv_files):
    """Demonstrate CSV import with templates"""
    print("\n" + "="*60)
    print("DEMO: CSV Import with Templates")
    print("="*60)
    
    importer = BankStatementImporter(db_path)
    template_manager = BankFormatTemplate(db_path)
    
    # Get ANZ template
    templates = template_manager.get_all_templates()
    anz_template = next((t for t in templates if t['name'] == 'ANZ CSV Standard'), None)
    
    if anz_template:
        print(f"Using ANZ template (ID: {anz_template['id']}) for import...")
        
        # Import ANZ CSV file
        anz_csv, asb_csv, qif_file = csv_files
        import_result = importer.import_with_duplicate_check(
            anz_csv,
            account_id=1,
            template_id=anz_template['id'],
            auto_categorize=True,
            skip_duplicates=True
        )
        
        print("\nImport Results:")
        print(f"  Total transactions: {import_result['total_transactions']}")
        print(f"  Successfully imported: {import_result['imported_count']}")
        print(f"  Duplicates skipped: {import_result['duplicate_count']}")
        print(f"  Errors: {import_result['error_count']}")
        print(f"  Requiring review: {import_result['review_count']}")
        
        if import_result['duplicates']:
            print("\nDuplicate transactions found:")
            for dup in import_result['duplicates']:
                trans = dup['transaction']
                print(f"  - {trans['date']}: {trans.get('description', 'N/A')} (${trans['amount']:.2f})")


def demo_qif_import(db_path, qif_file):
    """Demonstrate QIF file import"""
    print("\n" + "="*60)
    print("DEMO: QIF File Import")
    print("="*60)
    
    importer = BankStatementImporter(db_path)
    
    # Parse QIF file
    print("Parsing QIF file...")
    transactions = importer.parse_qif_file(qif_file)
    
    print(f"Found {len(transactions)} transactions in QIF file:")
    for trans in transactions:
        print(f"  - {trans['date']}: {trans.get('description', 'N/A')} (${trans['amount']:.2f}) [{trans['type']}]")
    
    # Import QIF file
    print("\nImporting QIF transactions...")
    import_result = importer.import_with_duplicate_check(
        qif_file,
        account_id=1,
        template_id=None,  # QIF doesn't need template
        auto_categorize=True,
        skip_duplicates=True
    )
    
    print("\nQIF Import Results:")
    print(f"  Total transactions: {import_result['total_transactions']}")
    print(f"  Successfully imported: {import_result['imported_count']}")
    print(f"  Duplicates skipped: {import_result['duplicate_count']}")
    print(f"  Errors: {import_result['error_count']}")


def cleanup_demo_files(csv_files, qif_file, db_path):
    """Clean up demo files"""
    print("\n" + "="*60)
    print("Cleaning up demo files...")
    
    files_to_remove = list(csv_files) + [qif_file]
    if db_path:  # Only add db_path if it's not None
        files_to_remove.append(db_path)

    for file_path in files_to_remove:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
            print(f"Removed: {file_path}")

    print("Cleanup completed!")


def main():
    """Main demo function"""
    print("="*60)
    print("PHASE 11.1 BANK STATEMENT IMPORT DEMO")
    print("="*60)
    print("This demo showcases the new bank statement import functionality:")
    print("- Bank format templates for major banks")
    print("- Advanced duplicate detection with fuzzy matching")
    print("- Enhanced CSV and QIF import capabilities")
    print("- Comprehensive import tracking and reporting")
    
    try:
        # Get existing company or create temporary database
        db_path, is_temporary = get_existing_company_or_create_temp()

        # Set up demo account
        account_id = setup_demo_account(db_path)
        
        # Create sample files
        csv_files = create_sample_csv_files()
        qif_file = csv_files[2]  # QIF file is the third item
        
        # Run demos
        demo_bank_templates(db_path)
        demo_duplicate_detection(db_path)
        demo_csv_import(db_path, csv_files, account_id)
        demo_qif_import(db_path, qif_file, account_id)
        
        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("All bank import features have been demonstrated.")
        print("The new functionality provides:")
        print("- Seamless bank statement imports")
        print("- Intelligent duplicate detection")
        print("- Professional import reporting")
        print("- Support for major bank formats")

        # Handle cleanup based on whether we used temporary or existing database
        if is_temporary:
            keep_files = input("\nKeep temporary demo files for inspection? (y/n): ").lower().strip()
            if keep_files != 'y':
                cleanup_demo_files(csv_files, qif_file, db_path)
            else:
                print(f"\nDemo files kept:")
                print(f"  Database: {db_path}")
                print(f"  CSV files: {', '.join(csv_files[:2])}")
                print(f"  QIF file: {qif_file}")
        else:
            print(f"\nDemo used existing company database: {db_path}")
            print("Demo transactions have been added to your existing company.")
            # Clean up sample files only
            cleanup_demo_files(csv_files, qif_file, None)
        
    except Exception as e:
        print(f"\nDemo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
