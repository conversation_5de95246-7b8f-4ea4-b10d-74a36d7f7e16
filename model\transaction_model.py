import sqlite3
from datetime import datetime

from model.base_model import BaseModel


class TransactionModel(BaseModel):
    """
    Model class for transactions
    """
    def __init__(self, db_path, id=None, date=None, amount=0.0, description=None, 
                 category_id=None, account_id=None, type=None, reconciled=0):
        super().__init__(db_path)
        self.id = id
        self.date = date
        self.amount = amount
        self.description = description
        self.category_id = category_id
        self.account_id = account_id
        self.type = type
        self.reconciled = reconciled
    
    def table_name(self):
        return "transactions"
    
    def fields(self):
        return ["id", "date", "amount", "description", "category_id", "account_id", "type", "reconciled"]
    
    def primary_key(self):
        return "id"
    
    def validate(self):
        """Validate transaction data"""
        if not self.date:
            raise ValueError("Transaction date is required")
        
        if not self.amount or self.amount <= 0:
            raise ValueError("Transaction amount must be positive")
        
        if not self.account_id:
            raise ValueError("Account is required")
        
        if self.type not in ["income", "expense", "transfer", "initial"]:
            raise ValueError("Invalid transaction type")
    
    def save(self):
        """Save the transaction to the database"""
        self.validate()
        
        data = {
            "date": self.date,
            "amount": self.amount,
            "description": self.description,
            "category_id": self.category_id,
            "account_id": self.account_id,
            "type": self.type,
            "reconciled": self.reconciled
        }
        
        if self.id:
            # Update existing transaction
            success = self.update(self.id, data)
            
            # Update account balance
            if success:
                self._update_account_balance(self.account_id)
                
            return success
        else:
            # Create new transaction
            self.id = self.create(data)
            
            # Update account balance
            if self.id:
                self._update_account_balance(self.account_id)
                
            return self.id is not None
    
    def _update_account_balance(self, account_id):
        """Update account balance based on transactions"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Get opening balance
            cursor.execute("SELECT opening_balance FROM accounts WHERE id = ?", (account_id,))
            opening_balance = cursor.fetchone()[0]
            
            # Sum all non-initial transactions
            cursor.execute("""
                SELECT SUM(CASE WHEN type = 'expense' THEN -amount 
                                WHEN type = 'income' THEN amount
                                ELSE 0 END)
                FROM transactions
                WHERE account_id = ? AND type != 'initial'
            """, (account_id,))
            
            transaction_sum = cursor.fetchone()[0] or 0
            new_balance = opening_balance + transaction_sum
            
            # Update account
            cursor.execute("""
                UPDATE accounts SET current_balance = ?
                WHERE id = ?
            """, (new_balance, account_id))
            
            conn.commit()
            return True
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating account balance: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def delete(self):
        """Delete the transaction and update account balance"""
        if not self.id:
            return False
        
        # Store account_id before deleting
        account_id = self.account_id
        
        # Delete the transaction
        result = super().delete(self.id)
        
        # Update account balance
        if result and account_id:
            self._update_account_balance(account_id)
        
        return result
    
    def mark_as_reconciled(self, reconciled=1):
        """Mark transaction as reconciled or unreconciled"""
        if not self.id:
            return False
        
        self.reconciled = reconciled
        return self.update(self.id, {"reconciled": reconciled})
    
    @classmethod
    def get_with_related(cls, db_path, transaction_id):
        """Get transaction with related data (account name, category name)"""
        model = cls(db_path)
        
        try:
            conn = model._get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, 
                       a.name as account_name,
                       c.name as category_name,
                       c.type as category_type
                FROM transactions t
                LEFT JOIN accounts a ON t.account_id = a.id
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            
            if result:
                data = dict(result)
                
                # Create and return a model instance
                transaction = cls(
                    db_path,
                    id=data["id"],
                    date=data["date"],
                    amount=data["amount"],
                    description=data["description"],
                    category_id=data["category_id"],
                    account_id=data["account_id"],
                    type=data["type"],
                    reconciled=data["reconciled"]
                )
                
                # Add related data
                transaction.account_name = data.get("account_name")
                transaction.category_name = data.get("category_name")
                transaction.category_type = data.get("category_type")
                
                return transaction
            
            return None
        except sqlite3.Error as e:
            raise Exception(f"Error fetching transaction: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    @classmethod
    def find_with_related(cls, db_path, filters=None, date_from=None, date_to=None, 
                         account_id=None, category_id=None, type_name=None, 
                         reconciled=None, sort_by="date", sort_order="DESC", 
                         limit=100, offset=0):
        """Find transactions with related data and optional filters"""
        model = cls(db_path)
        
        try:
            conn = model._get_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT t.*, 
                       a.name as account_name,
                       c.name as category_name,
                       c.type as category_type
                FROM transactions t
                LEFT JOIN accounts a ON t.account_id = a.id
                LEFT JOIN categories c ON t.category_id = c.id
            """
            
            conditions = []
            params = []
            
            # Add specific filters
            if date_from:
                conditions.append("t.date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("t.date <= ?")
                params.append(date_to)
            
            if account_id:
                conditions.append("t.account_id = ?")
                params.append(account_id)
            
            if category_id:
                conditions.append("t.category_id = ?")
                params.append(category_id)
            
            if type_name:
                conditions.append("t.type = ?")
                params.append(type_name)
            
            if reconciled is not None:
                conditions.append("t.reconciled = ?")
                params.append(reconciled)
            
            # Add text search filters
            if filters:
                for key, value in filters.items():
                    if key.startswith("t."):
                        conditions.append(f"{key} LIKE ?")
                        params.append(f"%{value}%")
                    elif key == "description":
                        conditions.append("t.description LIKE ?")
                        params.append(f"%{value}%")
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            # Add sorting
            valid_sort_columns = ["date", "amount", "description", "type", "reconciled"]
            sort_column = sort_by if sort_by in valid_sort_columns else "date"
            
            valid_sort_orders = ["ASC", "DESC"]
            order = sort_order.upper() if sort_order.upper() in valid_sort_orders else "DESC"
            
            query += f" ORDER BY t.{sort_column} {order}"
            
            # Add pagination
            query += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            transactions = []
            for row in results:
                data = dict(row)
                
                # Create a model instance
                transaction = cls(
                    db_path,
                    id=data["id"],
                    date=data["date"],
                    amount=data["amount"],
                    description=data["description"],
                    category_id=data["category_id"],
                    account_id=data["account_id"],
                    type=data["type"],
                    reconciled=data["reconciled"]
                )
                
                # Add related data
                transaction.account_name = data.get("account_name")
                transaction.category_name = data.get("category_name")
                transaction.category_type = data.get("category_type")
                
                transactions.append(transaction)
            
            return transactions
        except sqlite3.Error as e:
            raise Exception(f"Error fetching transactions: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    @classmethod
    def get_balance_summary(cls, db_path, account_id=None, date_from=None, date_to=None):
        """Get summary of income, expenses, and balance"""
        model = cls(db_path)
        
        try:
            conn = model._get_connection()
            cursor = conn.cursor()
            
            conditions = []
            params = []
            
            if account_id:
                conditions.append("account_id = ?")
                params.append(account_id)
            
            if date_from:
                conditions.append("date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("date <= ?")
                params.append(date_to)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            # Get total income
            income_query = f"SELECT SUM(amount) FROM transactions WHERE type = 'income'{where_clause}"
            cursor.execute(income_query, params)
            total_income = cursor.fetchone()[0] or 0
            
            # Get total expenses
            expense_query = f"SELECT SUM(amount) FROM transactions WHERE type = 'expense'{where_clause}"
            cursor.execute(expense_query, params)
            total_expenses = cursor.fetchone()[0] or 0
            
            return {
                "income": total_income,
                "expenses": total_expenses,
                "balance": total_income - total_expenses
            }
        except sqlite3.Error as e:
            raise Exception(f"Error calculating balance: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    @classmethod
    def get_by_category(cls, db_path, date_from=None, date_to=None, account_id=None):
        """Get transaction totals grouped by category"""
        model = cls(db_path)
        
        try:
            conn = model._get_connection()
            cursor = conn.cursor()
            
            conditions = []
            params = []
            
            if date_from:
                conditions.append("t.date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("t.date <= ?")
                params.append(date_to)
            
            if account_id:
                conditions.append("t.account_id = ?")
                params.append(account_id)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            query = f"""
                SELECT c.id as category_id, c.name, c.type as category_type, 
                       SUM(t.amount) as total
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                {where_clause}
                GROUP BY t.category_id, c.name, c.type
                ORDER BY c.type, total DESC
            """
            
            cursor.execute(query, params)
            results = [dict(row) for row in cursor.fetchall()]
            
            return results
        except sqlite3.Error as e:
            raise Exception(f"Error fetching category summary: {str(e)}")
        finally:
            if conn:
                conn.close()
