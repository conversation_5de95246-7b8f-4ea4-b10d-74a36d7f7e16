import tkinter as tk
from tkinter import messagebox, ttk as tkinter_ttk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from datetime import datetime
import sqlite3

from model.currency import Currency
from model.transaction import TransactionManager
from model.account import Account
from model.category import Category


class MultiCurrencyTransactionFrame(ttk.Frame):
    """Frame for adding multi-currency transactions"""

    def __init__(self, parent, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = "Multi-Currency Transaction"

        # Create models
        self.currency_model = Currency(db_path)
        self.transaction_manager = TransactionManager(db_path)
        self.account_model = Account(db_path)
        self.category_model = Category(db_path)

        # Variables
        self.transaction_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.description_var = tk.StringVar()
        self.amount_var = tk.DoubleVar()
        self.account_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.transaction_type_var = tk.StringVar(value="expense")
        self.currency_var = tk.StringVar()
        self.exchange_rate_var = tk.DoubleVar(value=1.0)
        self.base_amount_var = tk.DoubleVar()
        self.auto_calculate_var = tk.BooleanVar(value=True)

        self.create_widgets()
        self.load_data()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Transaction form
        form_frame = ttk.LabelFrame(main_frame, text="Transaction Details", padding=20)
        form_frame.pack(fill="both", expand=True, pady=(0, 20))

        # Date
        ttk.Label(form_frame, text="Date:").grid(row=0, column=0, sticky="w", pady=5, padx=(0, 10))
        date_entry = ttk.Entry(form_frame, textvariable=self.transaction_date_var, width=15)
        date_entry.grid(row=0, column=1, sticky="w", pady=5)

        # Transaction Type
        ttk.Label(form_frame, text="Type:").grid(row=0, column=2, sticky="w", pady=5, padx=(20, 10))
        type_frame = ttk.Frame(form_frame)
        type_frame.grid(row=0, column=3, sticky="w", pady=5)

        ttk.Radiobutton(type_frame, text="Income", variable=self.transaction_type_var, 
                       value="income", command=self.on_type_change).pack(side="left")
        ttk.Radiobutton(type_frame, text="Expense", variable=self.transaction_type_var, 
                       value="expense", command=self.on_type_change).pack(side="left", padx=(10, 0))

        # Description
        ttk.Label(form_frame, text="Description:").grid(row=1, column=0, sticky="w", pady=5, padx=(0, 10))
        description_entry = ttk.Entry(form_frame, textvariable=self.description_var, width=40)
        description_entry.grid(row=1, column=1, columnspan=3, sticky="ew", pady=5)

        # Account
        ttk.Label(form_frame, text="Account:").grid(row=2, column=0, sticky="w", pady=5, padx=(0, 10))
        self.account_combo = ttk.Combobox(form_frame, textvariable=self.account_var, width=25, state="readonly")
        self.account_combo.grid(row=2, column=1, columnspan=2, sticky="ew", pady=5)
        self.account_combo.bind("<<ComboboxSelected>>", self.on_account_change)

        # Category
        ttk.Label(form_frame, text="Category:").grid(row=3, column=0, sticky="w", pady=5, padx=(0, 10))
        self.category_combo = ttk.Combobox(form_frame, textvariable=self.category_var, width=25, state="readonly")
        self.category_combo.grid(row=3, column=1, columnspan=2, sticky="ew", pady=5)

        # Currency section
        currency_frame = ttk.LabelFrame(form_frame, text="Currency Information", padding=10)
        currency_frame.grid(row=4, column=0, columnspan=4, sticky="ew", pady=(20, 10))

        # Currency
        ttk.Label(currency_frame, text="Currency:").grid(row=0, column=0, sticky="w", pady=5, padx=(0, 10))
        self.currency_combo = ttk.Combobox(currency_frame, textvariable=self.currency_var, width=15, state="readonly")
        self.currency_combo.grid(row=0, column=1, sticky="w", pady=5)
        self.currency_combo.bind("<<ComboboxSelected>>", self.on_currency_change)

        # Amount in transaction currency
        ttk.Label(currency_frame, text="Amount:").grid(row=0, column=2, sticky="w", pady=5, padx=(20, 10))
        amount_entry = ttk.Entry(currency_frame, textvariable=self.amount_var, width=15)
        amount_entry.grid(row=0, column=3, sticky="w", pady=5)
        amount_entry.bind("<KeyRelease>", self.on_amount_change)

        # Exchange rate
        ttk.Label(currency_frame, text="Exchange Rate:").grid(row=1, column=0, sticky="w", pady=5, padx=(0, 10))
        rate_entry = ttk.Entry(currency_frame, textvariable=self.exchange_rate_var, width=15)
        rate_entry.grid(row=1, column=1, sticky="w", pady=5)
        rate_entry.bind("<KeyRelease>", self.on_rate_change)

        # Auto-calculate checkbox
        auto_calc_check = ttk.Checkbutton(currency_frame, text="Auto-calculate", 
                                         variable=self.auto_calculate_var, command=self.on_auto_calc_change)
        auto_calc_check.grid(row=1, column=2, sticky="w", pady=5, padx=(20, 0))

        # Get rate button
        get_rate_button = ttk.Button(currency_frame, text="Get Current Rate", 
                                   command=self.get_current_rate, bootstyle=INFO)
        get_rate_button.grid(row=1, column=3, sticky="w", pady=5, padx=(10, 0))

        # Base amount (in base currency)
        base_currency = self.currency_model.get_base_currency()
        base_currency_code = base_currency['code']
        base_currency_symbol = base_currency['symbol']

        ttk.Label(currency_frame, text=f"Base Amount ({base_currency_code}):").grid(row=2, column=0, sticky="w", pady=5, padx=(0, 10))
        base_amount_entry = ttk.Entry(currency_frame, textvariable=self.base_amount_var, width=15)
        base_amount_entry.grid(row=2, column=1, sticky="w", pady=5)
        base_amount_entry.bind("<KeyRelease>", self.on_base_amount_change)

        # Conversion info
        self.conversion_info_var = tk.StringVar()
        conversion_label = ttk.Label(currency_frame, textvariable=self.conversion_info_var, 
                                   font=("Segoe UI", 10), foreground="blue")
        conversion_label.grid(row=2, column=2, columnspan=2, sticky="w", pady=5, padx=(20, 0))

        # Configure grid weights
        form_frame.columnconfigure(1, weight=1)
        currency_frame.columnconfigure(1, weight=1)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")

        ttk.Button(button_frame, text="Add Transaction", command=self.add_transaction, 
                  bootstyle=PRIMARY).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="Clear Form", command=self.clear_form, 
                  bootstyle=SECONDARY).pack(side="left", padx=(0, 10))

        # Status
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(button_frame, textvariable=self.status_var, foreground="gray")
        status_label.pack(side="right")

    def load_data(self):
        """Load accounts, categories, and currencies"""
        # Load accounts
        accounts = self.account_model.get_all_accounts()
        account_list = [f"{acc['name']} ({acc['currency']})" for acc in accounts if acc['is_active']]
        self.account_combo['values'] = account_list
        self.accounts_data = accounts

        # Load currencies
        currencies = self.currency_model.get_all_currencies()
        currency_codes = [curr['code'] for curr in currencies]
        self.currency_combo['values'] = currency_codes

        # Set default currency to base currency
        base_currency = self.currency_model.get_base_currency()
        self.currency_var.set(base_currency['code'])

        # Load categories
        self.load_categories()

    def load_categories(self):
        """Load categories based on transaction type"""
        transaction_type = self.transaction_type_var.get()
        categories = self.category_model.get_categories_by_type(transaction_type)
        category_list = [cat['name'] for cat in categories]
        self.category_combo['values'] = category_list

    def on_type_change(self):
        """Handle transaction type change"""
        self.load_categories()
        self.category_var.set("")

    def on_account_change(self, event=None):
        """Handle account selection change"""
        selected_account = self.account_var.get()
        if selected_account:
            # Extract account name and find the account
            account_name = selected_account.split(" (")[0]
            for account in self.accounts_data:
                if account['name'] == account_name:
                    # Set currency to account currency
                    self.currency_var.set(account['currency'])
                    self.on_currency_change()
                    break

    def on_currency_change(self, event=None):
        """Handle currency change"""
        if self.auto_calculate_var.get():
            self.get_current_rate()

    def on_amount_change(self, event=None):
        """Handle amount change"""
        if self.auto_calculate_var.get():
            self.calculate_base_amount()

    def on_rate_change(self, event=None):
        """Handle exchange rate change"""
        if self.auto_calculate_var.get():
            self.calculate_base_amount()

    def on_base_amount_change(self, event=None):
        """Handle base amount change"""
        if not self.auto_calculate_var.get():
            self.calculate_exchange_rate()

    def on_auto_calc_change(self):
        """Handle auto-calculate toggle"""
        if self.auto_calculate_var.get():
            self.calculate_base_amount()

    def get_current_rate(self):
        """Get current exchange rate"""
        try:
            currency_code = self.currency_var.get()
            base_currency = self.currency_model.get_base_currency()
            
            if currency_code and currency_code != base_currency['code']:
                rate = self.currency_model.get_exchange_rate(currency_code, base_currency['code'])
                self.exchange_rate_var.set(rate)
                self.calculate_base_amount()
                self.status_var.set(f"Exchange rate updated: {rate:.6f}")
            else:
                self.exchange_rate_var.set(1.0)
                self.calculate_base_amount()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get exchange rate: {str(e)}")

    def calculate_base_amount(self):
        """Calculate base amount from transaction amount and exchange rate"""
        try:
            amount = self.amount_var.get()
            rate = self.exchange_rate_var.get()
            
            if amount and rate:
                base_amount = amount * rate
                self.base_amount_var.set(base_amount)
                
                # Update conversion info
                currency_code = self.currency_var.get()
                base_currency = self.currency_model.get_base_currency()
                self.conversion_info_var.set(f"{amount:.2f} {currency_code} = {base_amount:.2f} {base_currency['code']}")

        except (ValueError, tk.TclError):
            pass

    def calculate_exchange_rate(self):
        """Calculate exchange rate from amounts"""
        try:
            amount = self.amount_var.get()
            base_amount = self.base_amount_var.get()
            
            if amount and base_amount and amount != 0:
                rate = base_amount / amount
                self.exchange_rate_var.set(rate)
                
                # Update conversion info
                currency_code = self.currency_var.get()
                base_currency = self.currency_model.get_base_currency()
                self.conversion_info_var.set(f"{amount:.2f} {currency_code} = {base_amount:.2f} {base_currency['code']}")

        except (ValueError, tk.TclError, ZeroDivisionError):
            pass

    def add_transaction(self):
        """Add the multi-currency transaction"""
        try:
            # Validate inputs
            if not self.description_var.get().strip():
                messagebox.showerror("Error", "Please enter a description")
                return

            if not self.account_var.get():
                messagebox.showerror("Error", "Please select an account")
                return

            if self.amount_var.get() <= 0:
                messagebox.showerror("Error", "Please enter a valid amount")
                return

            # Get account ID
            selected_account = self.account_var.get()
            account_name = selected_account.split(" (")[0]
            account_id = None
            for account in self.accounts_data:
                if account['name'] == account_name:
                    account_id = account['id']
                    break

            if not account_id:
                messagebox.showerror("Error", "Invalid account selected")
                return

            # Get category ID
            category_id = None
            if self.category_var.get():
                categories = self.category_model.get_categories_by_type(self.transaction_type_var.get())
                for cat in categories:
                    if cat['name'] == self.category_var.get():
                        category_id = cat['id']
                        break

            # Add transaction
            transaction_id = self.transaction_manager.add_transaction(
                date=self.transaction_date_var.get(),
                amount=self.amount_var.get(),
                account_id=account_id,
                description=self.description_var.get().strip(),
                category_id=category_id,
                type_name=self.transaction_type_var.get(),
                currency_code=self.currency_var.get(),
                exchange_rate=self.exchange_rate_var.get(),
                base_amount=self.base_amount_var.get()
            )

            if transaction_id:
                messagebox.showinfo("Success", "Multi-currency transaction added successfully!")
                self.clear_form()
                self.status_var.set("Transaction added successfully")
            else:
                messagebox.showerror("Error", "Failed to add transaction")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to add transaction: {str(e)}")

    def clear_form(self):
        """Clear the form"""
        self.transaction_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.description_var.set("")
        self.amount_var.set(0.0)
        self.account_var.set("")
        self.category_var.set("")
        self.transaction_type_var.set("expense")
        base_currency = self.currency_model.get_base_currency()
        self.currency_var.set(base_currency['code'])
        self.exchange_rate_var.set(1.0)
        self.base_amount_var.set(0.0)
        self.conversion_info_var.set("")
        self.status_var.set("Form cleared")
        self.load_categories()
