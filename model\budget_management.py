import sqlite3
from datetime import datetime
from model.base_model import BaseModel


class BudgetManagement(BaseModel):
    """Model for managing budgets and budget vs actual analysis"""

    def __init__(self, db_path):
        super().__init__(db_path)
        self._table_name = "budgets"
        self._create_tables()

    def table_name(self):
        return self._table_name

    def fields(self):
        return [
            'id', 'budget_name', 'budget_year', 'budget_period', 'account_id', 
            'category', 'budgeted_amount', 'description', 'created_by', 
            'created_at', 'updated_at', 'is_active'
        ]

    def primary_key(self):
        return 'id'

    def _create_tables(self):
        """Create budget-related tables"""
        # Create budgets table
        budgets_query = '''
        CREATE TABLE IF NOT EXISTS budgets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            budget_name TEXT NOT NULL,
            budget_year INTEGER NOT NULL,
            budget_period TEXT NOT NULL,
            account_id INTEGER,
            category TEXT NOT NULL,
            budgeted_amount REAL NOT NULL DEFAULT 0.0,
            description TEXT,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
        '''
        self.execute_query(budgets_query)

        # Create budget templates table
        budget_templates_query = '''
        CREATE TABLE IF NOT EXISTS budget_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_name TEXT NOT NULL,
            description TEXT,
            template_data TEXT,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
        '''
        self.execute_query(budget_templates_query)

        # Create indexes
        index_queries = [
            'CREATE INDEX IF NOT EXISTS idx_budgets_year_period ON budgets (budget_year, budget_period)',
            'CREATE INDEX IF NOT EXISTS idx_budgets_category ON budgets (category)',
            'CREATE INDEX IF NOT EXISTS idx_budgets_account ON budgets (account_id)'
        ]
        
        for query in index_queries:
            self.execute_query(query)

    def create_budget(self, budget_name, budget_year, budget_period, category, 
                     budgeted_amount, account_id=None, description="", created_by=""):
        """Create a new budget entry"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO budgets 
                (budget_name, budget_year, budget_period, account_id, category, 
                 budgeted_amount, description, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (budget_name, budget_year, budget_period, account_id, category,
                  budgeted_amount, description, created_by, datetime.now(), datetime.now()))
            
            budget_id = cursor.lastrowid
            conn.commit()
            return budget_id
            
        except sqlite3.Error as e:
            print(f"Error creating budget: {e}")
            return None
        finally:
            conn.close()

    def get_budget_by_period(self, budget_year, budget_period):
        """Get all budget entries for a specific year and period"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT b.id, b.budget_name, b.budget_year, b.budget_period,
                       b.account_id, a.name as account_name, b.category,
                       b.budgeted_amount, b.description, b.created_by,
                       b.created_at, b.updated_at, b.is_active
                FROM budgets b
                LEFT JOIN accounts a ON b.account_id = a.id
                WHERE b.budget_year = ? AND b.budget_period = ? AND b.is_active = 1
                ORDER BY b.category, a.name
            ''', (budget_year, budget_period))
            
            budgets = []
            for row in cursor.fetchall():
                budgets.append({
                    'id': row[0],
                    'budget_name': row[1],
                    'budget_year': row[2],
                    'budget_period': row[3],
                    'account_id': row[4],
                    'account_name': row[5],
                    'category': row[6],
                    'budgeted_amount': row[7],
                    'description': row[8],
                    'created_by': row[9],
                    'created_at': row[10],
                    'updated_at': row[11],
                    'is_active': row[12]
                })
            
            return budgets
            
        except sqlite3.Error as e:
            print(f"Error getting budget: {e}")
            return []
        finally:
            conn.close()

    def update_budget(self, budget_id, budgeted_amount, description=""):
        """Update an existing budget entry"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE budgets 
                SET budgeted_amount = ?, description = ?, updated_at = ?
                WHERE id = ?
            ''', (budgeted_amount, description, datetime.now(), budget_id))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            print(f"Error updating budget: {e}")
            return False
        finally:
            conn.close()

    def delete_budget(self, budget_id):
        """Delete a budget entry (soft delete)"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE budgets 
                SET is_active = 0, updated_at = ?
                WHERE id = ?
            ''', (datetime.now(), budget_id))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            print(f"Error deleting budget: {e}")
            return False
        finally:
            conn.close()

    def create_budget_from_template(self, template_id, budget_year, budget_period, budget_name):
        """Create a budget from a template"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get template data
            cursor.execute('SELECT template_data FROM budget_templates WHERE id = ?', (template_id,))
            result = cursor.fetchone()
            
            if not result:
                return False
            
            # Parse template data (assuming JSON format)
            import json
            template_data = json.loads(result[0])
            
            # Create budget entries from template
            for item in template_data:
                cursor.execute('''
                    INSERT INTO budgets 
                    (budget_name, budget_year, budget_period, account_id, category, 
                     budgeted_amount, description, created_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (budget_name, budget_year, budget_period, item.get('account_id'),
                      item['category'], item['budgeted_amount'], item.get('description', ''),
                      item.get('created_by', ''), datetime.now(), datetime.now()))
            
            conn.commit()
            return True
            
        except (sqlite3.Error, json.JSONDecodeError) as e:
            print(f"Error creating budget from template: {e}")
            return False
        finally:
            conn.close()

    def get_budget_summary_by_category(self, budget_year, budget_period):
        """Get budget summary grouped by category"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT category, SUM(budgeted_amount) as total_budget, COUNT(*) as item_count
                FROM budgets
                WHERE budget_year = ? AND budget_period = ? AND is_active = 1
                GROUP BY category
                ORDER BY category
            ''', (budget_year, budget_period))
            
            summary = []
            for row in cursor.fetchall():
                summary.append({
                    'category': row[0],
                    'total_budget': row[1],
                    'item_count': row[2]
                })
            
            return summary
            
        except sqlite3.Error as e:
            print(f"Error getting budget summary: {e}")
            return []
        finally:
            conn.close()

    def copy_budget_to_new_period(self, source_year, source_period, target_year, target_period, 
                                 adjustment_percentage=0.0):
        """Copy budget from one period to another with optional adjustment"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get source budget entries
            cursor.execute('''
                SELECT budget_name, account_id, category, budgeted_amount, description, created_by
                FROM budgets
                WHERE budget_year = ? AND budget_period = ? AND is_active = 1
            ''', (source_year, source_period))
            
            source_budgets = cursor.fetchall()
            
            # Create new budget entries
            for budget in source_budgets:
                budget_name, account_id, category, budgeted_amount, description, created_by = budget
                
                # Apply adjustment
                adjusted_amount = budgeted_amount * (1 + adjustment_percentage / 100)
                
                cursor.execute('''
                    INSERT INTO budgets 
                    (budget_name, budget_year, budget_period, account_id, category, 
                     budgeted_amount, description, created_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (budget_name, target_year, target_period, account_id, category,
                      adjusted_amount, description, created_by, datetime.now(), datetime.now()))
            
            conn.commit()
            return len(source_budgets)
            
        except sqlite3.Error as e:
            print(f"Error copying budget: {e}")
            return 0
        finally:
            conn.close()

    def get_budget_vs_actual_data(self, budget_year, budget_period, start_date, end_date):
        """Get budget vs actual data for analysis"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get budget data
            budget_data = self.get_budget_summary_by_category(budget_year, budget_period)
            
            # Get actual data from journal entries
            cursor.execute('''
                SELECT 
                    CASE 
                        WHEN a.classification = 'Revenue' THEN 'Revenue'
                        WHEN a.classification = 'Expense' THEN 'Expense'
                        ELSE 'Other'
                    END as category,
                    SUM(
                        CASE 
                            WHEN a.classification = 'Revenue' THEN jl.credit_amount
                            WHEN a.classification = 'Expense' THEN jl.debit_amount
                            ELSE 0
                        END
                    ) as actual_amount
                FROM journal_lines jl
                JOIN journal_entries je ON jl.journal_entry_id = je.id
                JOIN accounts a ON jl.account_id = a.id
                WHERE je.date >= ? AND je.date <= ?
                AND a.classification IN ('Revenue', 'Expense')
                GROUP BY 
                    CASE 
                        WHEN a.classification = 'Revenue' THEN 'Revenue'
                        WHEN a.classification = 'Expense' THEN 'Expense'
                        ELSE 'Other'
                    END
            ''', (start_date, end_date))
            
            actual_data = {}
            for row in cursor.fetchall():
                actual_data[row[0]] = row[1]
            
            # Combine budget and actual data
            comparison_data = []
            for budget_item in budget_data:
                category = budget_item['category']
                budget_amount = budget_item['total_budget']
                actual_amount = actual_data.get(category, 0.0)
                variance = actual_amount - budget_amount
                variance_pct = (variance / budget_amount * 100) if budget_amount > 0 else 0
                
                comparison_data.append({
                    'category': category,
                    'budget_amount': budget_amount,
                    'actual_amount': actual_amount,
                    'variance': variance,
                    'variance_percentage': variance_pct,
                    'item_count': budget_item['item_count']
                })
            
            return comparison_data
            
        except sqlite3.Error as e:
            print(f"Error getting budget vs actual data: {e}")
            return []
        finally:
            conn.close()

    def save_budget_template(self, template_name, budget_data, description="", created_by=""):
        """Save a budget as a template for future use"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            import json
            template_data_json = json.dumps(budget_data)
            
            cursor.execute('''
                INSERT INTO budget_templates 
                (template_name, description, template_data, created_by, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (template_name, description, template_data_json, created_by, datetime.now()))
            
            template_id = cursor.lastrowid
            conn.commit()
            return template_id
            
        except (sqlite3.Error, json.JSONEncodeError) as e:
            print(f"Error saving budget template: {e}")
            return None
        finally:
            conn.close()

    def get_budget_templates(self):
        """Get all available budget templates"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, template_name, description, created_by, created_at
                FROM budget_templates
                WHERE is_active = 1
                ORDER BY template_name
            ''')
            
            templates = []
            for row in cursor.fetchall():
                templates.append({
                    'id': row[0],
                    'template_name': row[1],
                    'description': row[2],
                    'created_by': row[3],
                    'created_at': row[4]
                })
            
            return templates
            
        except sqlite3.Error as e:
            print(f"Error getting budget templates: {e}")
            return []
        finally:
            conn.close()
