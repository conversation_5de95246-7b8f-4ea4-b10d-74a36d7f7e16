#!/usr/bin/env python3
"""
Test script to verify Application Settings integration in Admin interface
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import sys

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from view.admin_frame import AdminFrame
from model.database import Database


class MockDatabase:
    """Mock database for testing"""
    
    def get_all_users(self):
        return [
            {"id": 1, "username": "admin", "role": "Admin"},
            {"id": 2, "username": "testuser", "role": "Client"}
        ]
    
    def add_user(self, username, password, role):
        print(f"Mock: Adding user {username} with role {role}")
        return True
    
    def update_user(self, user_id, data):
        print(f"Mock: Updating user {user_id} with data {data}")
        return True
    
    def delete_user(self, user_id):
        print(f"Mock: Deleting user {user_id}")
        return True


class AdminSettingsTest:
    def __init__(self):
        # Create main window
        self.root = ttk.Window(themename="cosmo")
        self.root.title("Admin Settings Integration Test")
        self.root.geometry("1200x800")
        
        # Create mock database
        self.mock_db = MockDatabase()
        
        # Create admin frame
        self.admin_frame = AdminFrame(
            parent=self.root,
            username="admin",
            logout_callback=self.logout,
            db=self.mock_db
        )
        
        self.admin_frame.pack(fill="both", expand=True)
        
        # Show welcome message
        self.show_welcome_message()
        
    def show_welcome_message(self):
        """Show welcome message with instructions"""
        welcome_window = tk.Toplevel(self.root)
        welcome_window.title("Admin Settings Test")
        welcome_window.geometry("500x400")
        welcome_window.transient(self.root)
        welcome_window.grab_set()
        
        # Center the window
        welcome_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 350,
            self.root.winfo_rooty() + 200
        ))
        
        frame = ttk.Frame(welcome_window, padding=20)
        frame.pack(fill="both", expand=True)
        
        # Title
        title_label = ttk.Label(
            frame,
            text="Admin Settings Integration Test",
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Instructions
        instructions = """
Welcome to the Admin Settings Integration Test!

This test verifies that Application Settings are properly 
integrated into the Admin interface.

Features to test:

✓ Application Settings Tab
  - Click on the "Application Settings" tab to access settings
  
✓ Quick Access Button
  - Click the "⚙️ Settings" button in the header for quick access
  
✓ Settings Categories
  - Appearance (themes, fonts, window settings)
  - Regional (date formats, currency, language)
  - Behavior (auto-save, confirmations, tooltips)
  - Data (default values, features)
  - Security (session, passwords, audit)
  - Backup (automatic backup configuration)

✓ Settings Management
  - Save settings
  - Reset to defaults
  - Import/Export settings

✓ Admin-Only Access
  - Settings are only available to admin users
  - Regular clients cannot access these settings

Click "Start Test" to begin testing!
        """
        
        instructions_label = ttk.Label(
            frame,
            text=instructions.strip(),
            font=("Segoe UI", 10),
            justify="left"
        )
        instructions_label.pack(pady=(0, 20))
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x")
        
        start_button = ttk.Button(
            button_frame,
            text="Start Test",
            command=welcome_window.destroy,
            bootstyle=PRIMARY
        )
        start_button.pack(side="left")
        
        # Test settings button
        test_settings_button = ttk.Button(
            button_frame,
            text="Test Settings Button",
            command=self.test_settings_button,
            bootstyle=INFO
        )
        test_settings_button.pack(side="left", padx=(10, 0))
        
        close_button = ttk.Button(
            button_frame,
            text="Close Test",
            command=self.root.quit,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")
    
    def test_settings_button(self):
        """Test the settings quick access button"""
        try:
            # Simulate clicking the settings button
            self.admin_frame.quick_access_settings()
            
            # Show confirmation
            messagebox.showinfo(
                "Test Result",
                "✓ Settings button test successful!\n\nThe Application Settings tab should now be active."
            )
        except Exception as e:
            messagebox.showerror(
                "Test Error",
                f"✗ Settings button test failed!\n\nError: {str(e)}"
            )
    
    def logout(self):
        """Handle logout"""
        result = messagebox.askyesno(
            "Logout",
            "Are you sure you want to logout and close the test?"
        )
        if result:
            self.root.quit()
    
    def run(self):
        """Run the test"""
        print("Starting Admin Settings Integration Test...")
        print("=" * 50)
        print("Testing Features:")
        print("✓ Application Settings tab in Admin interface")
        print("✓ Quick access settings button")
        print("✓ Settings categories and functionality")
        print("✓ Admin-only access control")
        print("=" * 50)
        print("Instructions:")
        print("1. Click on 'Application Settings' tab")
        print("2. Try the '⚙️ Settings' button for quick access")
        print("3. Test different settings categories")
        print("4. Try saving, resetting, and importing/exporting settings")
        print("5. Verify that settings are properly saved and loaded")
        print("=" * 50)
        
        self.root.mainloop()


def main():
    """Main function"""
    print("Admin Settings Integration Test")
    print("=" * 40)
    
    # Check if required files exist
    required_files = [
        "view/admin_frame.py",
        "view/application_settings_frame.py",
        "model/application_settings.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("ERROR: Missing required files:")
        for file in missing_files:
            print(f"  - {file}")
        print("\nPlease ensure all Application Settings files are present.")
        return
    
    print("✓ All required files found")
    print("✓ Starting integration test...")
    
    try:
        app = AdminSettingsTest()
        app.run()
    except Exception as e:
        print(f"ERROR: Failed to start test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
