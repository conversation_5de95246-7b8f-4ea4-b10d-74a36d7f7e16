# Cashbook Application Tests

This directory contains tests for the Cashbook application. The tests cover various aspects of the application, from database operations to UI components and their integration.

## Test Files

- `test_model.py`: Tests for the database and transaction manager
- `test_ui.py`: Tests for the UI components (AdminDashboard, ClientDashboard)
- `test_company.py`: Tests for the CompanyWindow
- `test_company_frame.py`: Tests for the CompanyFrame (modern single-window implementation)
- `test_integration.py`: Integration tests between different components
- `test_controller.py`: Tests for the main application controller
- `test_base_model.py`: Tests for the BaseModel abstract class
- `test_account.py`: Tests for the Account model
- `test_category.py`: Tests for the Category model
- `test_date_picker.py`: Tests for the DatePicker component
- `test_account_management.py`: Tests for the AccountManagementFrame
- `test_reconciliation.py`: Tests for the reconciliation functionality
- `test_import.py`: Tests for the import functionality and rules engine
- `test_financial_reports.py`: Tests for the financial reports functionality
- `test_reports.py`: Tests for the reports functionality
- `test_visualization.py`: Tests for the data visualization functionality

### Phase 7 Tests (Invoicing Integration)

- `test_client.py`: Tests for the Client model and client management functionality
- `test_invoice.py`: Tests for the Invoice model and invoice creation/management
- `test_invoice_template.py`: Tests for the InvoiceTemplate model and template functionality
- `test_email_service.py`: Tests for the EmailService utility and email functionality

## Running Tests

To run all tests, use the `run_tests.py` script:

```
python tests/run_tests.py
```

To run a specific test file:

```
python -m unittest tests/test_model.py
```

To run a specific test case:

```
python -m unittest tests.test_model.TestDatabase
```

To run a specific test method:

```
python -m unittest tests.test_model.TestDatabase.test_authenticate_user
```

## Test Cases and Results

### Database Tests (TestDatabase)

| Test Case                | Description                                                  | Status  |
| ------------------------ | ------------------------------------------------------------ | ------- |
| `test_init_users_db`     | Tests initialization of users database                       | ✅ PASS |
| `test_init_company_db`   | Tests creation of company database                           | ✅ PASS |
| `test_authenticate_user` | Tests user authentication with valid and invalid credentials | ✅ PASS |
| `test_get_all_users`     | Tests retrieving all users from the database                 | ✅ PASS |
| `test_add_user`          | Tests adding a new user to the database                      | ✅ PASS |
| `test_delete_user`       | Tests deleting a user from the database                      | ✅ PASS |

### Transaction Manager Tests (TestTransactionManager)

| Test Case                  | Description                                           | Status  |
| -------------------------- | ----------------------------------------------------- | ------- |
| `test_add_transaction`     | Tests adding a new transaction                        | ✅ PASS |
| `test_edit_transaction`    | Tests editing an existing transaction                 | ✅ PASS |
| `test_delete_transaction`  | Tests deleting a transaction                          | ✅ PASS |
| `test_get_balance_summary` | Tests retrieving balance summary                      | ✅ PASS |
| `test_mark_as_reconciled`  | Tests marking transactions as reconciled/unreconciled | ✅ PASS |

### Transaction Manager Reconciliation Tests (TestTransactionManagerReconciliation)

| Test Case                            | Description                                 | Status  |
| ------------------------------------ | ------------------------------------------- | ------- |
| `test_get_unreconciled_transactions` | Tests retrieving unreconciled transactions  | ✅ PASS |
| `test_mark_as_reconciled`            | Tests marking a transaction as reconciled   | ✅ PASS |
| `test_mark_as_unreconciled`          | Tests marking a transaction as unreconciled | ✅ PASS |
| `test_transaction_methods`           | Tests transaction management methods        | ✅ PASS |
| `test_transaction_rollback`          | Tests transaction rollback                  | ✅ PASS |

### Admin Dashboard Tests (TestAdminDashboard)

| Test Case                       | Description                                    | Status                       |
| ------------------------------- | ---------------------------------------------- | ---------------------------- |
| `test_dashboard_initialization` | Tests that the dashboard initializes correctly | ❌ ERROR (Database access)   |
| `test_delete_user`              | Tests the delete_user method                   | ❌ ERROR (Unique constraint) |
| `test_delete_user_cancelled`    | Tests cancelling the delete_user operation     | ❌ ERROR (Database locked)   |

### Client Dashboard Tests (TestClientDashboard)

| Test Case                        | Description                                    | Status  |
| -------------------------------- | ---------------------------------------------- | ------- |
| `test_dashboard_initialization`  | Tests that the dashboard initializes correctly | ✅ PASS |
| `test_create_company`            | Tests the create_company method                | ✅ PASS |
| `test_open_company_no_selection` | Tests opening a company with no selection      | ✅ PASS |

### Company Window Tests (TestCompanyWindow)

| Test Case                    | Description                                               | Status  |
| ---------------------------- | --------------------------------------------------------- | ------- |
| `test_window_initialization` | Tests that the window initializes correctly               | ✅ PASS |
| `test_summary_display`       | Tests that the summary information is displayed correctly | ✅ PASS |
| `test_add_transaction`       | Tests the add_transaction method                          | ✅ PASS |
| `test_edit_transaction`      | Tests the edit_transaction method                         | ✅ PASS |
| `test_delete_transaction`    | Tests the delete_transaction method                       | ✅ PASS |
| `test_filter_transactions`   | Tests the filter_transactions method                      | ✅ PASS |
| `test_import_file`           | Tests the import_file method                              | ✅ PASS |
| `test_generate_report`       | Tests the generate_report method                          | ✅ PASS |
| `test_share_data`            | Tests the share_data method                               | ✅ PASS |

### Company Frame Tests (TestCompanyFrame)

| Test Case                    | Description                                                 | Status  |
| ---------------------------- | ----------------------------------------------------------- | ------- |
| `test_frame_initialization`  | Tests that the frame initializes correctly                  | ✅ PASS |
| `test_summary_display`       | Tests that the summary information is displayed correctly   | ✅ PASS |
| `test_toggle_search_panel`   | Tests toggling the search panel visibility                  | ✅ PASS |
| `test_toggle_filter_panel`   | Tests toggling the filter panel visibility                  | ✅ PASS |
| `test_reset_filters`         | Tests resetting all filter values                           | ✅ PASS |
| `test_quick_search`          | Tests quick search functionality for different time periods | ✅ PASS |
| `test_add_column_separators` | Tests adding column separators to the transaction table     | ✅ PASS |
| `test_filter_transactions`   | Tests filtering transactions with various filter criteria   | ✅ PASS |

### Integration Tests (TestIntegration)

| Test Case                                      | Description                                                    | Status                            |
| ---------------------------------------------- | -------------------------------------------------------------- | --------------------------------- |
| `test_admin_dashboard_with_database`           | Tests the integration of AdminDashboard with Database          | ❌ ERROR (Foreign key constraint) |
| `test_client_dashboard_with_company`           | Tests the integration of ClientDashboard with company creation | ❌ ERROR (Foreign key constraint) |
| `test_company_window_with_transaction_manager` | Tests the integration of CompanyWindow with TransactionManager | ❌ ERROR (Foreign key constraint) |

### Controller Tests (TestController)

| Test Case                        | Description                                   | Status                   |
| -------------------------------- | --------------------------------------------- | ------------------------ |
| `test_login_invalid_credentials` | Tests login with invalid credentials          | ❌ ERROR (Tkinter error) |
| `test_login_valid_credentials`   | Tests login with valid credentials            | ❌ ERROR (Tkinter error) |
| `test_logout`                    | Tests the logout method                       | ❌ ERROR (Tkinter error) |
| `test_create_company`            | Tests the create_company method               | ❌ ERROR (Tkinter error) |
| `test_create_company_cancelled`  | Tests cancelling the create_company operation | ❌ ERROR (Tkinter error) |
| `test_close_company`             | Tests the close_company method                | ❌ ERROR (Tkinter error) |

### BaseModel Tests (TestBaseModel)

| Test Case                       | Description                            | Status  |
| ------------------------------- | -------------------------------------- | ------- |
| `test_create`                   | Tests creating a new record            | ✅ PASS |
| `test_read`                     | Tests reading a record                 | ✅ PASS |
| `test_update`                   | Tests updating a record                | ✅ PASS |
| `test_delete`                   | Tests deleting a record                | ✅ PASS |
| `test_find_all`                 | Tests finding all records              | ✅ PASS |
| `test_find_all_with_conditions` | Tests finding records with conditions  | ✅ PASS |
| `test_find_all_with_order_by`   | Tests finding records with ordering    | ✅ PASS |
| `test_find_all_with_limit`      | Tests finding records with limit       | ✅ PASS |
| `test_count`                    | Tests counting records                 | ✅ PASS |
| `test_count_with_conditions`    | Tests counting records with conditions | ✅ PASS |
| `test_execute_query`            | Tests executing a custom query         | ✅ PASS |

### Account Tests (TestAccount)

| Test Case                               | Description                                 | Status  |
| --------------------------------------- | ------------------------------------------- | ------- |
| `test_create_account`                   | Tests creating a new account                | ✅ PASS |
| `test_get_account`                      | Tests retrieving an account by ID           | ✅ PASS |
| `test_get_all_accounts`                 | Tests retrieving all accounts               | ✅ PASS |
| `test_update_account`                   | Tests updating an account                   | ✅ PASS |
| `test_delete_account`                   | Tests deleting an account                   | ✅ PASS |
| `test_delete_account_with_transactions` | Tests deleting an account with transactions | ✅ PASS |
| `test_recalculate_balance`              | Tests recalculating account balance         | ✅ PASS |

### Category Tests (TestCategory)

| Test Case                     | Description                      | Status  |
| ----------------------------- | -------------------------------- | ------- |
| `test_get_all_categories`     | Tests getting all categories     | ✅ PASS |
| `test_get_category_by_id`     | Tests getting a category by ID   | ✅ PASS |
| `test_get_category_by_name`   | Tests getting a category by name | ✅ PASS |
| `test_add_category`           | Tests adding a new category      | ✅ PASS |
| `test_update_category`        | Tests updating a category        | ✅ PASS |
| `test_delete_category`        | Tests deleting a category        | ✅ PASS |
| `test_get_categories_by_type` | Tests getting categories by type | ✅ PASS |

### DatePicker Tests (TestDatePicker)

| Test Case                       | Description                                      | Status  |
| ------------------------------- | ------------------------------------------------ | ------- |
| `test_initialization`           | Tests that the date picker initializes correctly | ✅ PASS |
| `test_initialization_with_date` | Tests initialization with a specific date        | ✅ PASS |
| `test_format_date`              | Tests date formatting                            | ✅ PASS |
| `test_parse_date`               | Tests date parsing                               | ✅ PASS |
| `test_validate_date`            | Tests date validation                            | ✅ PASS |
| `test_get_date`                 | Tests getting the selected date                  | ✅ PASS |
| `test_set_date`                 | Tests setting the date                           | ✅ PASS |

### Account Management Tests (TestAccountManagementFrame)

| Test Case                | Description                                       | Status  |
| ------------------------ | ------------------------------------------------- | ------- |
| `test_initialization`    | Tests that the frame initializes correctly        | ✅ PASS |
| `test_load_accounts`     | Tests loading accounts into the treeview          | ✅ PASS |
| `test_add_account_form`  | Tests preparing the form for adding a new account | ✅ PASS |
| `test_cancel_edit`       | Tests cancelling the edit operation               | ✅ PASS |
| `test_close_callback`    | Tests the close callback                          | ✅ PASS |
| `test_show_context_menu` | Tests showing the context menu                    | ✅ PASS |

### Reconciliation Tests (TestReconciliationFrame)

| Test Case                             | Description                                       | Status  |
| ------------------------------------- | ------------------------------------------------- | ------- |
| `test_load_unreconciled_transactions` | Tests loading unreconciled transactions           | ✅ PASS |
| `test_mark_as_reconciled`             | Tests marking transactions as reconciled          | ✅ PASS |
| `test_unmark_as_reconciled`           | Tests unmarking transactions as reconciled        | ✅ PASS |
| `test_auto_match`                     | Tests auto-matching transactions                  | ✅ PASS |
| `test_get_difference`                 | Tests calculating the difference between balances | ✅ PASS |
| `test_update_summary`                 | Tests updating the reconciliation summary         | ✅ PASS |
| `test_generate_reconciliation_report` | Tests generating a reconciliation report          | ✅ PASS |
| `test_create_reconciliation_record`   | Tests creating a reconciliation record in the DB  | ✅ PASS |
| `test_finish_reconciliation`          | Tests finishing the reconciliation process        | ✅ PASS |

### Rules Engine Tests (TestRulesEngine)

| Test Case                           | Description                                       | Status  |
| ----------------------------------- | ------------------------------------------------- | ------- |
| `test_add_rule`                     | Tests adding a new rule                           | ✅ PASS |
| `test_update_rule`                  | Tests updating an existing rule                   | ✅ PASS |
| `test_delete_rule`                  | Tests deleting a rule                             | ✅ PASS |
| `test_get_all_rules`                | Tests retrieving all rules                        | ✅ PASS |
| `test_apply_rules_simple_match`     | Tests applying rules with simple pattern matching | ✅ PASS |
| `test_apply_rules_regex_match`      | Tests applying rules with regex pattern matching  | ✅ PASS |
| `test_apply_rules_case_sensitivity` | Tests applying rules with case sensitivity        | ✅ PASS |
| `test_apply_rules_priority`         | Tests applying rules with different priorities    | ✅ PASS |
| `test_test_rule`                    | Tests the rule testing functionality              | ✅ PASS |

### Transaction Import Tests (TestTransactionImport)

| Test Case                           | Description                               | Status  |
| ----------------------------------- | ----------------------------------------- | ------- |
| `test_is_duplicate_transaction`     | Tests checking for duplicate transactions | ✅ PASS |
| `test_delete_duplicate_transaction` | Tests deleting a duplicate transaction    | ✅ PASS |
| `test_get_category_by_name`         | Tests getting a category by name          | ✅ PASS |
| `test_get_accounts`                 | Tests getting all accounts                | ✅ PASS |

## Test Summary

- **Total Tests**: 147
- **Passed**: 128
- **Failed**: 1
- **Errors**: 18

### Phase 7 Test Coverage

| Test File                  | Test Cases | Description                                                                            |
| -------------------------- | ---------- | -------------------------------------------------------------------------------------- |
| `test_client.py`           | 10         | Tests for the Client model including CRUD operations and search functionality          |
| `test_invoice.py`          | 12         | Tests for the Invoice model including invoice creation, management, and status updates |
| `test_invoice_template.py` | 8          | Tests for the InvoiceTemplate model including template creation and management         |
| `test_email_service.py`    | 10         | Tests for the EmailService utility including email sending and history tracking        |

## Known Issues

1. **Database Locking**: Some tests fail because the database is locked by another process. This is a common issue with SQLite when multiple tests try to access the same database file.

2. **Tkinter Errors**: The controller tests fail due to Tkinter-related errors. This is because Tkinter has limitations when running in a test environment, especially with multiple root windows.

## Fixed Issues

1. **Foreign Key Constraints**: The transaction tests were failing due to foreign key constraints. This has been fixed by adding the necessary setup code to create categories and accounts before running the tests.

2. **Test Database Handling**: Fixed issues with test database files being loaded as company databases by adding filters to exclude test database files.

## Recommendations for Improvement

1. **Mock Database**: Use an in-memory SQLite database for testing to avoid file locking issues.

2. **Setup Test Data**: Ensure all required data (categories, accounts) is created before testing transactions.

3. **Mock UI Components**: Use more mocking for UI components to avoid Tkinter-related errors.

4. **Separate Test Databases**: Use unique database filenames for each test to avoid conflicts.

5. **Transaction Isolation**: Run tests that modify the database in transactions that are rolled back after the test.

## Adding New Tests

When adding new functionality to the application, please add corresponding tests to maintain test coverage.

1. Add unit tests for new model classes or methods
2. Add UI tests for new UI components
3. Add integration tests for interactions between components
4. Update the `run_tests.py` script to include new test cases
