"""
<PERSON><PERSON><PERSON> to convert logo.png to icon.ico for Windows application
"""
import os
from PIL import Image

def create_icon():
    """Convert logo.png to icon.ico"""
    try:
        # Check if resources directory exists
        if not os.path.exists('resources'):
            os.makedirs('resources')
            
        # Check if logo.png exists
        logo_path = os.path.join('resources', 'logo.png')
        if not os.path.exists(logo_path):
            print(f"Warning: {logo_path} not found. Creating a default icon.")
            # Create a simple default icon (blue square)
            img = Image.new('RGB', (256, 256), color=(0, 120, 212))
        else:
            # Open the existing logo
            img = Image.open(logo_path)
            
        # Resize to standard icon sizes
        icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        icon_images = []
        
        for size in icon_sizes:
            resized_img = img.resize(size, Image.LANCZOS)
            icon_images.append(resized_img)
            
        # Save as .ico file
        icon_path = os.path.join('resources', 'icon.ico')
        icon_images[0].save(
            icon_path, 
            format='ICO', 
            sizes=[(img.width, img.height) for img in icon_images]
        )
        
        print(f"Icon created successfully at {icon_path}")
        return True
    except Exception as e:
        print(f"Error creating icon: {e}")
        return False

if __name__ == "__main__":
    create_icon()
