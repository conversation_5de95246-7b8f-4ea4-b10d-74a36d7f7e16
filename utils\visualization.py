import os
import datetime
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
import tkinter as tk
from tkinter import filedialog

# Use Agg backend for better compatibility
matplotlib.use('Agg')

class ChartGenerator:
    """Class for generating financial charts"""
    
    def __init__(self, db_path):
        """Initialize chart generator with database path"""
        self.db_path = db_path
        self.figure = None
        self.canvas = None
        self.toolbar = None
    
    def create_figure(self, figsize=(10, 6), dpi=100):
        """Create a new figure for plotting"""
        self.figure = Figure(figsize=figsize, dpi=dpi)
        return self.figure
    
    def embed_chart(self, parent_widget):
        """Embed the current figure in a tkinter widget"""
        if self.figure is None:
            self.create_figure()
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.figure, master=parent_widget)
        canvas_widget = self.canvas.get_tk_widget()
        canvas_widget.pack(fill=tk.BOTH, expand=True)
        
        # Create toolbar
        self.toolbar = NavigationToolbar2Tk(self.canvas, parent_widget)
        self.toolbar.update()
        
        return canvas_widget
    
    def clear_chart(self):
        """Clear the current figure"""
        if self.figure:
            self.figure.clear()
            if self.canvas:
                self.canvas.draw()
    
    def save_chart(self, default_filename="chart.png"):
        """Save the current chart to a file"""
        if self.figure is None:
            return False
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("PDF files", "*.pdf"),
                ("SVG files", "*.svg")
            ],
            initialfile=default_filename
        )
        
        if file_path:
            self.figure.savefig(file_path, bbox_inches='tight')
            return True
        
        return False
    
    def income_vs_expenses_bar_chart(self, date_from, date_to, account_id=None):
        """Generate income vs expenses bar chart"""
        import sqlite3
        
        # Create a new figure
        self.create_figure()
        
        # Connect to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get monthly income and expenses
        query = """
        SELECT strftime('%Y-%m', date) as month,
               SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as income,
               SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expense
        FROM transactions
        WHERE date >= ? AND date <= ?
        """
        
        params = [date_from, date_to]
        
        if account_id:
            query += " AND account_id = ?"
            params.append(account_id)
        
        query += " GROUP BY month ORDER BY month"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Close connection
        conn.close()
        
        if not results:
            # No data, show empty chart with message
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "No data available for the selected period", 
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes, fontsize=12)
            ax.set_axis_off()
            return self.figure
        
        # Extract data
        months = [row[0] for row in results]
        income = [row[1] for row in results]
        expenses = [row[2] for row in results]
        
        # Format month labels
        month_labels = []
        for month_str in months:
            year, month = month_str.split('-')
            month_date = datetime.datetime(int(year), int(month), 1)
            month_labels.append(month_date.strftime('%b %Y'))
        
        # Create bar chart
        ax = self.figure.add_subplot(111)
        
        x = np.arange(len(months))
        width = 0.35
        
        income_bars = ax.bar(x - width/2, income, width, label='Income', color='green', alpha=0.7)
        expense_bars = ax.bar(x + width/2, expenses, width, label='Expenses', color='red', alpha=0.7)
        
        # Add net income/expense line
        net = [inc - exp for inc, exp in zip(income, expenses)]
        ax.plot(x, net, 'o-', color='blue', label='Net')
        
        # Add data labels
        def add_labels(bars):
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'${height:.0f}',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom', fontsize=8)
        
        add_labels(income_bars)
        add_labels(expense_bars)
        
        # Set chart properties
        ax.set_title('Income vs Expenses by Month')
        ax.set_xlabel('Month')
        ax.set_ylabel('Amount ($)')
        ax.set_xticks(x)
        ax.set_xticklabels(month_labels, rotation=45, ha='right')
        ax.legend()
        
        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Adjust layout
        self.figure.tight_layout()
        
        return self.figure
    
    def category_pie_chart(self, date_from, date_to, chart_type='expense', account_id=None):
        """Generate category pie chart for income or expenses"""
        import sqlite3
        
        # Create a new figure
        self.create_figure()
        
        # Connect to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get category data
        query = """
        SELECT c.name, SUM(t.amount) as total_amount
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.type = ? AND t.date >= ? AND t.date <= ?
        """
        
        params = [chart_type, date_from, date_to]
        
        if account_id:
            query += " AND t.account_id = ?"
            params.append(account_id)
        
        query += " GROUP BY c.name ORDER BY total_amount DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Close connection
        conn.close()
        
        if not results:
            # No data, show empty chart with message
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f"No {chart_type} data available for the selected period", 
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes, fontsize=12)
            ax.set_axis_off()
            return self.figure
        
        # Extract data
        categories = []
        amounts = []
        
        # Group small categories as "Other"
        total = sum(row[1] for row in results)
        threshold = total * 0.03  # Categories less than 3% go to "Other"
        
        other_amount = 0
        for category, amount in results:
            if amount >= threshold:
                categories.append(category or "Uncategorized")
                amounts.append(amount)
            else:
                other_amount += amount
        
        if other_amount > 0:
            categories.append("Other")
            amounts.append(other_amount)
        
        # Create pie chart
        ax = self.figure.add_subplot(111)
        
        # Calculate percentages for labels
        def autopct_format(values):
            def my_format(pct):
                total = sum(values)
                val = int(round(pct*total/100.0))
                return f'{pct:.1f}%\n(${val:,})'
            return my_format
        
        wedges, texts, autotexts = ax.pie(
            amounts, 
            labels=categories, 
            autopct=autopct_format(amounts),
            startangle=90, 
            shadow=False,
            wedgeprops={'edgecolor': 'w', 'linewidth': 1},
            textprops={'fontsize': 9}
        )
        
        # Equal aspect ratio ensures that pie is drawn as a circle
        ax.axis('equal')
        
        # Set chart title
        title = f"{'Expense' if chart_type == 'expense' else 'Income'} Distribution by Category"
        ax.set_title(title)
        
        # Add legend if there are many categories
        if len(categories) > 6:
            ax.legend(wedges, categories, title="Categories", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
        
        # Adjust layout
        self.figure.tight_layout()
        
        return self.figure
    
    def monthly_trend_chart(self, date_from, date_to, account_id=None):
        """Generate monthly trend line chart"""
        import sqlite3
        
        # Create a new figure
        self.create_figure()
        
        # Connect to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get monthly income and expenses
        query = """
        SELECT strftime('%Y-%m', date) as month,
               SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as income,
               SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expense
        FROM transactions
        WHERE date >= ? AND date <= ?
        """
        
        params = [date_from, date_to]
        
        if account_id:
            query += " AND account_id = ?"
            params.append(account_id)
        
        query += " GROUP BY month ORDER BY month"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Close connection
        conn.close()
        
        if not results:
            # No data, show empty chart with message
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "No data available for the selected period", 
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes, fontsize=12)
            ax.set_axis_off()
            return self.figure
        
        # Extract data
        months_str = [row[0] for row in results]
        income = [row[1] for row in results]
        expenses = [row[2] for row in results]
        net = [inc - exp for inc, exp in zip(income, expenses)]
        
        # Convert month strings to datetime objects for better x-axis formatting
        months = [datetime.datetime.strptime(m, '%Y-%m') for m in months_str]
        
        # Create line chart
        ax = self.figure.add_subplot(111)
        
        ax.plot(months, income, 'o-', color='green', label='Income', linewidth=2)
        ax.plot(months, expenses, 'o-', color='red', label='Expenses', linewidth=2)
        ax.plot(months, net, 'o-', color='blue', label='Net', linewidth=2)
        
        # Fill between income and expenses
        ax.fill_between(months, income, expenses, where=[i > e for i, e in zip(income, expenses)], 
                        color='green', alpha=0.2, interpolate=True)
        ax.fill_between(months, income, expenses, where=[i <= e for i, e in zip(income, expenses)], 
                        color='red', alpha=0.2, interpolate=True)
        
        # Format x-axis to show month and year
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
        ax.xaxis.set_major_locator(mdates.MonthLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # Set chart properties
        ax.set_title('Monthly Income and Expense Trends')
        ax.set_xlabel('Month')
        ax.set_ylabel('Amount ($)')
        ax.legend()
        
        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Add data labels
        for i, (m, inc, exp, n) in enumerate(zip(months, income, expenses, net)):
            ax.annotate(f'${inc:.0f}', xy=(m, inc), xytext=(0, 5), 
                        textcoords='offset points', ha='center', fontsize=8)
            ax.annotate(f'${exp:.0f}', xy=(m, exp), xytext=(0, -15), 
                        textcoords='offset points', ha='center', fontsize=8)
            ax.annotate(f'${n:.0f}', xy=(m, n), xytext=(0, 5 if n >= 0 else -15), 
                        textcoords='offset points', ha='center', fontsize=8)
        
        # Adjust layout
        self.figure.tight_layout()
        
        return self.figure
    
    def balance_history_chart(self, date_from, date_to, account_id=None):
        """Generate balance history chart"""
        import sqlite3
        
        # Create a new figure
        self.create_figure()
        
        # Connect to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get account information
        if account_id:
            cursor.execute("SELECT name, opening_balance FROM accounts WHERE id = ?", (account_id,))
            account_info = cursor.fetchone()
            if not account_info:
                # Invalid account ID
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, "Invalid account selected", 
                        horizontalalignment='center', verticalalignment='center',
                        transform=ax.transAxes, fontsize=12)
                ax.set_axis_off()
                conn.close()
                return self.figure
            
            account_name, opening_balance = account_info
            
            # Get all transactions for this account in chronological order
            cursor.execute("""
                SELECT date, 
                       CASE WHEN type = 'income' THEN amount ELSE -amount END as amount
                FROM transactions
                WHERE account_id = ? AND date >= ? AND date <= ?
                ORDER BY date
            """, (account_id, date_from, date_to))
        else:
            # Get all accounts
            cursor.execute("SELECT SUM(opening_balance) FROM accounts")
            opening_balance = cursor.fetchone()[0] or 0
            account_name = "All Accounts"
            
            # Get all transactions in chronological order
            cursor.execute("""
                SELECT date, 
                       CASE WHEN type = 'income' THEN amount ELSE -amount END as amount
                FROM transactions
                WHERE date >= ? AND date <= ?
                ORDER BY date
            """, (date_from, date_to))
        
        transactions = cursor.fetchall()
        
        # Close connection
        conn.close()
        
        if not transactions:
            # No transactions, show empty chart with message
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "No transactions available for the selected period", 
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes, fontsize=12)
            ax.set_axis_off()
            return self.figure
        
        # Calculate running balance
        dates = []
        balances = []
        
        # Start with opening balance
        current_balance = opening_balance
        
        # Add starting point
        start_date = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
        dates.append(start_date)
        balances.append(current_balance)
        
        # Calculate running balance for each transaction
        for date_str, amount in transactions:
            transaction_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
            current_balance += amount
            dates.append(transaction_date)
            balances.append(current_balance)
        
        # Create line chart
        ax = self.figure.add_subplot(111)
        
        # Plot balance history
        ax.plot(dates, balances, 'o-', color='blue', linewidth=2)
        
        # Fill above/below zero
        ax.fill_between(dates, balances, 0, where=[b > 0 for b in balances], 
                        color='green', alpha=0.2, interpolate=True)
        ax.fill_between(dates, balances, 0, where=[b <= 0 for b in balances], 
                        color='red', alpha=0.2, interpolate=True)
        
        # Add horizontal line at zero
        ax.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
        
        # Format x-axis to show dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # Set chart properties
        ax.set_title(f'Balance History for {account_name}')
        ax.set_xlabel('Date')
        ax.set_ylabel('Balance ($)')
        
        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Add final balance annotation
        final_balance = balances[-1]
        ax.annotate(f'Final Balance: ${final_balance:.2f}', 
                    xy=(dates[-1], final_balance),
                    xytext=(10, 0), 
                    textcoords='offset points',
                    fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
        
        # Adjust layout
        self.figure.tight_layout()
        
        return self.figure
