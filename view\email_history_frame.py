import tkinter as tk
from datetime import datetime
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import Scrolled<PERSON>rame

from model.invoice import Invoice
from utils.email_service import EmailService


class EmailHistoryFrame(ttk.Frame):
    """Frame for viewing email history"""

    def __init__(self, parent, db_path, invoice_id=None, close_callback=None):
        """Initialize the email history frame"""
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.invoice_id = invoice_id
        self.close_callback = close_callback
        self.email_service = EmailService(db_path)
        self.invoice_model = Invoice(db_path)

        # Create widgets
        self.create_widgets()

        # Load email history
        self.load_history()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Email History", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20), anchor="w")

        # Filter frame
        filter_frame = ttk.Frame(main_frame)
        filter_frame.pack(fill="x", pady=(0, 10))

        # Invoice filter (only if not viewing a specific invoice)
        if not self.invoice_id:
            # Get all invoices
            invoices = self.invoice_model.get_all_invoices()
            invoice_options = ["All Invoices"]
            invoice_options.extend([f"{inv['invoice_number']} - {inv.get('client_name', '')}" for inv in invoices])

            # Invoice dropdown
            invoice_label = ttk.Label(filter_frame, text="Filter by Invoice:")
            invoice_label.pack(side="left", padx=(0, 5))

            self.invoice_var = tk.StringVar(value="All Invoices")
            invoice_dropdown = ttk.Combobox(filter_frame, textvariable=self.invoice_var, 
                                          values=invoice_options, state="readonly", width=30)
            invoice_dropdown.pack(side="left", padx=(0, 10))
            invoice_dropdown.bind("<<ComboboxSelected>>", self.filter_history)

            # Store invoice mapping
            self.invoice_mapping = {"All Invoices": None}
            for inv in invoices:
                key = f"{inv['invoice_number']} - {inv.get('client_name', '')}"
                self.invoice_mapping[key] = inv['id']

        # Status filter
        status_label = ttk.Label(filter_frame, text="Filter by Status:")
        status_label.pack(side="left", padx=(0, 5))

        self.status_var = tk.StringVar(value="All")
        status_dropdown = ttk.Combobox(filter_frame, textvariable=self.status_var, 
                                     values=["All", "Sent", "Failed"], state="readonly", width=15)
        status_dropdown.pack(side="left")
        status_dropdown.bind("<<ComboboxSelected>>", self.filter_history)

        # Refresh button
        refresh_button = ttk.Button(filter_frame, text="Refresh", 
                                  command=self.load_history, bootstyle=INFO)
        refresh_button.pack(side="right")

        # Email history treeview
        treeview_frame = ttk.Frame(main_frame)
        treeview_frame.pack(fill="both", expand=True, pady=(10, 0))

        # Create treeview
        columns = ("id", "invoice_id", "recipient", "subject", "status", "sent_at")
        self.history_tree = ttk.Treeview(treeview_frame, columns=columns, show="headings", 
                                       selectmode="browse")
        self.history_tree.pack(side="left", fill="both", expand=True)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(treeview_frame, orient="vertical", command=self.history_tree.yview)
        scrollbar.pack(side="right", fill="y")
        self.history_tree.config(yscrollcommand=scrollbar.set)

        # Configure columns
        self.history_tree.heading("id", text="ID")
        self.history_tree.heading("invoice_id", text="Invoice")
        self.history_tree.heading("recipient", text="Recipient")
        self.history_tree.heading("subject", text="Subject")
        self.history_tree.heading("status", text="Status")
        self.history_tree.heading("sent_at", text="Sent At")

        # Set column widths
        self.history_tree.column("id", width=50, stretch=False)
        self.history_tree.column("invoice_id", width=100, stretch=False)
        self.history_tree.column("recipient", width=150)
        self.history_tree.column("subject", width=200)
        self.history_tree.column("status", width=80, stretch=False)
        self.history_tree.column("sent_at", width=150, stretch=False)

        # Bind double-click to view details
        self.history_tree.bind("<Double-1>", self.view_email_details)

        # Create context menu
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="View Details", command=self.view_selected_email)
        self.context_menu.add_command(label="Resend Email", command=self.resend_selected_email)

        # Bind right-click to show context menu
        self.history_tree.bind("<Button-3>", self.show_context_menu)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))

        if self.close_callback:
            close_button = ttk.Button(button_frame, text="Close", 
                                    command=self.close_callback, bootstyle=SECONDARY)
            close_button.pack(side="right")

    def load_history(self):
        """Load email history into the treeview"""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Get email history
        history = self.email_service.get_email_history(self.invoice_id)

        # Get invoice mapping
        invoice_mapping = {}
        invoices = self.invoice_model.get_all_invoices()
        for inv in invoices:
            invoice_mapping[inv['id']] = inv['invoice_number']

        # Add history to treeview
        for email in history:
            # Apply filters
            if hasattr(self, 'invoice_var') and self.invoice_var.get() != "All Invoices":
                selected_id = self.invoice_mapping.get(self.invoice_var.get())
                if email['invoice_id'] != selected_id:
                    continue

            if self.status_var.get() != "All" and email['status'] != self.status_var.get():
                continue

            # Format date
            sent_at = email['sent_at']
            if sent_at:
                try:
                    dt = datetime.strptime(sent_at, "%Y-%m-%d %H:%M:%S.%f")
                    sent_at = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    pass

            # Get invoice number
            invoice_number = invoice_mapping.get(email['invoice_id'], f"ID: {email['invoice_id']}")

            # Add to treeview
            self.history_tree.insert("", "end", values=(
                email['id'],
                invoice_number,
                email['recipient'],
                email['subject'],
                email['status'],
                sent_at
            ))

    def filter_history(self, event=None):
        """Filter email history based on selected filters"""
        self.load_history()

    def view_email_details(self, event=None):
        """View details of the selected email"""
        self.view_selected_email()

    def view_selected_email(self):
        """View details of the selected email"""
        # Get selected item
        selection = self.history_tree.selection()
        if not selection:
            return

        # Get email ID
        email_id = self.history_tree.item(selection[0], "values")[0]

        # Get email details
        conn = self.email_service._get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM email_history WHERE id = ?", (email_id,))
        email = cursor.fetchone()
        conn.close()

        if not email:
            return

        # Convert to dictionary
        columns = ["id", "invoice_id", "recipient", "subject", "body", 
                  "status", "error_message", "sent_at"]
        email_dict = dict(zip(columns, email))

        # Create details dialog
        details_dialog = tk.Toplevel(self)
        details_dialog.title("Email Details")
        details_dialog.geometry("600x500")
        details_dialog.resizable(False, False)
        details_dialog.transient(self)
        details_dialog.grab_set()

        # Create widgets
        details_frame = ttk.Frame(details_dialog, padding=20)
        details_frame.pack(fill="both", expand=True)

        # Email details
        ttk.Label(details_frame, text="Email Details", font=("Helvetica", 14, "bold")).pack(anchor="w", pady=(0, 10))

        # Subject
        subject_frame = ttk.Frame(details_frame)
        subject_frame.pack(fill="x", pady=5)
        ttk.Label(subject_frame, text="Subject:", width=15, anchor="w").pack(side="left")
        ttk.Label(subject_frame, text=email_dict['subject']).pack(side="left", fill="x", expand=True)

        # Recipient
        recipient_frame = ttk.Frame(details_frame)
        recipient_frame.pack(fill="x", pady=5)
        ttk.Label(recipient_frame, text="Recipient:", width=15, anchor="w").pack(side="left")
        ttk.Label(recipient_frame, text=email_dict['recipient']).pack(side="left", fill="x", expand=True)

        # Status
        status_frame = ttk.Frame(details_frame)
        status_frame.pack(fill="x", pady=5)
        ttk.Label(status_frame, text="Status:", width=15, anchor="w").pack(side="left")
        status_color = "green" if email_dict['status'] == "Sent" else "red"
        ttk.Label(status_frame, text=email_dict['status'], foreground=status_color).pack(side="left", fill="x", expand=True)

        # Sent at
        sent_at_frame = ttk.Frame(details_frame)
        sent_at_frame.pack(fill="x", pady=5)
        ttk.Label(sent_at_frame, text="Sent At:", width=15, anchor="w").pack(side="left")
        ttk.Label(sent_at_frame, text=email_dict['sent_at']).pack(side="left", fill="x", expand=True)

        # Error message (if any)
        if email_dict['error_message']:
            error_frame = ttk.LabelFrame(details_frame, text="Error Message")
            error_frame.pack(fill="x", pady=10)
            error_text = tk.Text(error_frame, height=3, width=50, wrap="word")
            error_text.pack(fill="x", padx=10, pady=10)
            error_text.insert("1.0", email_dict['error_message'])
            error_text.config(state="disabled")

        # Email body
        body_frame = ttk.LabelFrame(details_frame, text="Email Body")
        body_frame.pack(fill="both", expand=True, pady=10)
        body_text = tk.Text(body_frame, height=10, width=50, wrap="word")
        body_text.pack(fill="both", expand=True, padx=10, pady=10)
        body_text.insert("1.0", email_dict['body'])
        body_text.config(state="disabled")

        # Add scrollbar to body text
        body_scrollbar = ttk.Scrollbar(body_text, orient="vertical", command=body_text.yview)
        body_scrollbar.pack(side="right", fill="y")
        body_text.config(yscrollcommand=body_scrollbar.set)

        # Close button
        close_button = ttk.Button(details_frame, text="Close", 
                                command=details_dialog.destroy, bootstyle=SECONDARY)
        close_button.pack(pady=(10, 0))

    def resend_selected_email(self):
        """Resend the selected email"""
        # Get selected item
        selection = self.history_tree.selection()
        if not selection:
            return

        # Get email ID and invoice ID
        values = self.history_tree.item(selection[0], "values")
        email_id = values[0]
        invoice_number = values[1]

        # Get invoice ID from invoice number
        invoice_id = None
        invoices = self.invoice_model.get_all_invoices()
        for inv in invoices:
            if inv['invoice_number'] == invoice_number:
                invoice_id = inv['id']
                break

        if not invoice_id:
            messagebox.showerror("Error", f"Invoice {invoice_number} not found")
            return

        # Open email dialog
        from view.invoice_email_dialog import InvoiceEmailDialog
        email_dialog = InvoiceEmailDialog(self, self.db_path, invoice_id, self.load_history)

    def show_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under the cursor
        item = self.history_tree.identify_row(event.y)
        if item:
            self.history_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
        else:
            self.context_menu.unpost()
