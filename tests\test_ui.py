import unittest
import tkinter as tk
import os
import sqlite3
from unittest.mock import MagicMock, patch

# Import modules to test
from model.database import Database
from view.dashboard import AdminDashboard, ClientDashboard


class TestAdminDashboard(unittest.TestCase):
    def setUp(self):
        # Create a root window for testing
        self.root = tk.Tk()
        
        # Create a mock database
        self.db = Database()
        self.db.users_db = "test_users.db"
        self.db.init_users_db()
        
        # Add test users
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        
        import hashlib
        hashed_password = hashlib.sha256("testpass".encode()).hexdigest()
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser1", hashed_password, "Client")
        )
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser2", hashed_password, "Admin")
        )
        
        conn.commit()
        conn.close()
        
        # Create a mock logout callback
        self.logout_callback = MagicMock()
        
        # Create the dashboard
        self.dashboard = AdminDashboard(self.root, "admin", self.logout_callback, self.db)
    
    def tearDown(self):
        # Destroy the root window
        self.root.destroy()
        
        # Clean up test database
        if os.path.exists("test_users.db"):
            os.remove("test_users.db")
    
    def test_dashboard_initialization(self):
        """Test that the dashboard initializes correctly"""
        # Check that the dashboard has the correct attributes
        self.assertEqual(self.dashboard.username, "admin")
        self.assertEqual(self.dashboard.logout_callback, self.logout_callback)
        self.assertEqual(self.dashboard.db, self.db)
        
        # Check that the user tree has items
        self.assertGreater(len(self.dashboard.user_tree.get_children()), 0)
    
    @patch('tkinter.messagebox.askyesno')
    @patch('tkinter.messagebox.showinfo')
    def test_delete_user(self, mock_showinfo, mock_askyesno):
        """Test the delete_user method"""
        # Set up the mock to return True for the confirmation dialog
        mock_askyesno.return_value = True
        
        # Get the first user in the tree
        users = self.dashboard.user_tree.get_children()
        self.dashboard.user_tree.selection_set(users[0])
        
        # Get the user ID and username
        user_id = self.dashboard.user_tree.item(users[0], "values")[0]
        username = self.dashboard.user_tree.item(users[0], "values")[1]
        
        # Call the delete_user method
        self.dashboard.delete_user()
        
        # Check that the confirmation dialog was shown
        mock_askyesno.assert_called_once()
        
        # Check that the success message was shown
        mock_showinfo.assert_called_once_with("Success", f"User '{username}' deleted successfully")
        
        # Check that the user was deleted from the database
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users WHERE id = ?", (user_id,))
        count = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(count, 0)
    
    @patch('tkinter.messagebox.askyesno')
    def test_delete_user_cancelled(self, mock_askyesno):
        """Test cancelling the delete_user operation"""
        # Set up the mock to return False for the confirmation dialog
        mock_askyesno.return_value = False
        
        # Get the first user in the tree
        users = self.dashboard.user_tree.get_children()
        self.dashboard.user_tree.selection_set(users[0])
        
        # Get the user ID
        user_id = self.dashboard.user_tree.item(users[0], "values")[0]
        
        # Call the delete_user method
        self.dashboard.delete_user()
        
        # Check that the confirmation dialog was shown
        mock_askyesno.assert_called_once()
        
        # Check that the user still exists in the database
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users WHERE id = ?", (user_id,))
        count = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(count, 1)


class TestClientDashboard(unittest.TestCase):
    def setUp(self):
        # Create a root window for testing
        self.root = tk.Tk()
        
        # Create mock callbacks
        self.logout_callback = MagicMock()
        self.create_company_callback = MagicMock()
        self.open_company_callback = MagicMock()
        
        # Create the dashboard
        self.dashboard = ClientDashboard(
            self.root, 
            "testclient", 
            self.logout_callback, 
            self.create_company_callback, 
            self.open_company_callback
        )
    
    def tearDown(self):
        # Destroy the root window
        self.root.destroy()
    
    def test_dashboard_initialization(self):
        """Test that the dashboard initializes correctly"""
        # Check that the dashboard has the correct attributes
        self.assertEqual(self.dashboard.username, "testclient")
        self.assertEqual(self.dashboard.logout_callback, self.logout_callback)
        self.assertEqual(self.dashboard.create_company_callback, self.create_company_callback)
        self.assertEqual(self.dashboard.open_company_callback, self.open_company_callback)
    
    def test_create_company(self):
        """Test the create_company method"""
        # Call the create_company method
        self.dashboard.create_company()
        
        # Check that the create_company_callback was called
        self.create_company_callback.assert_called_once()
    
    @patch('tkinter.messagebox.showerror')
    def test_open_company_no_selection(self, mock_showerror):
        """Test opening a company with no selection"""
        # Call the open_company method with no selection
        self.dashboard.open_company()
        
        # Check that an error message was shown
        mock_showerror.assert_called_once_with("Error", "Please select a company to open")
        
        # Check that the open_company_callback was not called
        self.open_company_callback.assert_not_called()


if __name__ == '__main__':
    unittest.main()
