#!/usr/bin/env python3
"""
Demo script for Phase 11.3 Split Transactions Enhancement
Demonstrates comprehensive split transaction functionality
"""

import os
import sqlite3
import tempfile
from datetime import datetime, date, timedelta

from model.split_transaction import SplitTransaction
from model.account import Account
from model.category import Category
from model.transaction import TransactionManager
from utils.database_migration import DatabaseMigration


def get_existing_company_or_create_temp():
    """Get an existing company database or create a temporary one for demo"""
    print("Looking for existing companies...")
    
    # Get all company databases (similar to client_frame.py logic)
    companies = []
    excluded_files = ['users.db', 'settings.db']
    db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]
    
    for db_file in db_files:
        try:
            # Extract company name from filename
            company_name = db_file.replace('.db', '').replace('_', ' ').title()
            companies.append({
                'name': company_name,
                'db_path': db_file
            })
        except Exception as e:
            print(f"Error loading company {db_file}: {e}")
    
    if companies:
        # Use the first existing company
        company = companies[0]
        print(f"Using existing company: {company['name']}")
        db_path = company['db_path']
        
        # Run database migration to ensure split transaction tables exist
        print("Running database migration...")
        DatabaseMigration.migrate_company_database(db_path)
        
        # Set up demo accounts and categories if needed
        account_id, category_ids = setup_demo_data(db_path)
        
        return db_path, account_id, category_ids, False
    else:
        print("No existing companies found. Creating temporary database...")
        
        # Create temporary database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        db_path = temp_db.name
        
        # Create basic database structure
        create_demo_database_structure(db_path)
        
        # Set up demo accounts and categories
        account_id, category_ids = setup_demo_data(db_path)
        
        return db_path, account_id, category_ids, True


def create_demo_database_structure(db_path):
    """Create basic database structure for demo"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create basic tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL,
                opening_balance REAL NOT NULL,
                current_balance REAL NOT NULL,
                description TEXT,
                created_date TEXT,
                classification TEXT,
                account_number TEXT,
                parent_id INTEGER,
                is_active INTEGER DEFAULT 1
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                category_id INTEGER,
                type TEXT NOT NULL,
                reconciled INTEGER DEFAULT 0,
                currency_code TEXT DEFAULT 'USD',
                exchange_rate REAL DEFAULT 1.0,
                base_amount REAL,
                import_hash TEXT,
                bank_reference TEXT,
                import_source TEXT,
                import_date TEXT,
                FOREIGN KEY (account_id) REFERENCES accounts(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
        """)
        
        conn.commit()
        conn.close()
        
        # Run database migration to add split transaction tables
        print("Running database migration...")
        DatabaseMigration.migrate_company_database(db_path)
        
    except Exception as e:
        print(f"Error creating database structure: {e}")
        raise


def setup_demo_data(db_path):
    """Set up demo accounts and categories"""
    try:
        account_model = Account(db_path)
        category_model = Category(db_path)
        
        # Check if demo account already exists
        accounts = account_model.get_all_accounts()
        demo_account = None
        for acc in accounts:
            if acc['name'] == 'Demo Checking Account':
                demo_account = acc
                break
        
        if not demo_account:
            # Create demo account
            account_id = account_model.create_account(
                name="Demo Checking Account",
                account_type="checking",
                currency="USD",
                opening_balance=5000.00,
                description="Demo account for split transaction testing"
            )
        else:
            account_id = demo_account['id']
        
        # Create demo categories if they don't exist
        categories = category_model.get_all_categories()
        category_names = [cat['name'] for cat in categories]
        
        category_ids = {}
        demo_categories = [
            ("Office Supplies", "expense"),
            ("Travel", "expense"),
            ("Meals & Entertainment", "expense"),
            ("Professional Services", "expense"),
            ("Utilities", "expense"),
            ("Marketing", "expense"),
            ("Training", "expense"),
            ("Software", "expense")
        ]
        
        for cat_name, cat_type in demo_categories:
            if cat_name not in category_names:
                cat_id = category_model.create_category(cat_name, cat_type)
                category_ids[cat_name] = cat_id
            else:
                # Find existing category ID
                for cat in categories:
                    if cat['name'] == cat_name:
                        category_ids[cat_name] = cat['id']
                        break
        
        print(f"Set up demo account (ID: {account_id}) and {len(category_ids)} categories")
        return account_id, category_ids
        
    except Exception as e:
        print(f"Error setting up demo data: {e}")
        raise


def create_sample_transactions(db_path, account_id, category_ids):
    """Create sample transactions that can be split"""
    try:
        transaction_manager = TransactionManager(db_path)
        
        # Sample transactions that would benefit from splitting
        sample_transactions = [
            {
                'date': (date.today() - timedelta(days=5)).strftime('%Y-%m-%d'),
                'description': 'Office Supply Store Purchase',
                'amount': 245.67,
                'category_id': category_ids.get('Office Supplies'),
                'type': 'expense'
            },
            {
                'date': (date.today() - timedelta(days=3)).strftime('%Y-%m-%d'),
                'description': 'Business Trip Expenses',
                'amount': 1250.00,
                'category_id': category_ids.get('Travel'),
                'type': 'expense'
            },
            {
                'date': (date.today() - timedelta(days=2)).strftime('%Y-%m-%d'),
                'description': 'Conference and Training',
                'amount': 850.00,
                'category_id': category_ids.get('Training'),
                'type': 'expense'
            },
            {
                'date': (date.today() - timedelta(days=1)).strftime('%Y-%m-%d'),
                'description': 'Monthly Service Bills',
                'amount': 425.50,
                'category_id': category_ids.get('Utilities'),
                'type': 'expense'
            }
        ]
        
        transaction_ids = []
        for trans in sample_transactions:
            trans_id = transaction_manager.add_transaction(
                date=trans['date'],
                amount=trans['amount'],
                account_id=account_id,
                description=trans['description'],
                category_id=trans['category_id'],
                type_name=trans['type']
            )
            transaction_ids.append(trans_id)
        
        print(f"Created {len(transaction_ids)} sample transactions")
        return transaction_ids
        
    except Exception as e:
        print(f"Error creating sample transactions: {e}")
        raise


def demo_split_transaction_creation(db_path, account_id, category_ids):
    """Demonstrate creating split transactions"""
    print("\n" + "="*60)
    print("DEMO: Creating Split Transactions")
    print("="*60)
    
    try:
        split_model = SplitTransaction(db_path)
        
        # Demo 1: Split office supply purchase
        print("\n1. Splitting Office Supply Purchase ($245.67)")
        print("   Original: Office supplies")
        print("   Split into: Office Supplies ($150.00) + Software ($95.67)")
        
        split_lines_1 = [
            {
                'category_id': category_ids.get('Office Supplies'),
                'amount': 150.00,
                'description': 'Printer paper, pens, folders'
            },
            {
                'category_id': category_ids.get('Software'),
                'amount': 95.67,
                'description': 'Microsoft Office license'
            }
        ]
        
        split_id_1 = split_model.create_split_transaction(
            parent_transaction_id=None,
            date=(date.today() - timedelta(days=5)).strftime('%Y-%m-%d'),
            description='Office Supply Store Purchase - Split',
            total_amount=245.67,
            account_id=account_id,
            transaction_type='expense',
            split_lines=split_lines_1,
            created_by='demo_user',
            notes='Split for better expense tracking'
        )
        
        print(f"   ✓ Created split transaction ID: {split_id_1}")
        
        # Demo 2: Split business trip expenses
        print("\n2. Splitting Business Trip Expenses ($1,250.00)")
        print("   Split into: Travel ($800.00) + Meals ($300.00) + Professional Services ($150.00)")
        
        split_lines_2 = [
            {
                'category_id': category_ids.get('Travel'),
                'amount': 800.00,
                'description': 'Flight and hotel'
            },
            {
                'category_id': category_ids.get('Meals & Entertainment'),
                'amount': 300.00,
                'description': 'Client dinners and meals'
            },
            {
                'category_id': category_ids.get('Professional Services'),
                'amount': 150.00,
                'description': 'Conference registration'
            }
        ]
        
        split_id_2 = split_model.create_split_transaction(
            parent_transaction_id=None,
            date=(date.today() - timedelta(days=3)).strftime('%Y-%m-%d'),
            description='Business Trip Expenses - Split',
            total_amount=1250.00,
            account_id=account_id,
            transaction_type='expense',
            split_lines=split_lines_2,
            created_by='demo_user',
            notes='Detailed breakdown of business trip costs'
        )
        
        print(f"   ✓ Created split transaction ID: {split_id_2}")
        
        # Demo 3: Split monthly bills
        print("\n3. Splitting Monthly Service Bills ($425.50)")
        print("   Split into: Utilities ($275.50) + Software ($100.00) + Marketing ($50.00)")
        
        split_lines_3 = [
            {
                'category_id': category_ids.get('Utilities'),
                'amount': 275.50,
                'description': 'Electricity and internet'
            },
            {
                'category_id': category_ids.get('Software'),
                'amount': 100.00,
                'description': 'Cloud storage and tools'
            },
            {
                'category_id': category_ids.get('Marketing'),
                'amount': 50.00,
                'description': 'Social media advertising'
            }
        ]
        
        split_id_3 = split_model.create_split_transaction(
            parent_transaction_id=None,
            date=(date.today() - timedelta(days=1)).strftime('%Y-%m-%d'),
            description='Monthly Service Bills - Split',
            total_amount=425.50,
            account_id=account_id,
            transaction_type='expense',
            split_lines=split_lines_3,
            created_by='demo_user',
            notes='Monthly recurring service expenses'
        )
        
        print(f"   ✓ Created split transaction ID: {split_id_3}")
        
        return [split_id_1, split_id_2, split_id_3]
        
    except Exception as e:
        print(f"   ✗ Error creating split transactions: {e}")
        return []


def demo_split_transaction_management(db_path, split_ids):
    """Demonstrate split transaction management operations"""
    print("\n" + "="*60)
    print("DEMO: Split Transaction Management")
    print("="*60)
    
    try:
        split_model = SplitTransaction(db_path)
        
        # Demo 1: Retrieve and display split transactions
        print("\n1. Retrieving All Split Transactions")
        all_splits = split_model.get_all_split_transactions()
        
        print(f"   Found {len(all_splits)} split transactions:")
        for split in all_splits:
            print(f"   - ID {split['id']}: {split['description']} (${split['total_amount']:.2f})")
            print(f"     Date: {split['date']}, Lines: {split.get('line_count', 0)}")
        
        # Demo 2: Get detailed split transaction
        if split_ids:
            print(f"\n2. Detailed View of Split Transaction {split_ids[0]}")
            detailed_split = split_model.get_split_transaction(split_ids[0])
            
            if detailed_split:
                print(f"   Transaction: {detailed_split['description']}")
                print(f"   Total Amount: ${detailed_split['total_amount']:.2f}")
                print(f"   Account: {detailed_split.get('account_name', 'N/A')}")
                print(f"   Created: {detailed_split.get('created_date', 'N/A')}")
                print(f"   Notes: {detailed_split.get('notes', 'N/A')}")
                
                print("   Split Lines:")
                for i, line in enumerate(detailed_split.get('lines', []), 1):
                    print(f"     {i}. {line.get('category_name', 'N/A')}: ${line['amount']:.2f} ({line.get('percentage', 0):.1f}%)")
                    if line.get('description'):
                        print(f"        Description: {line['description']}")
        
        # Demo 3: Update split transaction
        if split_ids:
            print(f"\n3. Updating Split Transaction {split_ids[0]}")
            
            # Get current split
            current_split = split_model.get_split_transaction(split_ids[0])
            if current_split:
                # Update with new split lines
                updated_lines = [
                    {
                        'category_id': line['category_id'],
                        'amount': line['amount'] + 10.00,  # Add $10 to each line
                        'description': line.get('description', '') + ' (Updated)'
                    }
                    for line in current_split['lines']
                ]
                
                new_total = sum(line['amount'] for line in updated_lines)
                
                success = split_model.update_split_transaction(
                    split_ids[0],
                    total_amount=new_total,
                    split_lines=updated_lines,
                    notes='Updated during demo'
                )
                
                if success:
                    print(f"   ✓ Updated split transaction with new total: ${new_total:.2f}")
                else:
                    print("   ✗ Failed to update split transaction")
        
        # Demo 4: Filter split transactions by date
        print("\n4. Filtering Split Transactions by Date")
        recent_splits = split_model.get_all_split_transactions(
            start_date=(date.today() - timedelta(days=7)).strftime('%Y-%m-%d'),
            end_date=date.today().strftime('%Y-%m-%d')
        )
        
        print(f"   Found {len(recent_splits)} split transactions in the last 7 days")
        
    except Exception as e:
        print(f"   ✗ Error in split transaction management: {e}")


def demo_split_transaction_reporting(db_path):
    """Demonstrate split transaction reporting and analysis"""
    print("\n" + "="*60)
    print("DEMO: Split Transaction Reporting & Analysis")
    print("="*60)
    
    try:
        split_model = SplitTransaction(db_path)
        
        # Get all split transactions
        all_splits = split_model.get_all_split_transactions()
        
        if not all_splits:
            print("   No split transactions found for reporting")
            return
        
        # Calculate summary statistics
        total_splits = len(all_splits)
        total_amount = sum(split['total_amount'] for split in all_splits)
        avg_amount = total_amount / total_splits if total_splits > 0 else 0
        
        print(f"\n📊 Split Transaction Summary:")
        print(f"   Total Split Transactions: {total_splits}")
        print(f"   Total Amount: ${total_amount:.2f}")
        print(f"   Average Amount: ${avg_amount:.2f}")
        
        # Category breakdown
        print(f"\n📈 Category Breakdown:")
        category_totals = {}
        
        for split in all_splits:
            detailed_split = split_model.get_split_transaction(split['id'])
            if detailed_split:
                for line in detailed_split.get('lines', []):
                    category_name = line.get('category_name', 'Unknown')
                    category_totals[category_name] = category_totals.get(category_name, 0) + line['amount']
        
        for category, amount in sorted(category_totals.items(), key=lambda x: x[1], reverse=True):
            percentage = (amount / total_amount * 100) if total_amount > 0 else 0
            print(f"   {category}: ${amount:.2f} ({percentage:.1f}%)")
        
        # Monthly breakdown
        print(f"\n📅 Monthly Breakdown:")
        monthly_totals = {}
        
        for split in all_splits:
            try:
                split_date = datetime.strptime(split['date'], '%Y-%m-%d')
                month_key = split_date.strftime('%Y-%m')
                monthly_totals[month_key] = monthly_totals.get(month_key, 0) + split['total_amount']
            except:
                pass
        
        for month, amount in sorted(monthly_totals.items()):
            print(f"   {month}: ${amount:.2f}")
        
    except Exception as e:
        print(f"   ✗ Error in split transaction reporting: {e}")


def cleanup_demo_files(db_path, is_temporary):
    """Clean up demo files"""
    if is_temporary and db_path and os.path.exists(db_path):
        try:
            os.remove(db_path)
            print(f"Removed temporary database: {db_path}")
        except Exception as e:
            print(f"Error removing temporary database: {e}")


def main():
    """Main demo function"""
    print("="*60)
    print("PHASE 11.3 SPLIT TRANSACTIONS ENHANCEMENT DEMO")
    print("="*60)
    print("This demo showcases the comprehensive split transaction functionality:")
    print("- Create split transactions with multiple categories")
    print("- Manage and update existing split transactions")
    print("- Advanced split transaction reporting and analysis")
    print("- Professional split transaction management interface")
    print()
    print("NOTE: This demo will use an existing company if available,")
    print("or create a temporary database for demonstration purposes.")
    
    try:
        # Get existing company or create temporary database
        db_path, account_id, category_ids, is_temporary = get_existing_company_or_create_temp()
        
        # Create sample transactions
        transaction_ids = create_sample_transactions(db_path, account_id, category_ids)
        
        # Demo split transaction creation
        split_ids = demo_split_transaction_creation(db_path, account_id, category_ids)
        
        # Demo split transaction management
        demo_split_transaction_management(db_path, split_ids)
        
        # Demo reporting and analysis
        demo_split_transaction_reporting(db_path)
        
        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("Key features demonstrated:")
        print("- Split transaction creation with multiple categories")
        print("- Percentage-based split calculations")
        print("- Split transaction management and updates")
        print("- Comprehensive reporting and analysis")
        print("- Professional split transaction interface")
        
        # Handle cleanup based on whether we used temporary or existing database
        if is_temporary:
            keep_files = input("\nKeep temporary demo files for inspection? (y/n): ").lower().strip()
            if keep_files != 'y':
                cleanup_demo_files(db_path, is_temporary)
            else:
                print(f"\nDemo files kept:")
                print(f"  Database: {db_path}")
        else:
            print(f"\nDemo used existing company database: {db_path}")
            print("Demo split transactions have been added to your existing company.")
        
    except Exception as e:
        print(f"\nDemo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
