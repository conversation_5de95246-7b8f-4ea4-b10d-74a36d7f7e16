import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from datetime import datetime, date

from model.split_transaction import SplitTransaction
from model.account import Account
from model.category import Category
from view.components.date_picker import DatePicker


class SplitTransactionDialog:
    """
    Dialog for creating and editing split transactions
    """
    
    def __init__(self, parent, db_path, transaction_data=None, split_transaction_id=None, callback=None):
        self.parent = parent
        self.db_path = db_path
        self.transaction_data = transaction_data  # Original transaction data for splitting
        self.split_transaction_id = split_transaction_id  # For editing existing splits
        self.callback = callback
        
        # Initialize models
        self.split_transaction_model = SplitTransaction(db_path)
        self.account_model = Account(db_path)
        self.category_model = Category(db_path)
        
        # Data storage
        self.split_lines = []
        self.accounts = {}
        self.categories = {}
        
        # UI variables
        self.description_var = tk.StringVar()
        self.total_amount_var = tk.DoubleVar()
        self.account_var = tk.StringVar()
        self.type_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # Create dialog
        self.create_dialog()
        self.load_data()
        
        # Load existing split if editing
        if split_transaction_id:
            self.load_split_transaction()
        elif transaction_data:
            self.load_from_transaction()
    
    def create_dialog(self):
        """Create the split transaction dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Split Transaction")
        self.dialog.geometry("800x700")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))
        
        title_text = "Edit Split Transaction" if self.split_transaction_id else "Create Split Transaction"
        title_label = ttk.Label(header_frame, text=title_text, font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")
        
        close_btn = ttk.Button(header_frame, text="✕", command=self.close_dialog, 
                              bootstyle=DANGER, width=3)
        close_btn.pack(side="right")
        
        # Create notebook for sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, pady=(0, 10))
        
        # Transaction details tab
        details_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(details_frame, text="Transaction Details")
        
        # Split lines tab
        lines_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(lines_frame, text="Split Lines")
        
        # Create forms
        self.create_details_form(details_frame)
        self.create_lines_form(lines_frame)
        
        # Bottom buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))
        
        save_btn = ttk.Button(button_frame, text="Save Split Transaction", 
                             command=self.save_split_transaction, bootstyle=SUCCESS)
        save_btn.pack(side="right", padx=(10, 0))
        
        cancel_btn = ttk.Button(button_frame, text="Cancel", 
                               command=self.close_dialog, bootstyle=SECONDARY)
        cancel_btn.pack(side="right")
        
        # Validation button
        validate_btn = ttk.Button(button_frame, text="Validate Split", 
                                 command=self.validate_split, bootstyle=INFO)
        validate_btn.pack(side="left")
    
    def create_details_form(self, parent):
        """Create the transaction details form"""
        # Date field
        date_frame = ttk.Frame(parent)
        date_frame.pack(fill="x", pady=5)
        
        ttk.Label(date_frame, text="Date:", width=15).pack(side="left")
        self.date_picker = DatePicker(date_frame, initial_date=date.today())
        self.date_picker.pack(side="left", fill="x", expand=True)
        
        # Description field
        desc_frame = ttk.Frame(parent)
        desc_frame.pack(fill="x", pady=5)
        
        ttk.Label(desc_frame, text="Description:", width=15).pack(side="left")
        desc_entry = ttk.Entry(desc_frame, textvariable=self.description_var)
        desc_entry.pack(side="left", fill="x", expand=True)
        
        # Total amount field
        amount_frame = ttk.Frame(parent)
        amount_frame.pack(fill="x", pady=5)
        
        ttk.Label(amount_frame, text="Total Amount:", width=15).pack(side="left")
        amount_entry = ttk.Entry(amount_frame, textvariable=self.total_amount_var)
        amount_entry.pack(side="left", fill="x", expand=True)
        
        # Account selection
        account_frame = ttk.Frame(parent)
        account_frame.pack(fill="x", pady=5)
        
        ttk.Label(account_frame, text="Account:", width=15).pack(side="left")
        self.account_combo = ttk.Combobox(account_frame, textvariable=self.account_var, 
                                         state="readonly")
        self.account_combo.pack(side="left", fill="x", expand=True)
        
        # Transaction type
        type_frame = ttk.Frame(parent)
        type_frame.pack(fill="x", pady=5)
        
        ttk.Label(type_frame, text="Type:", width=15).pack(side="left")
        
        type_radio_frame = ttk.Frame(type_frame)
        type_radio_frame.pack(side="left", fill="x", expand=True)
        
        ttk.Radiobutton(type_radio_frame, text="Income", variable=self.type_var, 
                       value="income").pack(side="left", padx=(0, 20))
        ttk.Radiobutton(type_radio_frame, text="Expense", variable=self.type_var, 
                       value="expense").pack(side="left")
        
        # Notes field
        notes_frame = ttk.Frame(parent)
        notes_frame.pack(fill="x", pady=5)
        
        ttk.Label(notes_frame, text="Notes:", width=15).pack(side="left")
        notes_entry = ttk.Entry(notes_frame, textvariable=self.notes_var)
        notes_entry.pack(side="left", fill="x", expand=True)
    
    def create_lines_form(self, parent):
        """Create the split lines form"""
        # Instructions
        instructions = ttk.Label(parent, 
                                text="Add split lines below. The total of all lines must equal the transaction amount.",
                                font=("Segoe UI", 10))
        instructions.pack(fill="x", pady=(0, 10))
        
        # Split lines list
        lines_frame = ttk.LabelFrame(parent, text="Split Lines", padding=10)
        lines_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Treeview for split lines
        columns = ("category", "account", "amount", "percentage", "description")
        self.lines_tree = ttk.Treeview(lines_frame, columns=columns, show="headings", height=8)
        
        # Configure columns
        self.lines_tree.heading("category", text="Category")
        self.lines_tree.heading("account", text="Account")
        self.lines_tree.heading("amount", text="Amount")
        self.lines_tree.heading("percentage", text="Percentage")
        self.lines_tree.heading("description", text="Description")
        
        self.lines_tree.column("category", width=150)
        self.lines_tree.column("account", width=150)
        self.lines_tree.column("amount", width=100)
        self.lines_tree.column("percentage", width=80)
        self.lines_tree.column("description", width=200)
        
        self.lines_tree.pack(fill="both", expand=True, pady=(0, 10))
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(lines_frame, orient="vertical", command=self.lines_tree.yview)
        scrollbar.pack(side="right", fill="y")
        self.lines_tree.configure(yscrollcommand=scrollbar.set)
        
        # Buttons for managing lines
        line_buttons_frame = ttk.Frame(lines_frame)
        line_buttons_frame.pack(fill="x")
        
        add_line_btn = ttk.Button(line_buttons_frame, text="Add Line", 
                                 command=self.add_split_line, bootstyle=SUCCESS)
        add_line_btn.pack(side="left", padx=(0, 10))
        
        edit_line_btn = ttk.Button(line_buttons_frame, text="Edit Line", 
                                  command=self.edit_split_line, bootstyle=PRIMARY)
        edit_line_btn.pack(side="left", padx=(0, 10))
        
        delete_line_btn = ttk.Button(line_buttons_frame, text="Delete Line", 
                                    command=self.delete_split_line, bootstyle=DANGER)
        delete_line_btn.pack(side="left")
        
        # Summary frame
        summary_frame = ttk.LabelFrame(parent, text="Summary", padding=10)
        summary_frame.pack(fill="x")
        
        self.summary_var = tk.StringVar()
        summary_label = ttk.Label(summary_frame, textvariable=self.summary_var, 
                                 font=("Segoe UI", 10, "bold"))
        summary_label.pack()
        
        self.update_summary()
    
    def load_data(self):
        """Load accounts and categories"""
        # Load accounts
        accounts = self.account_model.get_all_accounts()
        self.accounts = {f"{acc['name']} ({acc['type']})": acc['id'] for acc in accounts}
        self.account_combo.configure(values=list(self.accounts.keys()))
        
        # Load categories
        categories = self.category_model.get_all_categories()
        self.categories = {f"{cat['name']} ({cat['type']})": cat['id'] for cat in categories}
    
    def load_from_transaction(self):
        """Load data from an existing transaction for splitting"""
        if not self.transaction_data:
            return
        
        # Set transaction details
        try:
            trans_date = datetime.strptime(self.transaction_data['date'], '%Y-%m-%d').date()
            self.date_picker.set_date(trans_date)
        except:
            pass
        
        self.description_var.set(self.transaction_data.get('description', ''))
        self.total_amount_var.set(self.transaction_data.get('amount', 0.0))
        self.type_var.set(self.transaction_data.get('type', 'expense'))
        
        # Set account
        account_id = self.transaction_data.get('account_id')
        for account_name, acc_id in self.accounts.items():
            if acc_id == account_id:
                self.account_var.set(account_name)
                break
    
    def load_split_transaction(self):
        """Load an existing split transaction for editing"""
        try:
            split_data = self.split_transaction_model.get_split_transaction(self.split_transaction_id)
            if not split_data:
                messagebox.showerror("Error", "Split transaction not found")
                return
            
            # Load transaction details
            try:
                trans_date = datetime.strptime(split_data['date'], '%Y-%m-%d').date()
                self.date_picker.set_date(trans_date)
            except:
                pass
            
            self.description_var.set(split_data.get('description', ''))
            self.total_amount_var.set(split_data.get('total_amount', 0.0))
            self.type_var.set(split_data.get('type', 'expense'))
            self.notes_var.set(split_data.get('notes', ''))
            
            # Set account
            account_id = split_data.get('account_id')
            for account_name, acc_id in self.accounts.items():
                if acc_id == account_id:
                    self.account_var.set(account_name)
                    break
            
            # Load split lines
            self.split_lines = split_data.get('lines', [])
            self.refresh_lines_display()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load split transaction: {str(e)}")
    
    def add_split_line(self):
        """Add a new split line"""
        self.show_line_dialog()
    
    def edit_split_line(self):
        """Edit the selected split line"""
        selection = self.lines_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a line to edit")
            return
        
        line_index = self.lines_tree.index(selection[0])
        if 0 <= line_index < len(self.split_lines):
            self.show_line_dialog(self.split_lines[line_index], line_index)
    
    def delete_split_line(self):
        """Delete the selected split line"""
        selection = self.lines_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a line to delete")
            return
        
        line_index = self.lines_tree.index(selection[0])
        if 0 <= line_index < len(self.split_lines):
            if messagebox.askyesno("Confirm", "Are you sure you want to delete this split line?"):
                del self.split_lines[line_index]
                self.refresh_lines_display()
                self.update_summary()
    
    def show_line_dialog(self, line_data=None, line_index=None):
        """Show dialog for adding/editing a split line"""
        line_dialog = tk.Toplevel(self.dialog)
        line_dialog.title("Split Line")
        line_dialog.geometry("400x300")
        line_dialog.transient(self.dialog)
        line_dialog.grab_set()
        
        frame = ttk.Frame(line_dialog, padding=20)
        frame.pack(fill="both", expand=True)
        
        # Category selection
        ttk.Label(frame, text="Category:").grid(row=0, column=0, sticky="w", pady=5)
        category_var = tk.StringVar()
        category_combo = ttk.Combobox(frame, textvariable=category_var, 
                                     values=list(self.categories.keys()), state="readonly")
        category_combo.grid(row=0, column=1, sticky="ew", pady=5)
        
        # Account selection (optional)
        ttk.Label(frame, text="Account (optional):").grid(row=1, column=0, sticky="w", pady=5)
        line_account_var = tk.StringVar()
        line_account_combo = ttk.Combobox(frame, textvariable=line_account_var, 
                                         values=list(self.accounts.keys()), state="readonly")
        line_account_combo.grid(row=1, column=1, sticky="ew", pady=5)
        
        # Amount
        ttk.Label(frame, text="Amount:").grid(row=2, column=0, sticky="w", pady=5)
        amount_var = tk.DoubleVar()
        amount_entry = ttk.Entry(frame, textvariable=amount_var)
        amount_entry.grid(row=2, column=1, sticky="ew", pady=5)
        
        # Description
        ttk.Label(frame, text="Description:").grid(row=3, column=0, sticky="w", pady=5)
        desc_var = tk.StringVar()
        desc_entry = ttk.Entry(frame, textvariable=desc_var)
        desc_entry.grid(row=3, column=1, sticky="ew", pady=5)
        
        # Load existing data if editing
        if line_data:
            # Set category
            for cat_name, cat_id in self.categories.items():
                if cat_id == line_data.get('category_id'):
                    category_var.set(cat_name)
                    break
            
            # Set account
            for acc_name, acc_id in self.accounts.items():
                if acc_id == line_data.get('account_id'):
                    line_account_var.set(acc_name)
                    break
            
            amount_var.set(line_data.get('amount', 0.0))
            desc_var.set(line_data.get('description', ''))
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        def save_line():
            try:
                category_id = self.categories.get(category_var.get())
                account_id = self.accounts.get(line_account_var.get()) if line_account_var.get() else None
                amount = amount_var.get()
                description = desc_var.get()
                
                if not category_id:
                    messagebox.showerror("Error", "Please select a category")
                    return
                
                if amount <= 0:
                    messagebox.showerror("Error", "Amount must be greater than zero")
                    return
                
                line = {
                    'category_id': category_id,
                    'account_id': account_id,
                    'amount': amount,
                    'description': description
                }
                
                if line_index is not None:
                    # Update existing line
                    self.split_lines[line_index] = line
                else:
                    # Add new line
                    self.split_lines.append(line)
                
                self.refresh_lines_display()
                self.update_summary()
                line_dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save line: {str(e)}")
        
        save_btn = ttk.Button(button_frame, text="Save", command=save_line, bootstyle=SUCCESS)
        save_btn.pack(side="left", padx=(0, 10))
        
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=line_dialog.destroy, bootstyle=SECONDARY)
        cancel_btn.pack(side="left")
        
        frame.columnconfigure(1, weight=1)
    
    def refresh_lines_display(self):
        """Refresh the split lines display"""
        # Clear existing items
        for item in self.lines_tree.get_children():
            self.lines_tree.delete(item)
        
        # Add split lines
        total_amount = self.total_amount_var.get()
        for line in self.split_lines:
            # Get category name
            category_name = ""
            for cat_name, cat_id in self.categories.items():
                if cat_id == line.get('category_id'):
                    category_name = cat_name
                    break
            
            # Get account name
            account_name = ""
            if line.get('account_id'):
                for acc_name, acc_id in self.accounts.items():
                    if acc_id == line.get('account_id'):
                        account_name = acc_name
                        break
            
            # Calculate percentage
            percentage = (line['amount'] / total_amount * 100) if total_amount > 0 else 0
            
            self.lines_tree.insert("", "end", values=(
                category_name,
                account_name,
                f"${line['amount']:.2f}",
                f"{percentage:.1f}%",
                line.get('description', '')
            ))
    
    def update_summary(self):
        """Update the summary display"""
        total_amount = self.total_amount_var.get()
        split_total = sum(line['amount'] for line in self.split_lines)
        difference = total_amount - split_total
        
        summary_text = f"Transaction Total: ${total_amount:.2f} | "
        summary_text += f"Split Total: ${split_total:.2f} | "
        summary_text += f"Difference: ${difference:.2f}"
        
        if abs(difference) < 0.01:
            summary_text += " ✓"
        else:
            summary_text += " ⚠"
        
        self.summary_var.set(summary_text)
    
    def validate_split(self):
        """Validate the split transaction"""
        try:
            total_amount = self.total_amount_var.get()
            split_total = sum(line['amount'] for line in self.split_lines)
            
            if not self.split_lines:
                messagebox.showwarning("Validation", "No split lines defined")
                return
            
            if abs(total_amount - split_total) > 0.01:
                messagebox.showwarning("Validation", 
                                     f"Split total (${split_total:.2f}) does not match transaction total (${total_amount:.2f})")
                return
            
            if not self.description_var.get().strip():
                messagebox.showwarning("Validation", "Description is required")
                return
            
            if not self.account_var.get():
                messagebox.showwarning("Validation", "Account is required")
                return
            
            messagebox.showinfo("Validation", "Split transaction is valid!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Validation failed: {str(e)}")
    
    def save_split_transaction(self):
        """Save the split transaction"""
        try:
            # Validate
            if not self.description_var.get().strip():
                messagebox.showerror("Error", "Description is required")
                return
            
            if not self.account_var.get():
                messagebox.showerror("Error", "Account is required")
                return
            
            if not self.split_lines:
                messagebox.showerror("Error", "At least one split line is required")
                return
            
            total_amount = self.total_amount_var.get()
            split_total = sum(line['amount'] for line in self.split_lines)
            
            if abs(total_amount - split_total) > 0.01:
                messagebox.showerror("Error", 
                                   f"Split total (${split_total:.2f}) does not match transaction total (${total_amount:.2f})")
                return
            
            # Get data
            date_str = self.date_picker.get_date().strftime('%Y-%m-%d')
            description = self.description_var.get().strip()
            account_id = self.accounts.get(self.account_var.get())
            transaction_type = self.type_var.get()
            notes = self.notes_var.get().strip()
            
            parent_transaction_id = None
            if self.transaction_data:
                parent_transaction_id = self.transaction_data.get('id')
            
            if self.split_transaction_id:
                # Update existing split
                success = self.split_transaction_model.update_split_transaction(
                    self.split_transaction_id, date_str, description, total_amount, 
                    self.split_lines, notes
                )
                if success:
                    messagebox.showinfo("Success", "Split transaction updated successfully!")
            else:
                # Create new split
                split_id = self.split_transaction_model.create_split_transaction(
                    parent_transaction_id, date_str, description, total_amount,
                    account_id, transaction_type, self.split_lines, "current_user", notes
                )
                if split_id:
                    messagebox.showinfo("Success", "Split transaction created successfully!")
            
            # Call callback and close
            if self.callback:
                self.callback()
            
            self.close_dialog()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save split transaction: {str(e)}")
    
    def close_dialog(self):
        """Close the dialog"""
        self.dialog.destroy()
