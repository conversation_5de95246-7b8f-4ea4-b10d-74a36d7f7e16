import os
import sqlite3
import sys
import unittest
from datetime import datetime

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from model.client import Client
from model.invoice import Invoice


class TestClient(unittest.TestCase):
    """Test cases for the Client model"""

    def setUp(self):
        """Set up test environment"""
        # Create a test database
        self.db_path = "tests/test_client.db"

        # Remove the test database if it exists
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

        # Create the client model
        self.client_model = Client(self.db_path)

        # Create a test client
        self.client_id = self.client_model.add_client({
            'name': "Test Client",
            'company_name': "Test Company",
            'email': "<EMAIL>",
            'phone': "************",
            'address': "123 Test St",
            'notes': "Test notes"
        })

    def tearDown(self):
        """Clean up after tests"""
        # Close database connections
        self.client_model = None

        # Remove the test database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def test_add_client(self):
        """Test adding a new client"""
        # Get the current number of clients
        clients_before = self.client_model.get_all_clients()

        # Add a new client
        new_client_data = {
            'name': "Another Client",
            'company_name': "Another Company",
            'email': "<EMAIL>",
            'phone': "************",
            'address': "456 Test Ave",
            'notes': "More test notes"
        }

        result = self.client_model.add_client(new_client_data)

        # Verify the client was added
        self.assertIsNotNone(result)

        # Get all clients after adding
        clients_after = self.client_model.get_all_clients()

        # Verify a new client was added
        self.assertEqual(len(clients_after), len(clients_before) + 1)

        # Find the new client
        new_client = None
        for client in clients_after:
            if client["name"] == "Another Client":
                new_client = client
                break

        # Verify the client was found
        self.assertIsNotNone(new_client)

        # Verify the client data
        self.assertEqual(new_client["name"], "Another Client")
        self.assertEqual(new_client["company_name"], "Another Company")
        self.assertEqual(new_client["email"], "<EMAIL>")
        self.assertEqual(new_client["phone"], "************")
        self.assertEqual(new_client["address"], "456 Test Ave")
        self.assertEqual(new_client["notes"], "More test notes")

    def test_get_client(self):
        """Test retrieving a client"""
        # Get the client
        client = self.client_model.get_client(self.client_id)

        # Verify the client data
        self.assertEqual(client["id"], self.client_id)
        self.assertEqual(client["name"], "Test Client")
        self.assertEqual(client["company_name"], "Test Company")
        self.assertEqual(client["email"], "<EMAIL>")
        self.assertEqual(client["phone"], "************")
        self.assertEqual(client["address"], "123 Test St")
        self.assertEqual(client["notes"], "Test notes")

    def test_get_all_clients(self):
        """Test retrieving all clients"""
        # Add another client
        self.client_model.add_client({
            'name': "Another Client",
            'company_name': "Another Company",
            'email': "<EMAIL>",
            'phone': "************",
            'address': "456 Test Ave",
            'notes': "More test notes"
        })

        # Get all clients
        clients = self.client_model.get_all_clients()

        # Verify we have 2 clients
        self.assertEqual(len(clients), 2)

        # Verify the client names
        client_names = [client["name"] for client in clients]
        self.assertIn("Test Client", client_names)
        self.assertIn("Another Client", client_names)

    def test_update_client(self):
        """Test updating a client"""
        # Update the client
        result = self.client_model.update_client(
            client_id=self.client_id,
            client_data={
                'name': "Updated Client",
                'company_name': "Updated Company",
                'email': "<EMAIL>",
                'phone': "************",
                'address': "789 Update Rd",
                'notes': "Updated notes"
            }
        )

        # Verify the update was successful
        self.assertTrue(result)

        # Get the updated client
        client = self.client_model.get_client(self.client_id)

        # Verify the client data was updated
        self.assertEqual(client["name"], "Updated Client")
        self.assertEqual(client["company_name"], "Updated Company")
        self.assertEqual(client["email"], "<EMAIL>")
        self.assertEqual(client["phone"], "************")
        self.assertEqual(client["address"], "789 Update Rd")
        self.assertEqual(client["notes"], "Updated notes")

    def test_delete_client(self):
        """Test deleting a client"""
        # Create the invoices table to avoid the "no such table" error
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER,
            invoice_number TEXT,
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
        ''')
        conn.commit()
        conn.close()

        # Delete the client
        result = self.client_model.delete_client(self.client_id)

        # Verify the deletion was successful
        self.assertTrue(result)

        # Try to get the deleted client
        client = self.client_model.get_client(self.client_id)

        # Verify the client is gone
        self.assertIsNone(client)

    def test_delete_client_with_invoices(self):
        """Test deleting a client with invoices"""
        # Create the invoices table to avoid the "no such table" error
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER,
            invoice_number TEXT,
            issue_date TEXT,
            due_date TEXT,
            status TEXT,
            subtotal REAL,
            tax_amount REAL,
            total_amount REAL,
            notes TEXT,
            terms TEXT,
            created_at TIMESTAMP,
            updated_at TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
        ''')

        # Insert a test invoice directly
        conn.execute('''
        INSERT INTO invoices (
            client_id, invoice_number, issue_date, due_date, status,
            subtotal, tax_amount, total_amount, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', [
            self.client_id, "INV-001", "2025-05-15", "2025-06-15", "draft",
            100.0, 0.0, 100.0, "Test invoice", datetime.now(), datetime.now()
        ])
        conn.commit()
        conn.close()

        # Try to delete the client
        with self.assertRaises(ValueError):
            self.client_model.delete_client(self.client_id)

        # Verify the client still exists
        client = self.client_model.get_client(self.client_id)
        self.assertIsNotNone(client)

    def test_search_clients(self):
        """Test searching for clients"""
        # Add more clients for testing search
        self.client_model.add_client({
            'name': "John Doe",
            'company_name': "ABC Corp",
            'email': "<EMAIL>",
            'phone': "************",
            'address': "123 Main St",
            'notes': "Important client"
        })

        self.client_model.add_client({
            'name': "Jane Smith",
            'company_name': "XYZ Inc",
            'email': "<EMAIL>",
            'phone': "************",
            'address': "456 Oak Ave",
            'notes': "New client"
        })

        # Search by name
        results = self.client_model.search_clients("John")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["name"], "John Doe")

        # Search by company name
        results = self.client_model.search_clients("XYZ")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["company_name"], "XYZ Inc")

        # Search by email
        results = self.client_model.search_clients("test@")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["email"], "<EMAIL>")

        # Search by phone
        results = self.client_model.search_clients("111-222")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["phone"], "************")

        # Search with no results
        results = self.client_model.search_clients("nonexistent")
        self.assertEqual(len(results), 0)

        # Search with multiple results
        results = self.client_model.search_clients("example.com")
        self.assertEqual(len(results), 3)  # All 3 clients have example.com in their email


if __name__ == '__main__':
    unittest.main()
