import os
import sqlite3
import unittest
from unittest.mock import MagicMock, patch

# Import the Category model
from model.category import Category


class TestCategory(unittest.TestCase):
    def setUp(self):
        """Set up a test database"""
        # Create a test database in memory
        self.db_path = ":memory:"
        self.conn = sqlite3.connect(self.db_path)
        self.cursor = self.conn.cursor()
        
        # Create the categories table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Insert some test data
        self.cursor.execute('''
        INSERT INTO categories (name, type) VALUES
        ('Salary', 'income'),
        ('Food', 'expense'),
        ('Rent', 'expense'),
        ('Utilities', 'expense'),
        ('Entertainment', 'expense')
        ''')
        
        self.conn.commit()
        
        # Create a Category instance
        self.category = Category(self.db_path)
    
    def tearDown(self):
        """Clean up after the test"""
        self.conn.close()
    
    def test_get_all_categories(self):
        """Test getting all categories"""
        categories = self.category.get_all_categories()
        
        # Check that we got the right number of categories
        self.assertEqual(len(categories), 5)
        
        # Check that the categories have the expected fields
        for category in categories:
            self.assertIn('id', category)
            self.assertIn('name', category)
            self.assertIn('type', category)
    
    def test_get_category_by_id(self):
        """Test getting a category by ID"""
        # Get a category by ID
        category = self.category.get_category_by_id(1)
        
        # Check that we got the right category
        self.assertEqual(category['id'], 1)
        self.assertEqual(category['name'], 'Salary')
        self.assertEqual(category['type'], 'income')
    
    def test_get_category_by_name(self):
        """Test getting a category by name"""
        # Get a category by name
        category = self.category.get_category_by_name('Food')
        
        # Check that we got the right category
        self.assertEqual(category['id'], 2)
        self.assertEqual(category['name'], 'Food')
        self.assertEqual(category['type'], 'expense')
    
    def test_add_category(self):
        """Test adding a new category"""
        # Add a new category
        category_id = self.category.add_category('Bonus', 'income')
        
        # Check that the category was added
        self.assertIsNotNone(category_id)
        
        # Get the category and check its values
        category = self.category.get_category_by_id(category_id)
        self.assertEqual(category['name'], 'Bonus')
        self.assertEqual(category['type'], 'income')
    
    def test_update_category(self):
        """Test updating a category"""
        # Update a category
        result = self.category.update_category(1, {'name': 'Monthly Salary', 'type': 'income'})
        
        # Check that the update was successful
        self.assertTrue(result)
        
        # Get the category and check its values
        category = self.category.get_category_by_id(1)
        self.assertEqual(category['name'], 'Monthly Salary')
        self.assertEqual(category['type'], 'income')
    
    def test_delete_category(self):
        """Test deleting a category"""
        # Delete a category
        result = self.category.delete_category(5)
        
        # Check that the deletion was successful
        self.assertTrue(result)
        
        # Try to get the deleted category
        category = self.category.get_category_by_id(5)
        self.assertIsNone(category)
    
    def test_get_categories_by_type(self):
        """Test getting categories by type"""
        # Get income categories
        income_categories = self.category.get_categories_by_type('income')
        
        # Check that we got the right number of categories
        self.assertEqual(len(income_categories), 1)
        
        # Check that the category is the right one
        self.assertEqual(income_categories[0]['name'], 'Salary')
        
        # Get expense categories
        expense_categories = self.category.get_categories_by_type('expense')
        
        # Check that we got the right number of categories
        self.assertEqual(len(expense_categories), 4)


if __name__ == '__main__':
    unittest.main()
