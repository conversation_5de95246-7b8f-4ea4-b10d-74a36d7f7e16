import tkinter as tk
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import Scrolled<PERSON>rame

from utils.email_service import EmailService


class EmailSettingsFrame(ttk.Frame):
    """Frame for configuring email settings"""

    def __init__(self, parent, db_path, close_callback):
        """Initialize the email settings frame"""
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.close_callback = close_callback
        self.email_service = EmailService(db_path)

        # Create variables
        self.smtp_server_var = tk.StringVar()
        self.smtp_port_var = tk.StringVar()
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.from_email_var = tk.StringVar()
        self.from_name_var = tk.StringVar()
        self.signature_var = tk.StringVar()
        self.error_var = tk.StringVar()

        # Load existing settings
        self.load_settings()

        # Create widgets
        self.create_widgets()

    def load_settings(self):
        """Load existing email settings"""
        settings = self.email_service.get_email_settings()
        if settings:
            self.smtp_server_var.set(settings.get('smtp_server', ''))
            self.smtp_port_var.set(str(settings.get('smtp_port', '')))
            self.username_var.set(settings.get('username', ''))
            self.password_var.set(settings.get('password', ''))
            self.from_email_var.set(settings.get('from_email', ''))
            self.from_name_var.set(settings.get('from_name', ''))
            self.signature_var.set(settings.get('signature', ''))

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Email Settings", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 20), anchor="w")

        # Error message
        error_label = ttk.Label(main_frame, textvariable=self.error_var, foreground="red")
        error_label.pack(fill="x", pady=(0, 10))

        # Create scrolled frame for settings
        settings_frame = ScrolledFrame(main_frame)
        settings_frame.pack(fill="both", expand=True)
        
        # SMTP Settings
        smtp_frame = ttk.LabelFrame(settings_frame, text="SMTP Server Settings")
        smtp_frame.pack(fill="x", pady=10)

        # SMTP Server
        server_frame = ttk.Frame(smtp_frame)
        server_frame.pack(fill="x", padx=10, pady=5)
        
        server_label = ttk.Label(server_frame, text="SMTP Server:", width=15, anchor="w")
        server_label.pack(side="left")
        
        server_entry = ttk.Entry(server_frame, textvariable=self.smtp_server_var)
        server_entry.pack(side="left", fill="x", expand=True)

        # SMTP Port
        port_frame = ttk.Frame(smtp_frame)
        port_frame.pack(fill="x", padx=10, pady=5)
        
        port_label = ttk.Label(port_frame, text="SMTP Port:", width=15, anchor="w")
        port_label.pack(side="left")
        
        port_entry = ttk.Entry(port_frame, textvariable=self.smtp_port_var)
        port_entry.pack(side="left", fill="x", expand=True)

        # Authentication
        auth_frame = ttk.LabelFrame(settings_frame, text="Authentication")
        auth_frame.pack(fill="x", pady=10)

        # Username
        username_frame = ttk.Frame(auth_frame)
        username_frame.pack(fill="x", padx=10, pady=5)
        
        username_label = ttk.Label(username_frame, text="Username:", width=15, anchor="w")
        username_label.pack(side="left")
        
        username_entry = ttk.Entry(username_frame, textvariable=self.username_var)
        username_entry.pack(side="left", fill="x", expand=True)

        # Password
        password_frame = ttk.Frame(auth_frame)
        password_frame.pack(fill="x", padx=10, pady=5)
        
        password_label = ttk.Label(password_frame, text="Password:", width=15, anchor="w")
        password_label.pack(side="left")
        
        password_entry = ttk.Entry(password_frame, textvariable=self.password_var, show="*")
        password_entry.pack(side="left", fill="x", expand=True)

        # Sender Information
        sender_frame = ttk.LabelFrame(settings_frame, text="Sender Information")
        sender_frame.pack(fill="x", pady=10)

        # From Email
        from_email_frame = ttk.Frame(sender_frame)
        from_email_frame.pack(fill="x", padx=10, pady=5)
        
        from_email_label = ttk.Label(from_email_frame, text="From Email:", width=15, anchor="w")
        from_email_label.pack(side="left")
        
        from_email_entry = ttk.Entry(from_email_frame, textvariable=self.from_email_var)
        from_email_entry.pack(side="left", fill="x", expand=True)

        # From Name
        from_name_frame = ttk.Frame(sender_frame)
        from_name_frame.pack(fill="x", padx=10, pady=5)
        
        from_name_label = ttk.Label(from_name_frame, text="From Name:", width=15, anchor="w")
        from_name_label.pack(side="left")
        
        from_name_entry = ttk.Entry(from_name_frame, textvariable=self.from_name_var)
        from_name_entry.pack(side="left", fill="x", expand=True)

        # Signature
        signature_frame = ttk.LabelFrame(settings_frame, text="Email Signature")
        signature_frame.pack(fill="x", pady=10)

        signature_text = tk.Text(signature_frame, height=5, width=50)
        signature_text.pack(fill="x", padx=10, pady=10)
        
        # Set initial value
        signature_text.insert("1.0", self.signature_var.get())
        
        # Connect text widget to StringVar
        def update_signature_var(event=None):
            self.signature_var.set(signature_text.get("1.0", "end-1c"))
        
        signature_text.bind("<KeyRelease>", update_signature_var)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=20)

        test_button = ttk.Button(button_frame, text="Test Connection", 
                               command=self.test_connection, bootstyle=INFO)
        test_button.pack(side="left", padx=(0, 10))

        save_button = ttk.Button(button_frame, text="Save Settings", 
                               command=self.save_settings, bootstyle=SUCCESS)
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel", 
                                 command=self.close_callback, bootstyle=SECONDARY)
        cancel_button.pack(side="left")

    def save_settings(self):
        """Save email settings"""
        # Validate settings
        if not self.validate_settings():
            return

        # Prepare settings data
        settings = {
            'smtp_server': self.smtp_server_var.get(),
            'smtp_port': int(self.smtp_port_var.get()),
            'username': self.username_var.get(),
            'password': self.password_var.get(),
            'from_email': self.from_email_var.get(),
            'from_name': self.from_name_var.get(),
            'signature': self.signature_var.get()
        }

        # Save settings
        try:
            self.email_service.save_email_settings(settings)
            messagebox.showinfo("Success", "Email settings saved successfully.")
            self.close_callback()
        except Exception as e:
            self.error_var.set(f"Error saving settings: {str(e)}")

    def validate_settings(self):
        """Validate email settings"""
        # Clear error message
        self.error_var.set("")

        # Check required fields
        if not self.smtp_server_var.get():
            self.error_var.set("SMTP Server is required")
            return False

        if not self.smtp_port_var.get():
            self.error_var.set("SMTP Port is required")
            return False
        
        # Validate port is a number
        try:
            port = int(self.smtp_port_var.get())
            if port < 1 or port > 65535:
                self.error_var.set("SMTP Port must be between 1 and 65535")
                return False
        except ValueError:
            self.error_var.set("SMTP Port must be a number")
            return False

        if not self.username_var.get():
            self.error_var.set("Username is required")
            return False

        if not self.password_var.get():
            self.error_var.set("Password is required")
            return False

        if not self.from_email_var.get():
            self.error_var.set("From Email is required")
            return False

        # Validate email format
        if '@' not in self.from_email_var.get() or '.' not in self.from_email_var.get():
            self.error_var.set("From Email must be a valid email address")
            return False

        return True

    def test_connection(self):
        """Test SMTP connection"""
        # Validate settings
        if not self.validate_settings():
            return

        # Prepare settings data
        settings = {
            'smtp_server': self.smtp_server_var.get(),
            'smtp_port': int(self.smtp_port_var.get()),
            'username': self.username_var.get(),
            'password': self.password_var.get(),
            'from_email': self.from_email_var.get(),
            'from_name': self.from_name_var.get(),
            'signature': self.signature_var.get()
        }

        # Test connection
        try:
            import smtplib
            server = smtplib.SMTP(settings['smtp_server'], settings['smtp_port'])
            server.starttls()
            server.login(settings['username'], settings['password'])
            server.quit()
            messagebox.showinfo("Success", "Connection test successful!")
        except Exception as e:
            self.error_var.set(f"Connection test failed: {str(e)}")
