import os
import sqlite3
import tkinter as tk
from datetime import datetime
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
from ttkbootstrap.tooltip import ToolTip


class AdminFrame(ttk.Frame):
    """Admin dashboard as a frame instead of a standalone window"""

    def __init__(self, parent, username, logout_callback, db=None):
        super().__init__(parent)
        self.parent = parent
        self.username = username
        self.logout_callback = logout_callback
        self.title = "Admin Dashboard"

        # Get database instance
        if db is None:
            from model.database import Database
            self.db = Database()
        else:
            self.db = db

        self.create_widgets()
        self.load_users()
        self.load_companies()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with welcome message and logout button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        # Create a dashboard-style header
        header_content = ttk.Frame(header_frame)
        header_content.pack(fill="x")

        # Welcome message with icon
        welcome_frame = ttk.Frame(header_content)
        welcome_frame.pack(side="left")

        welcome_label = ttk.Label(welcome_frame, text=f"Welcome, {self.username}",
                                 font=("Segoe UI", 16, "bold"))
        welcome_label.pack(side="left")

        role_label = ttk.Label(welcome_frame, text="Administrator",
                              font=("Segoe UI", 10),
                              bootstyle="secondary")
        role_label.pack(side="left", padx=(5, 0), pady=(5, 0))

        # Right side controls
        controls_frame = ttk.Frame(header_content)
        controls_frame.pack(side="right")

        # Date display
        current_date = datetime.now().strftime("%B %d, %Y")
        date_label = ttk.Label(controls_frame, text=current_date,
                              font=("Segoe UI", 10), bootstyle="secondary")
        date_label.pack(side="left", padx=(0, 15))

        # Settings button
        settings_button = ttk.Button(controls_frame, text="⚙️ Settings",
                                    command=self.quick_access_settings,
                                    bootstyle=INFO)
        settings_button.pack(side="right", padx=(0, 10))
        ToolTip(settings_button, text="Quick access to Application Settings")

        # Logout button with icon
        logout_button = ttk.Button(controls_frame, text="Logout",
                                  command=self.logout_callback,
                                  bootstyle=DANGER)
        logout_button.pack(side="right")
        ToolTip(logout_button, text="Logout from the application")

        # Notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)

        # User Management Tab
        user_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(user_tab, text="User Management")

        # Top control panel for user management
        control_panel = ttk.Frame(user_tab)
        control_panel.pack(fill="x", pady=(0, 10))

        # Left side - action buttons
        button_frame = ttk.Frame(control_panel)
        button_frame.pack(side="left", fill="y")

        # Add user button with icon
        add_user_button = ttk.Button(button_frame, text="Add User",
                                    command=self.add_user,
                                    bootstyle=SUCCESS)
        add_user_button.pack(side="left", padx=(0, 5))
        ToolTip(add_user_button, text="Create a new user account")

        # Edit user button with icon
        edit_user_button = ttk.Button(button_frame, text="Edit User",
                                     command=self.edit_user,
                                     bootstyle=INFO)
        edit_user_button.pack(side="left", padx=5)
        ToolTip(edit_user_button, text="Edit the selected user")

        # Delete user button with icon
        delete_user_button = ttk.Button(button_frame, text="Delete User",
                                       command=self.delete_user,
                                       bootstyle=DANGER)
        delete_user_button.pack(side="left", padx=5)
        ToolTip(delete_user_button, text="Delete the selected user")

        # Right side - search and filter
        search_frame = ttk.Frame(control_panel)
        search_frame.pack(side="right", fill="y")

        # Search entry
        self.user_search_var = tk.StringVar()
        self.user_search_var.trace_add("write", lambda *_: self.filter_users())

        search_entry = ttk.Entry(search_frame, textvariable=self.user_search_var,
                                width=20, bootstyle="secondary")
        search_entry.pack(side="right")
        ToolTip(search_entry, text="Search users by username or role")

        search_label = ttk.Label(search_frame, text="Search: ")
        search_label.pack(side="right", padx=(0, 5))

        # Users frame
        users_frame = ttk.Labelframe(user_tab, text="Users", padding=10)
        users_frame.pack(fill="both", expand=True)

        # User treeview with scrollbars
        columns = ("id", "username", "role", "status")

        # Create a frame for the treeview and scrollbar
        tree_frame = ttk.Frame(users_frame)
        tree_frame.pack(fill="both", expand=True)

        # Create the treeview with modern styling
        self.user_tree = ttk.Treeview(tree_frame, columns=columns, show="headings",
                                     bootstyle="primary")

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.user_tree.yview)
        vsb.pack(side="right", fill="y")
        self.user_tree.configure(yscrollcommand=vsb.set)

        # Add horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.user_tree.xview)
        hsb.pack(side="bottom", fill="x")
        self.user_tree.configure(xscrollcommand=hsb.set)

        # Define headings
        self.user_tree.heading("id", text="ID", anchor="center")
        self.user_tree.heading("username", text="Username", anchor="center")
        self.user_tree.heading("role", text="Role", anchor="center")
        self.user_tree.heading("status", text="Status", anchor="center")

        # Define columns
        self.user_tree.column("id", width=50, anchor="center")
        self.user_tree.column("username", width=200)
        self.user_tree.column("role", width=100, anchor="center")
        self.user_tree.column("status", width=100, anchor="center")

        self.user_tree.pack(side="left", fill="both", expand=True)

        # Add right-click context menu for users
        self.user_context_menu = tk.Menu(self, tearoff=0)
        self.user_context_menu.add_command(label="Edit User", command=self.edit_user)
        self.user_context_menu.add_command(label="Delete User", command=self.delete_user)

        # Bind right-click to show context menu
        self.user_tree.bind("<Button-3>", self.show_user_context_menu)

        # Double-click to edit
        self.user_tree.bind("<Double-1>", lambda _: self.edit_user())

        # Application Settings Tab
        settings_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(settings_tab, text="Application Settings")
        self.create_settings_tab(settings_tab)

        # Multi-Currency Tab
        currency_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(currency_tab, text="Multi-Currency")
        self.create_currency_tab(currency_tab)

        # Companies Tab
        companies_tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(companies_tab, text="Companies")

        # Top control panel for company management
        control_panel = ttk.Frame(companies_tab)
        control_panel.pack(fill="x", pady=(0, 10))

        # Left side - action buttons
        button_frame = ttk.Frame(control_panel)
        button_frame.pack(side="left", fill="y")

        # Add company button with icon
        add_company_button = ttk.Button(button_frame, text="Add Company",
                                       command=self.create_company,
                                       bootstyle=SUCCESS)
        add_company_button.pack(side="left", padx=(0, 5))
        ToolTip(add_company_button, text="Create a new company")

        # View company button with icon
        view_company_button = ttk.Button(button_frame, text="View Company",
                                        command=self.view_company_details,
                                        bootstyle=INFO)
        view_company_button.pack(side="left", padx=5)
        ToolTip(view_company_button, text="Open the selected company")

        # Right side - search and filter
        search_frame = ttk.Frame(control_panel)
        search_frame.pack(side="right", fill="y")

        # Search entry
        self.company_search_var = tk.StringVar()
        self.company_search_var.trace_add("write", lambda *_: self.filter_companies())

        search_entry = ttk.Entry(search_frame, textvariable=self.company_search_var,
                                width=20, bootstyle="secondary")
        search_entry.pack(side="right")
        ToolTip(search_entry, text="Search companies by name")

        search_label = ttk.Label(search_frame, text="Search: ")
        search_label.pack(side="right", padx=(0, 5))

        # Companies frame with modern styling
        companies_frame = ttk.Labelframe(companies_tab, text="Companies", padding=10)
        companies_frame.pack(fill="both", expand=True)

        # Company treeview with scrollbars
        columns = ("name", "created_date", "owner", "transactions", "balance", "status")

        # Create a frame for the treeview and scrollbar
        tree_frame = ttk.Frame(companies_frame)
        tree_frame.pack(fill="both", expand=True)

        # Create the treeview with modern styling
        self.company_tree = ttk.Treeview(tree_frame, columns=columns, show="headings",
                                        bootstyle="primary")

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.company_tree.yview)
        vsb.pack(side="right", fill="y")
        self.company_tree.configure(yscrollcommand=vsb.set)

        # Add horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.company_tree.xview)
        hsb.pack(side="bottom", fill="x")
        self.company_tree.configure(xscrollcommand=hsb.set)

        # Define headings with centered text
        self.company_tree.heading("name", text="Company Name", anchor="center")
        self.company_tree.heading("created_date", text="Created Date", anchor="center")
        self.company_tree.heading("owner", text="Owner", anchor="center")
        self.company_tree.heading("transactions", text="Transactions", anchor="center")
        self.company_tree.heading("balance", text="Balance", anchor="center")
        self.company_tree.heading("status", text="Status", anchor="center")

        # Define columns
        self.company_tree.column("name", width=200)
        self.company_tree.column("created_date", width=120, anchor="center")
        self.company_tree.column("owner", width=120, anchor="center")
        self.company_tree.column("transactions", width=100, anchor="center")
        self.company_tree.column("balance", width=100, anchor="center")
        self.company_tree.column("status", width=80, anchor="center")

        self.company_tree.pack(side="left", fill="both", expand=True)

        # Add right-click context menu for companies
        self.company_context_menu = tk.Menu(self, tearoff=0)
        self.company_context_menu.add_command(label="View Company", command=self.view_company_details)

        # Bind right-click to show context menu
        self.company_tree.bind("<Button-3>", self.show_company_context_menu)

        # Double-click to view company
        self.company_tree.bind("<Double-1>", lambda _: self.view_company_details())

    def create_settings_tab(self, parent):
        """Create the application settings tab"""
        # Header
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(
            header_frame,
            text="Application Settings",
            font=("Segoe UI", 14, "bold")
        )
        title_label.pack(side="left")

        # Info label
        info_label = ttk.Label(
            header_frame,
            text="Configure application-wide settings and preferences (Admin Only)",
            font=("Segoe UI", 10),
            bootstyle="secondary"
        )
        info_label.pack(side="left", padx=(10, 0))

        # Admin access indicator
        admin_indicator = ttk.Label(
            header_frame,
            text="🔒 Admin Access",
            font=("Segoe UI", 9, "bold"),
            bootstyle="warning"
        )
        admin_indicator.pack(side="right")

        # Settings content frame
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill="both", expand=True)

        # Import the ApplicationSettingsFrame
        try:
            from view.application_settings_frame import \
                ApplicationSettingsFrame

            # Create the settings frame
            self.settings_frame = ApplicationSettingsFrame(content_frame, self.close_settings)
            self.settings_frame.pack(fill="both", expand=True)

        except ImportError as e:
            # Fallback if the settings frame is not available
            error_frame = ttk.LabelFrame(content_frame, text="Settings Not Available", padding=20)
            error_frame.pack(fill="both", expand=True)

            error_label = ttk.Label(
                error_frame,
                text=f"Application Settings module not found.\nError: {str(e)}",
                font=("Segoe UI", 10),
                foreground="red"
            )
            error_label.pack()

            # Button to open settings in separate window
            open_settings_button = ttk.Button(
                error_frame,
                text="Open Settings in New Window",
                command=self.open_settings_window,
                bootstyle=PRIMARY
            )
            open_settings_button.pack(pady=10)

    def close_settings(self):
        """Handle settings close - just refresh the tab"""
        # Settings are embedded in the tab, so we don't need to close anything
        # Just show a status message
        messagebox.showinfo("Settings", "Settings have been updated successfully!")

    def open_settings_window(self):
        """Open settings in a separate window as fallback"""
        try:
            from view.application_settings_frame import \
                ApplicationSettingsFrame

            # Create settings window
            settings_window = tk.Toplevel(self)
            settings_window.title("Application Settings")
            settings_window.geometry("900x700")
            settings_window.transient(self)
            settings_window.grab_set()

            # Center the window
            settings_window.geometry("+%d+%d" % (
                self.winfo_rootx() + 50,
                self.winfo_rooty() + 50
            ))

            def close_settings_window():
                settings_window.destroy()
                messagebox.showinfo("Settings", "Settings window closed. Changes have been saved.")

            # Create settings frame
            settings_frame = ApplicationSettingsFrame(settings_window, close_settings_window)
            settings_frame.pack(fill="both", expand=True)

        except ImportError as e:
            messagebox.showerror("Error", f"Cannot open settings: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open settings window: {str(e)}")

    def create_currency_tab(self, parent):
        """Create the multi-currency management tab"""
        # Header
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(
            header_frame,
            text="Multi-Currency Management",
            font=("Segoe UI", 14, "bold")
        )
        title_label.pack(side="left")

        # Info label
        info_label = ttk.Label(
            header_frame,
            text="Manage currencies, exchange rates, and multi-currency transactions",
            font=("Segoe UI", 10),
            bootstyle="secondary"
        )
        info_label.pack(side="left", padx=(10, 0))

        # Currency management buttons
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill="x", pady=(0, 20))

        # Currency Management button
        currency_mgmt_button = ttk.Button(
            buttons_frame,
            text="💱 Currency Management",
            command=self.open_currency_management,
            bootstyle=PRIMARY,
            width=25
        )
        currency_mgmt_button.pack(side="left", padx=(0, 10))

        # Multi-Currency Transaction button
        mc_transaction_button = ttk.Button(
            buttons_frame,
            text="💰 Multi-Currency Transaction",
            command=self.open_mc_transaction,
            bootstyle=INFO,
            width=25
        )
        mc_transaction_button.pack(side="left", padx=(0, 10))

        # Multi-Currency Reports button
        mc_reports_button = ttk.Button(
            buttons_frame,
            text="📊 Multi-Currency Reports",
            command=self.open_mc_reports,
            bootstyle=SUCCESS,
            width=25
        )
        mc_reports_button.pack(side="left")

        # Currency overview
        overview_frame = ttk.LabelFrame(parent, text="Currency Overview", padding=15)
        overview_frame.pack(fill="both", expand=True)

        # Create a simple overview display
        self.create_currency_overview(overview_frame)

    def create_currency_overview(self, parent):
        """Create currency overview display"""
        try:
            # Import here to avoid circular imports
            from model.currency import Currency

            # Create a simple overview of currencies and recent rates
            overview_text = tk.Text(parent, height=15, wrap=tk.WORD, font=("Consolas", 10))
            overview_text.pack(fill="both", expand=True)

            # Try to get currency information
            try:
                # This is a placeholder - in a real implementation, you'd get actual data
                overview_content = """
Multi-Currency System Overview
==============================

Base Currency: USD (US Dollar)
Active Currencies: 15
Exchange Rates: Auto-updated daily

Recent Activity:
• EUR/USD: 1.0850 (Updated: Today)
• GBP/USD: 1.2650 (Updated: Today)
• JPY/USD: 0.0067 (Updated: Today)
• CAD/USD: 0.7420 (Updated: Today)

Features Available:
✓ Multi-currency accounts
✓ Real-time exchange rates
✓ Currency conversion
✓ Gains/losses tracking
✓ Multi-currency reporting
✓ Risk analysis

Quick Actions:
• Add new currencies
• Update exchange rates
• Create multi-currency transactions
• Generate currency reports
• Analyze currency exposure

For detailed currency management, use the buttons above.
                """.strip()

                overview_text.insert(1.0, overview_content)
                overview_text.configure(state="disabled")

            except Exception as e:
                overview_text.insert(1.0, f"Currency overview not available: {str(e)}")
                overview_text.configure(state="disabled")

        except ImportError:
            # Fallback if currency model is not available
            error_label = ttk.Label(
                parent,
                text="Multi-Currency module not available.\nPlease ensure all currency components are installed.",
                font=("Segoe UI", 10),
                foreground="red",
                justify="center"
            )
            error_label.pack(expand=True)

    def open_currency_management(self):
        """Open currency management window"""
        try:
            from view.currency_management_frame import CurrencyManagementFrame

            # Create currency management window
            currency_window = tk.Toplevel(self)
            currency_window.title("Currency Management")
            currency_window.geometry("1000x700")
            currency_window.transient(self)
            currency_window.grab_set()

            # Center the window
            currency_window.geometry("+%d+%d" % (
                self.winfo_rootx() + 50,
                self.winfo_rooty() + 50
            ))

            def close_currency_window():
                currency_window.destroy()
                # Refresh currency overview
                self.refresh_currency_overview()

            # Create currency management frame
            # Use a default database path - in real implementation, this would be dynamic
            db_path = "company.db"  # This should be the current company's database
            currency_frame = CurrencyManagementFrame(currency_window, db_path, close_currency_window)
            currency_frame.pack(fill="both", expand=True)

        except ImportError as e:
            messagebox.showerror("Error", f"Currency management not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open currency management: {str(e)}")

    def open_mc_transaction(self):
        """Open multi-currency transaction window"""
        try:
            from view.multi_currency_transaction_frame import \
                MultiCurrencyTransactionFrame

            # Create transaction window
            transaction_window = tk.Toplevel(self)
            transaction_window.title("Multi-Currency Transaction")
            transaction_window.geometry("800x600")
            transaction_window.transient(self)
            transaction_window.grab_set()

            # Center the window
            transaction_window.geometry("+%d+%d" % (
                self.winfo_rootx() + 100,
                self.winfo_rooty() + 100
            ))

            def close_transaction_window():
                transaction_window.destroy()

            # Create transaction frame
            db_path = "company.db"  # This should be the current company's database
            transaction_frame = MultiCurrencyTransactionFrame(transaction_window, db_path, close_transaction_window)
            transaction_frame.pack(fill="both", expand=True)

        except ImportError as e:
            messagebox.showerror("Error", f"Multi-currency transaction not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open multi-currency transaction: {str(e)}")

    def open_mc_reports(self):
        """Open multi-currency reports window"""
        try:
            from view.multi_currency_reports_frame import \
                MultiCurrencyReportsFrame

            # Create reports window
            reports_window = tk.Toplevel(self)
            reports_window.title("Multi-Currency Reports")
            reports_window.geometry("1200x800")
            reports_window.transient(self)
            reports_window.grab_set()

            # Center the window
            reports_window.geometry("+%d+%d" % (
                self.winfo_rootx() + 25,
                self.winfo_rooty() + 25
            ))

            def close_reports_window():
                reports_window.destroy()

            # Create reports frame
            db_path = "company.db"  # This should be the current company's database
            reports_frame = MultiCurrencyReportsFrame(reports_window, db_path, close_reports_window)
            reports_frame.pack(fill="both", expand=True)

        except ImportError as e:
            messagebox.showerror("Error", f"Multi-currency reports not available: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open multi-currency reports: {str(e)}")

    def refresh_currency_overview(self):
        """Refresh the currency overview display"""
        # This would refresh the overview with current data
        pass

    def quick_access_settings(self):
        """Quick access to settings - switch to settings tab"""
        # Switch to the Application Settings tab
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == "Application Settings":
                self.notebook.select(i)
                break

    def load_users(self):
        """Load users into the treeview"""
        # Clear existing items
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)

        # Get all users from database
        users = self.db.get_all_users()

        # Add users to treeview with status
        for user in users:
            # Set status as "Active" for all users (could be enhanced in the future)
            status = "Active"

            # Apply tag based on role for color coding
            tag = user["role"].lower()

            # Insert with appropriate tag
            self.user_tree.insert("", "end",
                                 values=(user["id"], user["username"], user["role"], status),
                                 tags=(tag,))

        # Configure tags for color coding
        self.user_tree.tag_configure("admin", background="#e6f2ff")  # Light blue for admins
        self.user_tree.tag_configure("client", background="#f9f9f9")  # Light gray for clients

    def filter_users(self):
        """Filter users based on search text"""
        search_text = self.user_search_var.get().lower()

        # Clear existing items
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)

        # Get all users from database
        users = self.db.get_all_users()

        # Filter and add users to treeview
        for user in users:
            # Check if search text is in username or role
            if (search_text in user["username"].lower() or
                search_text in user["role"].lower()):

                # Set status as "Active" for all users
                status = "Active"

                # Apply tag based on role for color coding
                tag = user["role"].lower()

                # Insert with appropriate tag
                self.user_tree.insert("", "end",
                                     values=(user["id"], user["username"], user["role"], status),
                                     tags=(tag,))

    def show_user_context_menu(self, event):
        """Show context menu for user management"""
        # Get the item under cursor
        item = self.user_tree.identify_row(event.y)
        if item:
            # Select the item
            self.user_tree.selection_set(item)
            # Show context menu
            self.user_context_menu.post(event.x_root, event.y_root)

    def add_user(self):
        """Show dialog to add a new user"""
        # Create a new toplevel window
        add_window = tk.Toplevel(self)
        add_window.title("Add User")
        add_window.geometry("450x400")
        add_window.transient(self)  # Set to be on top of the main window
        add_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(add_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Header
        header_label = ttk.Label(frame, text="Create New User",
                                font=("Segoe UI", 14, "bold"))
        header_label.pack(pady=(0, 15))

        # Username field
        username_frame = ttk.Frame(frame)
        username_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(username_frame, text="Username:", width=15).pack(side="left")
        username_var = tk.StringVar()
        username_entry = ttk.Entry(username_frame, textvariable=username_var, width=30)
        username_entry.pack(side="left", fill="x", expand=True)

        # Password field
        password_frame = ttk.Frame(frame)
        password_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(password_frame, text="Password:", width=15).pack(side="left")
        password_var = tk.StringVar()
        password_entry = ttk.Entry(password_frame, textvariable=password_var, show="*", width=30)
        password_entry.pack(side="left", fill="x", expand=True)

        # Confirm password field
        confirm_frame = ttk.Frame(frame)
        confirm_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(confirm_frame, text="Confirm Password:", width=15).pack(side="left")
        confirm_var = tk.StringVar()
        confirm_entry = ttk.Entry(confirm_frame, textvariable=confirm_var, show="*", width=30)
        confirm_entry.pack(side="left", fill="x", expand=True)

        # Role selection with modern radio buttons
        ttk.Label(frame, text="User Role:", font=("Segoe UI", 10, "bold")).pack(anchor="w", pady=(10, 5))

        role_var = tk.StringVar(value="Client")
        role_frame = ttk.Frame(frame)
        role_frame.pack(fill="x", pady=(0, 15))

        # Admin role with description
        admin_frame = ttk.Frame(role_frame)
        admin_frame.pack(fill="x", pady=(0, 5))

        admin_radio = ttk.Radiobutton(admin_frame, text="Administrator",
                                     variable=role_var, value="Admin",
                                     bootstyle="primary-toolbutton")
        admin_radio.pack(side="left")

        admin_desc = ttk.Label(admin_frame, text="Full access to all features",
                              bootstyle="secondary")
        admin_desc.pack(side="left", padx=(10, 0))

        # Client role with description
        client_frame = ttk.Frame(role_frame)
        client_frame.pack(fill="x")

        client_radio = ttk.Radiobutton(client_frame, text="Client",
                                      variable=role_var, value="Client",
                                      bootstyle="primary-toolbutton")
        client_radio.pack(side="left")

        client_desc = ttk.Label(client_frame, text="Limited access to company data",
                               bootstyle="secondary")
        client_desc.pack(side="left", padx=(10, 0))

        # Error message label
        error_var = tk.StringVar()
        error_label = ttk.Label(frame, textvariable=error_var, bootstyle="danger")
        error_label.pack(fill="x", pady=(10, 0))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x", pady=(15, 0))

        def save_user():
            # Validate inputs
            username = username_var.get().strip()
            password = password_var.get().strip()
            confirm = confirm_var.get().strip()
            role = role_var.get()

            # Clear previous error
            error_var.set("")

            # Validate inputs
            if not username:
                error_var.set("Username is required")
                username_entry.focus()
                return

            if not password:
                error_var.set("Password is required")
                password_entry.focus()
                return

            if password != confirm:
                error_var.set("Passwords do not match")
                confirm_entry.focus()
                return

            try:
                # Add user to database
                self.db.add_user(username, password, role)

                # Close window and refresh user list
                add_window.destroy()
                self.load_users()

                # Show success message
                Messagebox.show_info(f"User '{username}' added successfully", "Success")

            except Exception as e:
                error_var.set(f"Error: {str(e)}")

        save_button = ttk.Button(button_frame, text="Create User", command=save_user,
                                bootstyle=SUCCESS, width=15)
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel",
                                  command=add_window.destroy,
                                  bootstyle=SECONDARY, width=15)
        cancel_button.pack(side="left")

        # Focus the username entry
        username_entry.focus()

    def edit_user(self):
        """Edit the selected user"""
        # Get selected item
        selected = self.user_tree.selection()
        if not selected:
            Messagebox.show_error("Please select a user to edit", "No User Selected")
            return

        # Get user ID and details
        user_id = self.user_tree.item(selected[0], "values")[0]
        username = self.user_tree.item(selected[0], "values")[1]
        role = self.user_tree.item(selected[0], "values")[2]

        # Create a new toplevel window
        edit_window = tk.Toplevel(self)
        edit_window.title("Edit User")
        edit_window.geometry("450x380")
        edit_window.transient(self)  # Set to be on top of the main window
        edit_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(edit_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Header
        header_label = ttk.Label(frame, text=f"Edit User: {username}",
                                font=("Segoe UI", 14, "bold"))
        header_label.pack(pady=(0, 15))

        # Username field
        username_frame = ttk.Frame(frame)
        username_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(username_frame, text="Username:", width=15).pack(side="left")
        username_var = tk.StringVar(value=username)
        username_entry = ttk.Entry(username_frame, textvariable=username_var, width=30)
        username_entry.pack(side="left", fill="x", expand=True)

        # Password field (optional for editing)
        password_frame = ttk.Frame(frame)
        password_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(password_frame, text="New Password:", width=15).pack(side="left")
        password_var = tk.StringVar()
        password_entry = ttk.Entry(password_frame, textvariable=password_var, show="*", width=30)
        password_entry.pack(side="left", fill="x", expand=True)

        # Password hint
        password_hint = ttk.Label(frame, text="Leave password blank to keep current password",
                                 bootstyle="secondary", font=("Segoe UI", 9, "italic"))
        password_hint.pack(anchor="w", padx=(15, 0))

        # Role selection with modern radio buttons
        ttk.Label(frame, text="User Role:", font=("Segoe UI", 10, "bold")).pack(anchor="w", pady=(15, 5))

        role_var = tk.StringVar(value=role)
        role_frame = ttk.Frame(frame)
        role_frame.pack(fill="x", pady=(0, 15))

        # Admin role with description
        admin_frame = ttk.Frame(role_frame)
        admin_frame.pack(fill="x", pady=(0, 5))

        admin_radio = ttk.Radiobutton(admin_frame, text="Administrator",
                                     variable=role_var, value="Admin",
                                     bootstyle="primary-toolbutton")
        admin_radio.pack(side="left")

        admin_desc = ttk.Label(admin_frame, text="Full access to all features",
                              bootstyle="secondary")
        admin_desc.pack(side="left", padx=(10, 0))

        # Client role with description
        client_frame = ttk.Frame(role_frame)
        client_frame.pack(fill="x")

        client_radio = ttk.Radiobutton(client_frame, text="Client",
                                      variable=role_var, value="Client",
                                      bootstyle="primary-toolbutton")
        client_radio.pack(side="left")

        client_desc = ttk.Label(client_frame, text="Limited access to company data",
                               bootstyle="secondary")
        client_desc.pack(side="left", padx=(10, 0))

        # Error message label
        error_var = tk.StringVar()
        error_label = ttk.Label(frame, textvariable=error_var, bootstyle="danger")
        error_label.pack(fill="x", pady=(10, 0))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x", pady=(15, 0))

        def save_user():
            # Validate inputs
            new_username = username_var.get().strip()
            new_password = password_var.get().strip()
            new_role = role_var.get()

            # Clear previous error
            error_var.set("")

            # Validate inputs
            if not new_username:
                error_var.set("Username is required")
                username_entry.focus()
                return

            try:
                # Update user in database
                if new_password:
                    # If password provided, update it too
                    self.db.update_user(user_id, new_username, new_password, new_role)
                else:
                    # Otherwise just update username and role
                    self.db.update_user(user_id, new_username, None, new_role)

                # Close window and refresh user list
                edit_window.destroy()
                self.load_users()

                # Show success message
                Messagebox.show_info(f"User '{new_username}' updated successfully", "Success")

            except Exception as e:
                error_var.set(f"Error: {str(e)}")

        save_button = ttk.Button(button_frame, text="Save Changes", command=save_user,
                                bootstyle=SUCCESS, width=15)
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = ttk.Button(button_frame, text="Cancel",
                                  command=edit_window.destroy,
                                  bootstyle=SECONDARY, width=15)
        cancel_button.pack(side="left")

        # Focus the username entry
        username_entry.focus()

    def delete_user(self):
        """Delete the selected user"""
        # Get selected item
        selected = self.user_tree.selection()
        if not selected:
            Messagebox.show_error("Please select a user to delete", "No User Selected")
            return

        # Get user ID and username
        user_id = self.user_tree.item(selected[0], "values")[0]
        username = self.user_tree.item(selected[0], "values")[1]
        role = self.user_tree.item(selected[0], "values")[2]

        # Don't allow deleting the last admin
        if role == "Admin":
            # Check if this is the last admin
            users = self.db.get_all_users()
            admin_count = sum(1 for user in users if user["role"] == "Admin")

            if admin_count <= 1:
                Messagebox.show_error(
                    "Cannot delete the last administrator account.\n"
                    "At least one administrator must exist in the system.",
                    "Operation Not Allowed"
                )
                return

        # Confirm deletion with a more detailed dialog
        if not Messagebox.show_question(
            f"Are you sure you want to delete user '{username}'?\n\n"
            f"Role: {role}\n"
            f"User ID: {user_id}\n\n"
            "This action cannot be undone.",
            "Confirm User Deletion",
            buttons=["Yes:primary", "No:secondary"]
        ) == "Yes":
            return

        try:
            # Delete user from database
            self.db.delete_user(user_id)

            # Refresh user list
            self.load_users()

            # Show success message
            Messagebox.show_info(f"User '{username}' deleted successfully", "Success")

        except Exception as e:
            Messagebox.show_error(str(e), "Error")

    def load_companies(self):
        """Load companies into the treeview"""
        # Clear existing items
        for item in self.company_tree.get_children():
            self.company_tree.delete(item)

        # Get all company databases
        import os
        import sqlite3

        # Get all .db files in the current directory, excluding system databases
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        # Process each database file
        for db_file in db_files:
            try:
                # Extract company name from filename
                company_name = db_file.replace('.db', '').replace('_', ' ').title()

                # Connect to the database
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Get creation date from metadata
                cursor.execute("SELECT value FROM metadata WHERE key = 'created_date'")
                result = cursor.fetchone()
                created_date = result[0] if result else "Unknown"

                # Format the date for display
                if created_date != "Unknown":
                    try:
                        # Parse the date and reformat it
                        date_obj = datetime.strptime(created_date, "%Y-%m-%d %H:%M:%S")
                        created_date = date_obj.strftime("%b %d, %Y")
                    except:
                        pass

                # Get owner (placeholder - not stored in database yet)
                owner = "Admin"

                # Get transaction count
                cursor.execute("SELECT COUNT(*) FROM transactions")
                transaction_count = cursor.fetchone()[0]

                # Get total balance
                cursor.execute("SELECT SUM(current_balance) FROM accounts")
                balance = cursor.fetchone()[0] or 0.0

                # Determine status based on activity
                status = "Active"

                # Apply tag based on balance for color coding
                tag = "positive" if balance >= 0 else "negative"

                # Add to treeview with appropriate tag
                self.company_tree.insert("", "end",
                                        values=(company_name, created_date, owner,
                                               transaction_count, f"${balance:.2f}", status),
                                        tags=(tag,))

                conn.close()
            except Exception as e:
                print(f"Error loading company {db_file}: {e}")

        # Configure tags for color coding
        self.company_tree.tag_configure("positive", foreground="#006400")  # Dark green for positive balance
        self.company_tree.tag_configure("negative", foreground="#8B0000")  # Dark red for negative balance

    def filter_companies(self):
        """Filter companies based on search text"""
        search_text = self.company_search_var.get().lower()

        # Clear existing items
        for item in self.company_tree.get_children():
            self.company_tree.delete(item)

        # Get all company databases
        import os
        import sqlite3

        # Get all .db files in the current directory, excluding system databases
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        # Process each database file
        for db_file in db_files:
            try:
                # Extract company name from filename
                company_name = db_file.replace('.db', '').replace('_', ' ').title()

                # Skip if company name doesn't match search
                if search_text not in company_name.lower():
                    continue

                # Connect to the database
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Get creation date from metadata
                cursor.execute("SELECT value FROM metadata WHERE key = 'created_date'")
                result = cursor.fetchone()
                created_date = result[0] if result else "Unknown"

                # Format the date for display
                if created_date != "Unknown":
                    try:
                        # Parse the date and reformat it
                        date_obj = datetime.strptime(created_date, "%Y-%m-%d %H:%M:%S")
                        created_date = date_obj.strftime("%b %d, %Y")
                    except:
                        pass

                # Get owner (placeholder - not stored in database yet)
                owner = "Admin"

                # Get transaction count
                cursor.execute("SELECT COUNT(*) FROM transactions")
                transaction_count = cursor.fetchone()[0]

                # Get total balance
                cursor.execute("SELECT SUM(current_balance) FROM accounts")
                balance = cursor.fetchone()[0] or 0.0

                # Determine status based on activity
                status = "Active"

                # Apply tag based on balance for color coding
                tag = "positive" if balance >= 0 else "negative"

                # Add to treeview with appropriate tag
                self.company_tree.insert("", "end",
                                        values=(company_name, created_date, owner,
                                               transaction_count, f"${balance:.2f}", status),
                                        tags=(tag,))

                conn.close()
            except Exception as e:
                print(f"Error loading company {db_file}: {e}")

    def show_company_context_menu(self, event):
        """Show context menu for company management"""
        # Get the item under cursor
        item = self.company_tree.identify_row(event.y)
        if item:
            # Select the item
            self.company_tree.selection_set(item)
            # Show context menu
            self.company_context_menu.post(event.x_root, event.y_root)

    def create_company(self):
        """Show dialog to create a new company"""
        # Create a new toplevel window
        create_window = tk.Toplevel(self)
        create_window.title("Create New Company")
        create_window.geometry("450x300")
        create_window.transient(self)  # Set to be on top of the main window
        create_window.grab_set()  # Modal

        # Create a frame with padding
        frame = ttk.Frame(create_window, padding=20)
        frame.pack(fill="both", expand=True)

        # Header
        header_label = ttk.Label(frame, text="Create New Company",
                                font=("Segoe UI", 14, "bold"))
        header_label.pack(pady=(0, 15))

        # Company name field
        ttk.Label(frame, text="Company Name:").pack(anchor="w", pady=(0, 5))
        name_var = tk.StringVar()
        name_entry = ttk.Entry(frame, textvariable=name_var, width=40)
        name_entry.pack(fill="x", pady=(0, 15))

        # Description field (optional)
        ttk.Label(frame, text="Description (Optional):").pack(anchor="w", pady=(0, 5))
        description_var = tk.StringVar()
        description_entry = ttk.Entry(frame, textvariable=description_var, width=40)
        description_entry.pack(fill="x", pady=(0, 15))

        # Error message label
        error_var = tk.StringVar()
        error_label = ttk.Label(frame, textvariable=error_var, bootstyle="danger")
        error_label.pack(fill="x", pady=(0, 10))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x", pady=(10, 0))

        def save_company():
            # Validate inputs
            company_name = name_var.get().strip()

            if not company_name:
                error_var.set("Company name is required")
                return

            try:
                # Create company database
                self.db.init_company_db(company_name)

                # Close window and refresh company list
                create_window.destroy()
                self.load_companies()

                # Show success message
                messagebox.showinfo("Success", f"Company '{company_name}' created successfully")

            except Exception as e:
                error_var.set(f"Error: {str(e)}")

        ttk.Button(button_frame, text="Create", command=save_company,
                  bootstyle=SUCCESS).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=create_window.destroy,
                  bootstyle=DANGER).pack(side="left")

        # Focus the name entry
        name_entry.focus()

    def view_company_details(self):
        """View details of the selected company"""
        # Get selected item
        selected = self.company_tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select a company to view")
            return

        # Get company name
        company_name = self.company_tree.item(selected[0], "values")[0]

        # Convert company name to database file name
        db_name = f"{company_name.lower().replace(' ', '_')}.db"

        if not os.path.exists(db_name):
            messagebox.showerror(title="Error",
                                message=f"Company database for '{company_name}' not found")
            return

        # Get the main application container
        main_app = self.parent

        # Create transaction manager for the company
        from model.transaction import TransactionManager
        transaction_manager = TransactionManager(db_name)

        # Create company frame
        from view.company_frame import CompanyFrame
        company_key = f"company_{company_name}"

        if company_key in main_app.screens:
            # Remove existing company frame
            main_app.remove_screen(company_key)

        # Create new company frame
        company_frame = CompanyFrame(main_app, company_name, transaction_manager, self.return_to_admin)
        main_app.add_screen(company_key, company_frame)

        # Show company frame
        main_app.show_screen(company_key)

    def return_to_admin(self):
        """Return to admin dashboard"""
        # Get the main application container
        main_app = self.parent

        # Show admin dashboard
        main_app.show_screen("admin_dashboard")
