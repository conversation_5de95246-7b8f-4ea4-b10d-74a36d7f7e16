import sqlite3  # Needed for catching <PERSON><PERSON>rror
from datetime import datetime

from model.base_model import BaseModel


class Client(BaseModel):
    """Client model for managing client information for invoices"""

    def __init__(self, db_path):
        """Initialize the Client model with database path"""
        super().__init__(db_path)
        self._table_name = "clients"
        self._create_table()

    def table_name(self):
        """Return the table name for this model"""
        return self._table_name

    def fields(self):
        """Return a list of field names for this model"""
        return [
            'id', 'name', 'company_name', 'email', 'phone', 'address',
            'city', 'state', 'zip_code', 'country', 'tax_id', 'notes',
            'created_at', 'updated_at'
        ]

    def primary_key(self):
        """Return the primary key field name"""
        return 'id'

    def _create_table(self):
        """Create the clients table if it doesn't exist"""
        query = '''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            company_name TEXT,
            email TEXT,
            phone TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            zip_code TEXT,
            country TEXT,
            tax_id TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.execute_query(query)

    def add_client(self, client_data):
        """Add a new client to the database

        Args:
            client_data (dict): Dictionary containing client information
                - name: Client name (required)
                - company_name: Client company name
                - email: Client email
                - phone: Client phone number
                - address: Client address
                - city: Client city
                - state: Client state
                - zip_code: Client zip code
                - country: Client country
                - tax_id: Client tax ID
                - notes: Additional notes about the client

        Returns:
            int: ID of the newly created client
        """
        # Validate required fields
        if not client_data.get('name'):
            raise ValueError("Client name is required")

        # Prepare data for insertion
        columns = []
        values = []
        placeholders = []

        for key, value in client_data.items():
            if key in ['name', 'company_name', 'email', 'phone', 'address',
                      'city', 'state', 'zip_code', 'country', 'tax_id', 'notes']:
                columns.append(key)
                values.append(value)
                placeholders.append('?')

        # Add timestamps
        columns.extend(['created_at', 'updated_at'])
        values.extend([datetime.now(), datetime.now()])
        placeholders.extend(['?', '?'])

        # Build the query
        query = f'''
        INSERT INTO {self.table_name()}
        ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        '''

        # Execute the query
        result = self.execute_query(query, values)
        # For INSERT queries, execute_query returns the lastrowid
        return result

    def update_client(self, client_id, client_data):
        """Update an existing client

        Args:
            client_id (int): ID of the client to update
            client_data (dict): Dictionary containing client information to update

        Returns:
            bool: True if the update was successful, False otherwise
        """
        # Prepare data for update
        set_clause = []
        values = []

        for key, value in client_data.items():
            if key in ['name', 'company_name', 'email', 'phone', 'address',
                      'city', 'state', 'zip_code', 'country', 'tax_id', 'notes']:
                set_clause.append(f"{key} = ?")
                values.append(value)

        # Add updated_at timestamp
        set_clause.append("updated_at = ?")
        values.append(datetime.now())

        # Add client_id to values
        values.append(client_id)

        # Build the query
        query = f'''
        UPDATE {self.table_name()}
        SET {', '.join(set_clause)}
        WHERE id = ?
        '''

        # Execute the query
        result = self.execute_query(query, values)
        # For UPDATE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False

    def delete_client(self, client_id):
        """Delete a client from the database

        Args:
            client_id (int): ID of the client to delete

        Returns:
            bool: True if the deletion was successful, False otherwise
        """
        # Check if client has invoices
        query = '''
        SELECT COUNT(*) as count FROM invoices WHERE client_id = ?
        '''
        try:
            result = self.execute_query(query, [client_id])
            if result and len(result) > 0:
                count = result[0]['count']
                if count > 0:
                    raise ValueError(f"Cannot delete client with ID {client_id} because it has {count} invoices")
        except sqlite3.OperationalError:
            # Invoices table might not exist yet
            pass

        # Delete the client
        query = f'''
        DELETE FROM {self.table_name()} WHERE id = ?
        '''
        result = self.execute_query(query, [client_id])
        # For DELETE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False

    def get_client(self, client_id):
        """Get a client by ID

        Args:
            client_id (int): ID of the client to retrieve

        Returns:
            dict: Dictionary containing client information, or None if not found
        """
        query = f'''
        SELECT * FROM {self.table_name()} WHERE id = ?
        '''
        result = self.execute_query(query, [client_id])

        if not result or len(result) == 0:
            return None

        # Return the first row
        return result[0]

    def get_all_clients(self, order_by="name", order="ASC"):
        """Get all clients from the database

        Args:
            order_by (str): Column to order by (default: "name")
            order (str): Order direction, "ASC" or "DESC" (default: "ASC")

        Returns:
            list: List of dictionaries containing client information
        """
        # Validate order_by column
        valid_columns = ['id', 'name', 'company_name', 'email', 'phone',
                         'city', 'state', 'country', 'created_at', 'updated_at']
        if order_by not in valid_columns:
            order_by = "name"

        # Validate order direction
        order = order.upper()
        if order not in ["ASC", "DESC"]:
            order = "ASC"

        query = f'''
        SELECT * FROM {self.table_name()} ORDER BY {order_by} {order}
        '''
        result = self.execute_query(query)

        # For SELECT queries, execute_query returns a list of dictionaries
        clients = result

        return clients

    def search_clients(self, search_term):
        """Search for clients by name, company, email, or phone

        Args:
            search_term (str): Term to search for

        Returns:
            list: List of dictionaries containing client information
        """
        search_term = f"%{search_term}%"
        query = f'''
        SELECT * FROM {self.table_name()}
        WHERE name LIKE ? OR company_name LIKE ? OR email LIKE ? OR phone LIKE ?
        ORDER BY name ASC
        '''
        result = self.execute_query(query, [search_term, search_term, search_term, search_term])

        # For SELECT queries, execute_query returns a list of dictionaries
        clients = result

        return clients
