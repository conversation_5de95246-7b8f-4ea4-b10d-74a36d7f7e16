import tkinter as tk
from datetime import date, datetime
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.account import Account
from model.split_transaction import SplitTransaction
from view.split_transaction_dialog import SplitTransactionDialog


class SplitTransactionFrame(ttk.Frame):
    """
    Frame for managing split transactions
    """
    
    def __init__(self, parent, db_path, user_role="admin"):
        super().__init__(parent)
        self.db_path = db_path
        self.user_role = user_role
        self.parent = parent
        
        # Initialize models
        self.split_transaction_model = SplitTransaction(db_path)
        self.account_model = Account(db_path)
        
        # Data storage
        self.accounts = {}
        self.current_splits = []
        
        # UI variables
        self.account_filter_var = tk.StringVar()
        self.date_from_var = tk.StringVar()
        self.date_to_var = tk.StringVar()
        self.search_var = tk.StringVar()
        
        # Create widgets
        self.create_widgets()
        self.load_accounts()
        self.load_split_transactions()
    
    def create_widgets(self):
        """Create the main UI widgets"""
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(self, text="Split Transaction Management", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20), sticky="w")
        
        # Filters frame
        filters_frame = ttk.LabelFrame(self, text="Filters", padding=10)
        filters_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        filters_frame.grid_columnconfigure(1, weight=1)
        filters_frame.grid_columnconfigure(3, weight=1)
        filters_frame.grid_columnconfigure(5, weight=1)
        
        # Account filter
        ttk.Label(filters_frame, text="Account:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.account_filter_combo = ttk.Combobox(filters_frame, textvariable=self.account_filter_var, 
                                                state="readonly", width=20)
        self.account_filter_combo.grid(row=0, column=1, sticky="ew", padx=(0, 10))
        
        # Date range filters
        ttk.Label(filters_frame, text="From:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        self.date_from_entry = ttk.Entry(filters_frame, width=12)
        self.date_from_entry.grid(row=0, column=3, sticky="ew", padx=(0, 10))
        self.date_from_entry.insert(0, "YYYY-MM-DD")

        ttk.Label(filters_frame, text="To:").grid(row=0, column=4, sticky="w", padx=(0, 5))
        self.date_to_entry = ttk.Entry(filters_frame, width=12)
        self.date_to_entry.grid(row=0, column=5, sticky="ew", padx=(0, 10))
        self.date_to_entry.insert(0, "YYYY-MM-DD")
        
        # Search and filter buttons
        search_frame = ttk.Frame(filters_frame)
        search_frame.grid(row=0, column=6, sticky="ew", padx=(10, 0))
        
        ttk.Label(search_frame, text="Search:").pack(side="left", padx=(0, 5))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=15)
        search_entry.pack(side="left", padx=(0, 5))
        
        filter_btn = ttk.Button(search_frame, text="Filter", command=self.apply_filters, 
                               bootstyle=INFO, width=8)
        filter_btn.pack(side="left", padx=(0, 5))
        
        clear_btn = ttk.Button(search_frame, text="Clear", command=self.clear_filters, 
                              bootstyle=SECONDARY, width=8)
        clear_btn.pack(side="left")
        
        # Main content frame
        content_frame = ttk.Frame(self)
        content_frame.grid(row=2, column=0, sticky="nsew")
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_rowconfigure(1, weight=1)
        
        # Action buttons
        action_frame = ttk.Frame(content_frame)
        action_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        new_split_btn = ttk.Button(action_frame, text="New Split Transaction", 
                                  command=self.create_new_split, bootstyle=SUCCESS)
        new_split_btn.pack(side="left", padx=(0, 10))
        
        edit_split_btn = ttk.Button(action_frame, text="Edit Split", 
                                   command=self.edit_selected_split, bootstyle=PRIMARY)
        edit_split_btn.pack(side="left", padx=(0, 10))
        
        delete_split_btn = ttk.Button(action_frame, text="Delete Split", 
                                     command=self.delete_selected_split, bootstyle=DANGER)
        delete_split_btn.pack(side="left", padx=(0, 10))
        
        view_details_btn = ttk.Button(action_frame, text="View Details", 
                                     command=self.view_split_details, bootstyle=INFO)
        view_details_btn.pack(side="left")
        
        # Refresh button on the right
        refresh_btn = ttk.Button(action_frame, text="🔄 Refresh", 
                                command=self.load_split_transactions, bootstyle=SECONDARY)
        refresh_btn.pack(side="right")
        
        # Split transactions list
        list_frame = ttk.LabelFrame(content_frame, text="Split Transactions", padding=10)
        list_frame.grid(row=1, column=0, sticky="nsew")
        list_frame.grid_columnconfigure(0, weight=1)
        list_frame.grid_rowconfigure(0, weight=1)
        
        # Treeview for split transactions
        columns = ("id", "date", "description", "total_amount", "account", "type", "lines", "created_date")
        self.splits_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # Configure columns
        self.splits_tree.heading("id", text="ID")
        self.splits_tree.heading("date", text="Date")
        self.splits_tree.heading("description", text="Description")
        self.splits_tree.heading("total_amount", text="Total Amount")
        self.splits_tree.heading("account", text="Account")
        self.splits_tree.heading("type", text="Type")
        self.splits_tree.heading("lines", text="Split Lines")
        self.splits_tree.heading("created_date", text="Created")
        
        self.splits_tree.column("id", width=50)
        self.splits_tree.column("date", width=100)
        self.splits_tree.column("description", width=200)
        self.splits_tree.column("total_amount", width=100)
        self.splits_tree.column("account", width=150)
        self.splits_tree.column("type", width=80)
        self.splits_tree.column("lines", width=80)
        self.splits_tree.column("created_date", width=120)
        
        self.splits_tree.grid(row=0, column=0, sticky="nsew")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.splits_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        self.splits_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(list_frame, orient="horizontal", command=self.splits_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        self.splits_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Bind double-click to view details
        self.splits_tree.bind("<Double-1>", lambda e: self.view_split_details())
        
        # Status bar
        status_frame = ttk.Frame(content_frame)
        status_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))
        
        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side="left")
        
        # Summary on the right
        self.summary_var = tk.StringVar()
        summary_label = ttk.Label(status_frame, textvariable=self.summary_var, font=("Arial", 9, "bold"))
        summary_label.pack(side="right")
    
    def load_accounts(self):
        """Load available accounts"""
        try:
            accounts = self.account_model.get_all_accounts()
            self.accounts = {f"{acc['name']} ({acc['type']})": acc['id'] for acc in accounts}
            
            account_names = ["All Accounts"] + list(self.accounts.keys())
            self.account_filter_combo.configure(values=account_names)
            self.account_filter_var.set("All Accounts")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load accounts: {str(e)}")
    
    def load_split_transactions(self):
        """Load split transactions with current filters"""
        try:
            # Get filter values
            account_id = None
            if self.account_filter_var.get() != "All Accounts":
                account_id = self.accounts.get(self.account_filter_var.get())
            
            start_date = None
            end_date = None

            try:
                if hasattr(self, 'date_from_entry'):
                    from_date = self.date_from_entry.get().strip()
                    if from_date and from_date != "YYYY-MM-DD":
                        start_date = from_date
                if hasattr(self, 'date_to_entry'):
                    to_date = self.date_to_entry.get().strip()
                    if to_date and to_date != "YYYY-MM-DD":
                        end_date = to_date
            except:
                pass
            
            # Load split transactions
            self.current_splits = self.split_transaction_model.get_all_split_transactions(
                account_id=account_id, start_date=start_date, end_date=end_date
            )
            
            # Apply search filter
            search_term = self.search_var.get().lower()
            if search_term:
                self.current_splits = [
                    split for split in self.current_splits
                    if search_term in split['description'].lower() or
                       search_term in str(split['total_amount']) or
                       search_term in split.get('account_name', '').lower()
                ]
            
            # Clear existing items
            for item in self.splits_tree.get_children():
                self.splits_tree.delete(item)
            
            # Populate treeview
            total_amount = 0
            for split in self.current_splits:
                total_amount += split['total_amount']
                
                # Format created date
                created_date = split.get('created_date', '')
                if created_date:
                    try:
                        dt = datetime.strptime(created_date, '%Y-%m-%d %H:%M:%S')
                        created_date = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        pass
                
                self.splits_tree.insert("", "end", values=(
                    split['id'],
                    split['date'],
                    split['description'][:50] + "..." if len(split['description']) > 50 else split['description'],
                    f"${split['total_amount']:.2f}",
                    split.get('account_name', ''),
                    split['type'].title(),
                    split.get('line_count', 0),
                    created_date
                ))
            
            # Update status and summary
            self.status_var.set(f"Loaded {len(self.current_splits)} split transactions")
            self.summary_var.set(f"Total Amount: ${total_amount:.2f}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load split transactions: {str(e)}")
            self.status_var.set("Error loading split transactions")
    
    def apply_filters(self):
        """Apply current filters"""
        self.load_split_transactions()
    
    def clear_filters(self):
        """Clear all filters"""
        self.account_filter_var.set("All Accounts")
        self.search_var.set("")
        
        # Clear date filters
        try:
            self.date_from_entry.delete(0, tk.END)
            self.date_from_entry.insert(0, "YYYY-MM-DD")
            self.date_to_entry.delete(0, tk.END)
            self.date_to_entry.insert(0, "YYYY-MM-DD")
        except:
            pass
        
        self.load_split_transactions()
    
    def create_new_split(self):
        """Create a new split transaction"""
        dialog = SplitTransactionDialog(
            parent=self,
            db_path=self.db_path,
            callback=self.load_split_transactions
        )
    
    def edit_selected_split(self):
        """Edit the selected split transaction"""
        selection = self.splits_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a split transaction to edit")
            return
        
        split_id = self.splits_tree.item(selection[0], "values")[0]
        
        dialog = SplitTransactionDialog(
            parent=self,
            db_path=self.db_path,
            split_transaction_id=int(split_id),
            callback=self.load_split_transactions
        )
    
    def delete_selected_split(self):
        """Delete the selected split transaction"""
        selection = self.splits_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a split transaction to delete")
            return
        
        split_id = self.splits_tree.item(selection[0], "values")[0]
        description = self.splits_tree.item(selection[0], "values")[2]
        
        if not messagebox.askyesno("Confirm Delete", 
                                  f"Are you sure you want to delete the split transaction '{description}'?\n\n"
                                  f"This action cannot be undone."):
            return
        
        try:
            success = self.split_transaction_model.delete_split_transaction(int(split_id))
            if success:
                messagebox.showinfo("Success", "Split transaction deleted successfully")
                self.load_split_transactions()
            else:
                messagebox.showerror("Error", "Failed to delete split transaction")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete split transaction: {str(e)}")
    
    def view_split_details(self):
        """View details of the selected split transaction"""
        selection = self.splits_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a split transaction to view")
            return
        
        split_id = self.splits_tree.item(selection[0], "values")[0]
        
        try:
            split_data = self.split_transaction_model.get_split_transaction(int(split_id))
            if not split_data:
                messagebox.showerror("Error", "Split transaction not found")
                return
            
            # Create details dialog
            details_dialog = tk.Toplevel(self)
            details_dialog.title(f"Split Transaction Details - ID {split_id}")
            details_dialog.geometry("700x600")
            details_dialog.transient(self)
            
            frame = ttk.Frame(details_dialog, padding=20)
            frame.pack(fill="both", expand=True)
            
            # Transaction details
            details_frame = ttk.LabelFrame(frame, text="Transaction Details", padding=10)
            details_frame.pack(fill="x", pady=(0, 10))
            
            details_text = f"""ID: {split_data['id']}
Date: {split_data['date']}
Description: {split_data['description']}
Total Amount: ${split_data['total_amount']:.2f}
Account: {split_data.get('account_name', 'N/A')}
Type: {split_data['type'].title()}
Created: {split_data.get('created_date', 'N/A')}
Created By: {split_data.get('created_by', 'N/A')}
Notes: {split_data.get('notes', 'N/A')}"""
            
            details_label = ttk.Label(details_frame, text=details_text, font=("Courier", 10))
            details_label.pack(anchor="w")
            
            # Split lines
            lines_frame = ttk.LabelFrame(frame, text="Split Lines", padding=10)
            lines_frame.pack(fill="both", expand=True, pady=(0, 10))
            
            # Treeview for lines
            lines_columns = ("category", "account", "amount", "percentage", "description")
            lines_tree = ttk.Treeview(lines_frame, columns=lines_columns, show="headings", height=10)
            
            lines_tree.heading("category", text="Category")
            lines_tree.heading("account", text="Account")
            lines_tree.heading("amount", text="Amount")
            lines_tree.heading("percentage", text="Percentage")
            lines_tree.heading("description", text="Description")
            
            lines_tree.column("category", width=150)
            lines_tree.column("account", width=150)
            lines_tree.column("amount", width=100)
            lines_tree.column("percentage", width=80)
            lines_tree.column("description", width=200)
            
            lines_tree.pack(fill="both", expand=True)
            
            # Populate lines
            for line in split_data.get('lines', []):
                lines_tree.insert("", "end", values=(
                    line.get('category_name', 'N/A'),
                    line.get('account_name', 'N/A'),
                    f"${line['amount']:.2f}",
                    f"{line.get('percentage', 0):.1f}%",
                    line.get('description', '')
                ))
            
            # Close button
            close_btn = ttk.Button(frame, text="Close", command=details_dialog.destroy, 
                                  bootstyle=SECONDARY)
            close_btn.pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load split transaction details: {str(e)}")
