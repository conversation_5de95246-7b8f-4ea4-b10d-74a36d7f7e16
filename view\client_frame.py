import os
import sqlite3
import tkinter as tk
from datetime import datetime
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import DANGER, INFO, PRIMARY, SECONDARY, SUCCESS


class ClientFrame(ttk.Frame):
    """Client dashboard as a frame instead of a standalone window"""

    def __init__(self, parent, username, logout_callback, create_company_callback, open_company_callback):
        super().__init__(parent)
        self.parent = parent
        self.username = username
        self.logout_callback = logout_callback
        self.create_company_callback = create_company_callback
        self.open_company_callback = open_company_callback
        self.title = "Client Dashboard"

        self.create_widgets()

    def create_widgets(self):
        # Main frame with modern styling
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with welcome message and logout button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 30))

        # Left side - welcome and subtitle
        welcome_frame = ttk.Frame(header_frame)
        welcome_frame.pack(side="left")

        welcome_label = ttk.Label(welcome_frame, text=f"Welcome back, {self.username}!",
                                font=("Segoe UI", 20, "bold"))
        welcome_label.pack(anchor="w")

        subtitle_label = ttk.Label(welcome_frame, text="Manage your companies and access your financial data",
                                 font=("Segoe UI", 11), foreground="gray")
        subtitle_label.pack(anchor="w", pady=(5, 0))

        # Right side - logout button
        logout_button = ttk.Button(header_frame, text="Logout", command=self.logout_callback, bootstyle=DANGER)
        logout_button.pack(side="right")

        # Action buttons with modern styling
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill="x", pady=(0, 20))

        # Create company button with icon-like styling
        create_button = ttk.Button(action_frame, text="+ Create New Company",
                                 command=self.create_company, bootstyle=SUCCESS,
                                 width=20)
        create_button.pack(side="left", padx=(0, 10))

        # Refresh button
        refresh_button = ttk.Button(action_frame, text="🔄 Refresh",
                                  command=self.load_companies, bootstyle=SECONDARY,
                                  width=12)
        refresh_button.pack(side="left")

        # Sort options
        sort_frame = ttk.Frame(action_frame)
        sort_frame.pack(side="right")

        ttk.Label(sort_frame, text="Sort by:", font=("Segoe UI", 10)).pack(side="left", padx=(0, 5))

        self.sort_var = tk.StringVar(value="Last Used")
        sort_combo = ttk.Combobox(sort_frame, textvariable=self.sort_var,
                                values=["Last Used", "Name", "Created Date", "Balance"],
                                state="readonly", width=12)
        sort_combo.pack(side="left")
        sort_combo.bind("<<ComboboxSelected>>", lambda e: self.load_companies())

        # Companies container with modern card layout
        companies_container = ttk.Frame(main_frame)
        companies_container.pack(fill="both", expand=True)

        # Create canvas for scrollable card view
        self.canvas = tk.Canvas(companies_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(companies_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)

        # Load companies
        self.load_companies()

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def load_companies(self):
        """Load companies as modern cards with last-used sorting"""
        # Clear existing cards
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Get all company databases with metadata
        companies_data = []
        # Exclude system databases: users.db, settings.db, and test files
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        for db_file in db_files:
            try:
                # Extract company name from filename
                company_name = db_file.replace('.db', '').replace('_', ' ').title()

                # Connect to the database
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Get metadata
                company_info = {
                    'name': company_name,
                    'db_file': db_file,
                    'created_date': 'Unknown',
                    'last_accessed': 'Never',
                    'transaction_count': 0,
                    'balance': 0.0,
                    'last_accessed_timestamp': 0
                }

                # Get created date
                try:
                    cursor.execute("SELECT value FROM metadata WHERE key = 'created_date'")
                    result = cursor.fetchone()
                    if result:
                        company_info['created_date'] = result[0]
                except:
                    pass

                # Get last accessed date
                try:
                    cursor.execute("SELECT value FROM metadata WHERE key = 'last_accessed'")
                    result = cursor.fetchone()
                    if result:
                        company_info['last_accessed'] = result[0]
                        # Convert to timestamp for sorting
                        try:
                            dt = datetime.strptime(result[0], "%Y-%m-%d %H:%M:%S")
                            company_info['last_accessed_timestamp'] = dt.timestamp()
                        except:
                            pass
                except:
                    pass

                # Get transaction count
                try:
                    cursor.execute("SELECT COUNT(*) FROM transactions")
                    company_info['transaction_count'] = cursor.fetchone()[0]
                except:
                    pass

                # Get total balance
                try:
                    cursor.execute("SELECT SUM(current_balance) FROM accounts")
                    balance = cursor.fetchone()[0]
                    company_info['balance'] = balance or 0.0
                except:
                    pass

                companies_data.append(company_info)
                conn.close()

            except Exception as e:
                print(f"Error loading company {db_file}: {e}")

        # Sort companies based on selected criteria
        sort_by = self.sort_var.get()
        if sort_by == "Last Used":
            companies_data.sort(key=lambda x: x['last_accessed_timestamp'], reverse=True)
        elif sort_by == "Name":
            companies_data.sort(key=lambda x: x['name'])
        elif sort_by == "Created Date":
            companies_data.sort(key=lambda x: x['created_date'], reverse=True)
        elif sort_by == "Balance":
            companies_data.sort(key=lambda x: x['balance'], reverse=True)

        # Create cards for each company
        if not companies_data:
            self.create_empty_state()
        else:
            self.create_company_cards(companies_data)

    def create_empty_state(self):
        """Create empty state when no companies exist"""
        empty_frame = ttk.Frame(self.scrollable_frame)
        empty_frame.pack(fill="x", pady=50)

        # Empty state icon (using text as icon)
        icon_label = ttk.Label(empty_frame, text="🏢", font=("Segoe UI", 48))
        icon_label.pack()

        # Empty state message
        message_label = ttk.Label(empty_frame, text="No companies found",
                                font=("Segoe UI", 16, "bold"))
        message_label.pack(pady=(10, 5))

        subtitle_label = ttk.Label(empty_frame, text="Create your first company to get started",
                                 font=("Segoe UI", 11), foreground="gray")
        subtitle_label.pack()

        # Create button
        create_btn = ttk.Button(empty_frame, text="+ Create Your First Company",
                              command=self.create_company, bootstyle=SUCCESS)
        create_btn.pack(pady=20)

    def create_company_cards(self, companies_data):
        """Create modern cards for companies"""
        # Calculate cards per row based on window width (responsive design)
        cards_per_row = max(1, min(3, len(companies_data)))

        current_row_frame = None
        cards_in_current_row = 0

        for i, company in enumerate(companies_data):
            # Create new row frame if needed
            if cards_in_current_row == 0:
                current_row_frame = ttk.Frame(self.scrollable_frame)
                current_row_frame.pack(fill="x", pady=(0, 15))

            # Create company card with enhanced styling
            card_frame = ttk.LabelFrame(current_row_frame, text="", padding=20, relief="raised", borderwidth=2)
            card_frame.pack(side="left", fill="both", expand=True, padx=(0, 15 if cards_in_current_row < cards_per_row - 1 else 0))

            # Add hover effect simulation (color change on click)
            self.add_card_interactions(card_frame, company)

            # Company name header
            name_label = ttk.Label(card_frame, text=company['name'],
                                 font=("Segoe UI", 14, "bold"))
            name_label.pack(anchor="w")

            # Last accessed info with color coding
            last_accessed = company['last_accessed']
            if last_accessed == 'Never':
                access_color = "orange"
                access_text = "🆕 New Company"
            else:
                access_color = "green"
                access_text = f"🕒 Last used: {last_accessed}"

            access_label = ttk.Label(card_frame, text=access_text,
                                   font=("Segoe UI", 9), foreground=access_color)
            access_label.pack(anchor="w", pady=(5, 10))

            # Stats frame
            stats_frame = ttk.Frame(card_frame)
            stats_frame.pack(fill="x", pady=(0, 15))

            # Balance with color coding
            balance = company['balance']
            balance_color = "green" if balance >= 0 else "red"
            balance_text = f"${balance:,.2f}"

            ttk.Label(stats_frame, text="💰 Balance:", font=("Segoe UI", 9, "bold")).pack(anchor="w")
            ttk.Label(stats_frame, text=balance_text, font=("Segoe UI", 12, "bold"),
                     foreground=balance_color).pack(anchor="w")

            # Transaction count
            ttk.Label(stats_frame, text=f"📊 Transactions: {company['transaction_count']}",
                     font=("Segoe UI", 9)).pack(anchor="w", pady=(5, 0))

            # Created date
            ttk.Label(stats_frame, text=f"📅 Created: {company['created_date']}",
                     font=("Segoe UI", 9)).pack(anchor="w")

            # Action buttons
            button_frame = ttk.Frame(card_frame)
            button_frame.pack(fill="x")

            open_btn = ttk.Button(button_frame, text="Open",
                                command=lambda name=company['name']: self.open_company_by_name(name),
                                bootstyle=PRIMARY, width=12)
            open_btn.pack(side="left")

            # Quick stats button
            stats_btn = ttk.Button(button_frame, text="📈",
                                 command=lambda name=company['name']: self.show_quick_stats(name),
                                 bootstyle=INFO, width=3)
            stats_btn.pack(side="right")

            cards_in_current_row += 1

            # Reset row if we've reached the limit
            if cards_in_current_row >= cards_per_row:
                cards_in_current_row = 0

    def add_card_interactions(self, card_frame, company):
        """Add interactive features to company cards"""
        # Store original relief for hover effect
        original_relief = card_frame.cget("relief")

        def on_enter(event):
            card_frame.configure(relief="solid", borderwidth=3)

        def on_leave(event):
            card_frame.configure(relief=original_relief, borderwidth=2)

        # Bind hover events to the card frame and all its children
        def bind_hover_recursive(widget):
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)
            for child in widget.winfo_children():
                bind_hover_recursive(child)

        bind_hover_recursive(card_frame)

    def open_company_by_name(self, company_name):
        """Open company and update last accessed time"""
        # Update last accessed time in database
        self.update_last_accessed(company_name)

        # Call the original open company callback
        self.open_company_callback(company_name)

    def update_last_accessed(self, company_name):
        """Update the last accessed timestamp for a company"""
        try:
            db_file = company_name.lower().replace(' ', '_') + '.db'
            if os.path.exists(db_file):
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Create metadata table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS metadata (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')

                # Update last accessed time
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                cursor.execute('''
                    INSERT OR REPLACE INTO metadata (key, value)
                    VALUES ('last_accessed', ?)
                ''', (current_time,))

                conn.commit()
                conn.close()
        except Exception as e:
            print(f"Error updating last accessed time: {e}")

    def show_quick_stats(self, company_name):
        """Show quick statistics for a company"""
        try:
            db_file = company_name.lower().replace(' ', '_') + '.db'
            if not os.path.exists(db_file):
                messagebox.showerror("Error", "Company database not found")
                return

            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # Get detailed statistics
            stats = {}

            # Account count
            cursor.execute("SELECT COUNT(*) FROM accounts")
            stats['accounts'] = cursor.fetchone()[0]

            # Recent transactions (last 30 days)
            cursor.execute("""
                SELECT COUNT(*) FROM transactions
                WHERE date >= date('now', '-30 days')
            """)
            stats['recent_transactions'] = cursor.fetchone()[0]

            # Income vs Expenses this month
            cursor.execute("""
                SELECT
                    SUM(CASE WHEN type = 'Income' THEN amount ELSE 0 END) as income,
                    SUM(CASE WHEN type = 'Expense' THEN amount ELSE 0 END) as expenses
                FROM transactions
                WHERE date >= date('now', 'start of month')
            """)
            result = cursor.fetchone()
            stats['monthly_income'] = result[0] or 0
            stats['monthly_expenses'] = result[1] or 0

            conn.close()

            # Show stats in a message box
            stats_text = f"""Company: {company_name}

📊 Quick Statistics:
• Accounts: {stats['accounts']}
• Recent Transactions (30 days): {stats['recent_transactions']}

💰 This Month:
• Income: ${stats['monthly_income']:,.2f}
• Expenses: ${stats['monthly_expenses']:,.2f}
• Net: ${stats['monthly_income'] - stats['monthly_expenses']:,.2f}"""

            messagebox.showinfo("Company Statistics", stats_text)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load statistics: {str(e)}")

    def create_company(self):
        """Call the create company callback"""
        self.create_company_callback()

        # Refresh the company list
        self.load_companies()

    def open_company(self):
        """Legacy method - now handled by open_company_by_name"""
        messagebox.showinfo("Info", "Please click the 'Open' button on a company card to open it.")
