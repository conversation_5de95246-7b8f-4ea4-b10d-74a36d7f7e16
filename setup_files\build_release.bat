@echo off
echo ===================================================
echo Cashbook Release Builder
echo ===================================================
echo.
echo This script will build the Cashbook executable and installer.
echo.
echo Prerequisites:
echo - Python 3.x with required packages installed
echo - Inno Setup installed
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

echo.
echo Step 1: Running the build script...
call build_exe.bat

echo.
echo Step 2: Creating the installer...
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" setup_files/cashbook_installer.iss

echo.
echo ===================================================
echo Build process completed!
echo ===================================================
echo.
echo If successful, the installer is located in the Output directory.
echo.
pause
