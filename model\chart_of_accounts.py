import sqlite3
from datetime import datetime

from model.account import Account
from model.base_model import BaseModel


class ChartOfAccounts(BaseModel):
    """Model for managing the chart of accounts with proper accounting structure"""

    def __init__(self, db_path):
        super().__init__(db_path)
        self._table_name = "chart_of_accounts"
        self._create_tables()

    def table_name(self):
        return self._table_name

    def fields(self):
        return [
            'id', 'account_number', 'account_name', 'classification', 'account_type',
            'parent_id', 'level', 'is_active', 'description', 'created_at', 'updated_at'
        ]

    def primary_key(self):
        return 'id'

    def _create_tables(self):
        """Create the chart of accounts table and update accounts table"""
        # First, update the accounts table to include new fields
        self._update_accounts_table()

        # Create chart of accounts table for hierarchy management
        query = '''
        CREATE TABLE IF NOT EXISTS chart_of_accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_number TEXT UNIQUE NOT NULL,
            account_name TEXT NOT NULL,
            classification TEXT NOT NULL,
            account_type TEXT NOT NULL,
            parent_id INTEGER,
            level INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES chart_of_accounts (id)
        )
        '''
        self.execute_query(query)

    def _update_accounts_table(self):
        """Update the accounts table to include new fields for proper accounting"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Check if new columns exist, if not add them
            cursor.execute("PRAGMA table_info(accounts)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'classification' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN classification TEXT")

            if 'account_number' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN account_number TEXT")

            if 'parent_id' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN parent_id INTEGER")

            if 'is_active' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN is_active BOOLEAN DEFAULT 1")

            conn.commit()
        except sqlite3.Error as e:
            print(f"Error updating accounts table: {e}")
        finally:
            conn.close()

    def create_standard_chart_of_accounts(self):
        """Create a standard chart of accounts for small business"""
        standard_accounts = [
            # Assets (1000-1999)
            {'number': '1000', 'name': 'ASSETS', 'classification': 'Asset', 'type': 'Header', 'parent': None, 'level': 0},
            {'number': '1100', 'name': 'Current Assets', 'classification': 'Asset', 'type': 'Header', 'parent': '1000', 'level': 1},
            {'number': '1110', 'name': 'Cash', 'classification': 'Asset', 'type': 'Cash', 'parent': '1100', 'level': 2},
            {'number': '1120', 'name': 'Checking Account', 'classification': 'Asset', 'type': 'Checking Account', 'parent': '1100', 'level': 2},
            {'number': '1130', 'name': 'Savings Account', 'classification': 'Asset', 'type': 'Savings Account', 'parent': '1100', 'level': 2},
            {'number': '1140', 'name': 'Accounts Receivable', 'classification': 'Asset', 'type': 'Accounts Receivable', 'parent': '1100', 'level': 2},
            {'number': '1150', 'name': 'Inventory', 'classification': 'Asset', 'type': 'Inventory', 'parent': '1100', 'level': 2},
            {'number': '1160', 'name': 'Prepaid Expenses', 'classification': 'Asset', 'type': 'Prepaid Expenses', 'parent': '1100', 'level': 2},

            {'number': '1200', 'name': 'Fixed Assets', 'classification': 'Asset', 'type': 'Header', 'parent': '1000', 'level': 1},
            {'number': '1210', 'name': 'Equipment', 'classification': 'Asset', 'type': 'Equipment', 'parent': '1200', 'level': 2},
            {'number': '1220', 'name': 'Furniture & Fixtures', 'classification': 'Asset', 'type': 'Furniture', 'parent': '1200', 'level': 2},
            {'number': '1230', 'name': 'Vehicles', 'classification': 'Asset', 'type': 'Vehicles', 'parent': '1200', 'level': 2},
            {'number': '1240', 'name': 'Accumulated Depreciation', 'classification': 'Asset', 'type': 'Accumulated Depreciation', 'parent': '1200', 'level': 2},

            # Liabilities (2000-2999)
            {'number': '2000', 'name': 'LIABILITIES', 'classification': 'Liability', 'type': 'Header', 'parent': None, 'level': 0},
            {'number': '2100', 'name': 'Current Liabilities', 'classification': 'Liability', 'type': 'Header', 'parent': '2000', 'level': 1},
            {'number': '2110', 'name': 'Accounts Payable', 'classification': 'Liability', 'type': 'Accounts Payable', 'parent': '2100', 'level': 2},
            {'number': '2120', 'name': 'Credit Cards', 'classification': 'Liability', 'type': 'Credit Cards', 'parent': '2100', 'level': 2},
            {'number': '2130', 'name': 'Accrued Expenses', 'classification': 'Liability', 'type': 'Accrued Expenses', 'parent': '2100', 'level': 2},
            {'number': '2140', 'name': 'Sales Tax Payable', 'classification': 'Liability', 'type': 'Sales Tax Payable', 'parent': '2100', 'level': 2},

            {'number': '2200', 'name': 'Long-term Liabilities', 'classification': 'Liability', 'type': 'Header', 'parent': '2000', 'level': 1},
            {'number': '2210', 'name': 'Long-term Loans', 'classification': 'Liability', 'type': 'Long-term Loans', 'parent': '2200', 'level': 2},
            {'number': '2220', 'name': 'Mortgages', 'classification': 'Liability', 'type': 'Mortgages', 'parent': '2200', 'level': 2},

            # Equity (3000-3999)
            {'number': '3000', 'name': 'EQUITY', 'classification': 'Equity', 'type': 'Header', 'parent': None, 'level': 0},
            {'number': '3100', 'name': "Owner's Capital", 'classification': 'Equity', 'type': "Owner's Capital", 'parent': '3000', 'level': 1},
            {'number': '3200', 'name': 'Retained Earnings', 'classification': 'Equity', 'type': 'Retained Earnings', 'parent': '3000', 'level': 1},
            {'number': '3300', 'name': "Owner's Drawings", 'classification': 'Equity', 'type': "Owner's Drawings", 'parent': '3000', 'level': 1},

            # Revenue (4000-4999)
            {'number': '4000', 'name': 'REVENUE', 'classification': 'Revenue', 'type': 'Header', 'parent': None, 'level': 0},
            {'number': '4100', 'name': 'Sales Revenue', 'classification': 'Revenue', 'type': 'Sales Revenue', 'parent': '4000', 'level': 1},
            {'number': '4200', 'name': 'Service Revenue', 'classification': 'Revenue', 'type': 'Service Revenue', 'parent': '4000', 'level': 1},
            {'number': '4300', 'name': 'Interest Income', 'classification': 'Revenue', 'type': 'Interest Income', 'parent': '4000', 'level': 1},
            {'number': '4400', 'name': 'Other Income', 'classification': 'Revenue', 'type': 'Miscellaneous Income', 'parent': '4000', 'level': 1},

            # Expenses (5000-9999)
            {'number': '5000', 'name': 'EXPENSES', 'classification': 'Expense', 'type': 'Header', 'parent': None, 'level': 0},
            {'number': '5100', 'name': 'Cost of Goods Sold', 'classification': 'Expense', 'type': 'Cost of Goods Sold', 'parent': '5000', 'level': 1},

            {'number': '6000', 'name': 'Operating Expenses', 'classification': 'Expense', 'type': 'Header', 'parent': '5000', 'level': 1},
            {'number': '6100', 'name': 'Salaries Expense', 'classification': 'Expense', 'type': 'Salaries Expense', 'parent': '6000', 'level': 2},
            {'number': '6200', 'name': 'Rent Expense', 'classification': 'Expense', 'type': 'Rent Expense', 'parent': '6000', 'level': 2},
            {'number': '6300', 'name': 'Utilities Expense', 'classification': 'Expense', 'type': 'Utilities Expense', 'parent': '6000', 'level': 2},
            {'number': '6400', 'name': 'Office Supplies', 'classification': 'Expense', 'type': 'Office Supplies', 'parent': '6000', 'level': 2},
            {'number': '6500', 'name': 'Insurance Expense', 'classification': 'Expense', 'type': 'Insurance Expense', 'parent': '6000', 'level': 2},

            {'number': '7000', 'name': 'Administrative Expenses', 'classification': 'Expense', 'type': 'Header', 'parent': '5000', 'level': 1},
            {'number': '7100', 'name': 'Professional Fees', 'classification': 'Expense', 'type': 'Professional Fees', 'parent': '7000', 'level': 2},
            {'number': '7200', 'name': 'Bank Charges', 'classification': 'Expense', 'type': 'Bank Charges', 'parent': '7000', 'level': 2},
            {'number': '7300', 'name': 'Depreciation Expense', 'classification': 'Expense', 'type': 'Depreciation Expense', 'parent': '7000', 'level': 2},

            {'number': '8000', 'name': 'Other Expenses', 'classification': 'Expense', 'type': 'Header', 'parent': '5000', 'level': 1},
            {'number': '8100', 'name': 'Interest Expense', 'classification': 'Expense', 'type': 'Interest Expense', 'parent': '8000', 'level': 2},
        ]

        # Insert accounts with parent relationships
        for account_data in standard_accounts:
            parent_id = None
            if account_data['parent']:
                parent_id = self.get_account_id_by_number(account_data['parent'])

            self.add_account(
                account_number=account_data['number'],
                account_name=account_data['name'],
                classification=account_data['classification'],
                account_type=account_data['type'],
                parent_id=parent_id,
                level=account_data['level']
            )

    def add_account(self, account_number, account_name, classification, account_type,
                   parent_id=None, level=0, description=""):
        """Add a new account to the chart of accounts"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Insert into chart_of_accounts
            cursor.execute('''
                INSERT INTO chart_of_accounts
                (account_number, account_name, classification, account_type, parent_id, level, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (account_number, account_name, classification, account_type, parent_id, level, description))

            chart_id = cursor.lastrowid

            # Also insert into accounts table for backward compatibility
            cursor.execute('''
                INSERT INTO accounts
                (name, type, currency, opening_balance, current_balance, classification, account_number, parent_id, is_active, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (account_name, account_type, 'USD', 0.0, 0.0, classification, account_number, parent_id, True, datetime.now().strftime("%Y-%m-%d")))

            conn.commit()
            return chart_id

        except sqlite3.Error as e:
            conn.rollback()
            print(f"Error adding account: {e}")
            raise
        finally:
            conn.close()

    def get_account_id_by_number(self, account_number):
        """Get account ID by account number"""
        query = "SELECT id FROM chart_of_accounts WHERE account_number = ?"
        result = self.execute_query(query, [account_number])
        return result[0]['id'] if result else None

    def get_chart_of_accounts(self, include_inactive=False):
        """Get the complete chart of accounts in hierarchical order"""
        where_clause = "" if include_inactive else "WHERE is_active = 1"
        query = f'''
        SELECT id, account_number, account_name, classification, account_type,
               parent_id, level, is_active, description
        FROM chart_of_accounts
        {where_clause}
        ORDER BY account_number
        '''
        return self.execute_query(query)

    def get_accounts_by_classification(self, classification):
        """Get all accounts for a specific classification"""
        query = '''
        SELECT id, account_number, account_name, classification, account_type,
               parent_id, level, is_active, description
        FROM chart_of_accounts
        WHERE classification = ? AND is_active = 1
        ORDER BY account_number
        '''
        return self.execute_query(query, [classification])

    def validate_accounting_equation(self):
        """Validate that the accounting equation (Assets = Liabilities + Equity) is balanced"""
        # Get balances by classification
        assets = self.get_total_balance_by_classification('Asset')
        liabilities = self.get_total_balance_by_classification('Liability')
        equity = self.get_total_balance_by_classification('Equity')

        # Calculate the difference
        left_side = assets
        right_side = liabilities + equity
        difference = left_side - right_side

        return {
            'assets': assets,
            'liabilities': liabilities,
            'equity': equity,
            'difference': difference,
            'is_balanced': abs(difference) < 0.01  # Allow for small rounding differences
        }

    def get_total_balance_by_classification(self, classification):
        """Get total balance for all accounts in a classification"""
        query = '''
        SELECT SUM(a.current_balance) as total_balance
        FROM accounts a
        JOIN chart_of_accounts c ON a.account_number = c.account_number
        WHERE c.classification = ? AND a.is_active = 1
        '''
        result = self.execute_query(query, [classification])
        return result[0]['total_balance'] or 0.0
