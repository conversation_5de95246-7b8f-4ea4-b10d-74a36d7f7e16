import tkinter as tk
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.account import Account


class AccountManagementFrame(ttk.Frame):
    """Account management frame for creating, editing, and deleting accounts"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Account Management - {company_name}"

        # Create account model
        self.account_model = Account(self.db_path)

        self.create_widgets()
        self.load_accounts()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with title and close button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(header_frame, text="Account Management", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")

        close_button = ttk.Button(header_frame, text="Close", command=self.close_callback, bootstyle=DANGER)
        close_button.pack(side="right")

        # Split view: Account list on left, details on right
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill="both", expand=True)

        # Left side: Account list
        list_frame = ttk.LabelFrame(content_frame, text="Accounts", padding=10)
        list_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))

        # Account list buttons
        button_frame = ttk.Frame(list_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        add_button = ttk.Button(button_frame, text="Add Account", command=self.add_account, bootstyle=SUCCESS)
        add_button.pack(side="left", padx=(0, 5))

        edit_button = ttk.Button(button_frame, text="Edit Account", command=self.edit_account)
        edit_button.pack(side="left", padx=5)

        delete_button = ttk.Button(button_frame, text="Delete Account", command=self.delete_account, bootstyle=DANGER)
        delete_button.pack(side="left", padx=5)

        # Account treeview
        columns = ("id", "number", "name", "classification", "type", "currency", "balance")

        # Create a frame for the treeview and scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill="both", expand=True)

        # Create the treeview
        self.account_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=10)

        # Define headings
        self.account_tree.heading("id", text="ID")
        self.account_tree.heading("number", text="Number")
        self.account_tree.heading("name", text="Account Name")
        self.account_tree.heading("classification", text="Classification")
        self.account_tree.heading("type", text="Type")
        self.account_tree.heading("currency", text="Currency")
        self.account_tree.heading("balance", text="Balance")

        # Define columns
        self.account_tree.column("id", width=50, stretch=False)
        self.account_tree.column("number", width=80, stretch=False)
        self.account_tree.column("name", width=150)
        self.account_tree.column("classification", width=100)
        self.account_tree.column("type", width=120)
        self.account_tree.column("currency", width=80, anchor="center")
        self.account_tree.column("balance", width=100, anchor="e")

        # Add scrollbars
        scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical", command=self.account_tree.yview)
        scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.account_tree.xview)
        self.account_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Pack scrollbars and treeview
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        self.account_tree.pack(fill="both", expand=True)

        # Add right-click context menu
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Edit Account", command=self.edit_account)
        self.context_menu.add_command(label="Delete Account", command=self.delete_account)

        # Bind right-click to show context menu
        self.account_tree.bind("<Button-3>", self.show_context_menu)

        # Double-click to edit
        self.account_tree.bind("<Double-1>", lambda event: self.edit_account())

        # Right side: Account details
        details_frame = ttk.LabelFrame(content_frame, text="Account Details", padding=10)
        details_frame.pack(side="right", fill="both", expand=True)

        # Account details form
        form_frame = ttk.Frame(details_frame)
        form_frame.pack(fill="both", expand=True)

        # Account name
        ttk.Label(form_frame, text="Account Name:").grid(row=0, column=0, sticky="w", pady=5)
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(form_frame, textvariable=self.name_var)
        name_entry.grid(row=0, column=1, sticky="ew", pady=5)

        # Account Classification
        ttk.Label(form_frame, text="Classification:").grid(row=1, column=0, sticky="w", pady=5)
        self.classification_var = tk.StringVar()
        from model.account import Account
        classifications = Account.get_all_classifications()
        classification_combo = ttk.Combobox(form_frame, textvariable=self.classification_var, values=classifications, state="readonly")
        classification_combo.grid(row=1, column=1, sticky="ew", pady=5)
        classification_combo.bind("<<ComboboxSelected>>", self.on_classification_changed)

        # Account type
        ttk.Label(form_frame, text="Account Type:").grid(row=2, column=0, sticky="w", pady=5)
        self.type_var = tk.StringVar()
        self.type_combo = ttk.Combobox(form_frame, textvariable=self.type_var, state="readonly")
        self.type_combo.grid(row=2, column=1, sticky="ew", pady=5)

        # Account Number
        ttk.Label(form_frame, text="Account Number:").grid(row=3, column=0, sticky="w", pady=5)
        self.account_number_var = tk.StringVar()
        account_number_entry = ttk.Entry(form_frame, textvariable=self.account_number_var)
        account_number_entry.grid(row=3, column=1, sticky="ew", pady=5)

        # Currency
        ttk.Label(form_frame, text="Currency:").grid(row=4, column=0, sticky="w", pady=5)
        self.currency_var = tk.StringVar(value="USD")
        currencies = ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY", "INR"]
        currency_combo = ttk.Combobox(form_frame, textvariable=self.currency_var, values=currencies, state="readonly")
        currency_combo.grid(row=4, column=1, sticky="ew", pady=5)

        # Opening balance
        ttk.Label(form_frame, text="Opening Balance:").grid(row=5, column=0, sticky="w", pady=5)
        self.balance_var = tk.DoubleVar(value=0.0)
        balance_entry = ttk.Entry(form_frame, textvariable=self.balance_var)
        balance_entry.grid(row=5, column=1, sticky="ew", pady=5)

        # Description
        ttk.Label(form_frame, text="Description:").grid(row=6, column=0, sticky="w", pady=5)
        self.description_var = tk.StringVar()
        description_entry = ttk.Entry(form_frame, textvariable=self.description_var)
        description_entry.grid(row=6, column=1, sticky="ew", pady=5)

        # Error message
        self.error_var = tk.StringVar()
        error_label = ttk.Label(form_frame, textvariable=self.error_var, foreground="red")
        error_label.grid(row=7, column=0, columnspan=2, sticky="ew", pady=5)

        # Form buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=8, column=0, columnspan=2, pady=10)

        self.save_button = ttk.Button(button_frame, text="Save", command=self.save_account, bootstyle=SUCCESS)
        self.save_button.pack(side="left", padx=(0, 5))

        self.cancel_button = ttk.Button(button_frame, text="Cancel", command=self.cancel_edit, bootstyle=SECONDARY)
        self.cancel_button.pack(side="left")

        # Configure grid weights
        form_frame.columnconfigure(1, weight=1)

        # Disable form initially
        self.enable_form(False)

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

    def on_classification_changed(self, event=None):
        """Handle classification selection change"""
        classification = self.classification_var.get()
        if classification:
            from model.account import Account
            types_dict = Account.get_types_for_classification(classification)

            # Flatten the types dictionary to get all types for this classification
            all_types = []
            for category, types in types_dict.items():
                all_types.extend(types)

            # Update the type combobox
            self.type_combo['values'] = all_types
            if all_types:
                self.type_var.set(all_types[0])  # Set first type as default
            else:
                self.type_var.set("")

    def load_accounts(self):
        """Load accounts into the treeview"""
        # Clear existing items
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        try:
            # Get all accounts
            accounts = self.account_model.get_all_accounts()

            # Add to treeview
            for account in accounts:
                self.account_tree.insert(
                    "", "end",
                    values=(
                        account["id"],
                        account.get("account_number", ""),
                        account["name"],
                        account.get("classification", ""),
                        account["type"],
                        account["currency"],
                        f"${account['current_balance']:.2f}"
                    )
                )

            self.status_var.set(f"Loaded {len(accounts)} accounts")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load accounts: {str(e)}")
            self.status_var.set("Error loading accounts")

    def add_account(self):
        """Prepare form for adding a new account"""
        # Clear form
        self.name_var.set("")
        self.classification_var.set("")
        self.type_var.set("")
        self.account_number_var.set("")
        self.currency_var.set("USD")
        self.balance_var.set(0.0)
        self.description_var.set("")
        self.error_var.set("")

        # Clear type combobox
        self.type_combo['values'] = []

        # Enable form
        self.enable_form(True)

        # Set current operation
        self.current_operation = "add"
        self.current_account_id = None

    def edit_account(self):
        """Prepare form for editing an existing account"""
        # Get selected item
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select an account to edit")
            return

        # Get account ID
        account_id = self.account_tree.item(selected[0], "values")[0]

        try:
            # Get account details
            account = self.account_model.get_account(account_id)

            if not account:
                messagebox.showerror("Error", f"Account with ID {account_id} not found")
                return

            # Populate form
            self.name_var.set(account["name"])
            classification = account.get("classification", "")
            self.classification_var.set(classification)

            # Update type combobox based on classification
            if classification:
                self.on_classification_changed()

            self.type_var.set(account["type"])
            self.account_number_var.set(account.get("account_number", ""))
            self.currency_var.set(account["currency"])
            self.balance_var.set(account["opening_balance"])
            self.description_var.set(account.get("description", ""))
            self.error_var.set("")

            # Enable form
            self.enable_form(True)

            # Set current operation
            self.current_operation = "edit"
            self.current_account_id = account_id

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get account details: {str(e)}")

    def delete_account(self):
        """Delete the selected account"""
        # Get selected item
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showerror("Error", "Please select an account to delete")
            return

        # Get account ID and name
        account_id = self.account_tree.item(selected[0], "values")[0]
        account_name = self.account_tree.item(selected[0], "values")[1]

        # Confirm deletion
        if not messagebox.askyesno("Confirm", f"Are you sure you want to delete account '{account_name}'?\n\nThis action cannot be undone and will fail if there are transactions associated with this account."):
            return

        try:
            # Delete account
            self.account_model.delete_account(account_id)

            # Refresh account list
            self.load_accounts()

            # Show success message
            self.status_var.set(f"Deleted account {account_name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete account: {str(e)}")

    def save_account(self):
        """Save the current account (add or edit)"""
        # Clear error
        self.error_var.set("")

        # Validate inputs
        try:
            name = self.name_var.get().strip()
            classification = self.classification_var.get()
            account_type = self.type_var.get()
            account_number = self.account_number_var.get().strip()
            currency = self.currency_var.get()
            opening_balance = self.balance_var.get()
            description = self.description_var.get().strip()

            # Validate required fields
            if not name:
                self.error_var.set("Account name is required")
                return

            if not classification:
                self.error_var.set("Account classification is required")
                return

            if not account_type:
                self.error_var.set("Account type is required")
                return

            if not currency:
                self.error_var.set("Currency is required")
                return

            # Save account
            if self.current_operation == "add":
                # Create new account
                self.account_model.create_account(
                    name=name,
                    account_type=account_type,
                    currency=currency,
                    opening_balance=opening_balance,
                    description=description,
                    classification=classification,
                    account_number=account_number
                )

                self.status_var.set(f"Added new account: {name}")

            elif self.current_operation == "edit":
                # Update existing account
                self.account_model.update_account(
                    account_id=self.current_account_id,
                    name=name,
                    account_type=account_type,
                    currency=currency,
                    description=description
                )

                self.status_var.set(f"Updated account: {name}")

            # Refresh account list
            self.load_accounts()

            # Disable form
            self.enable_form(False)

        except Exception as e:
            self.error_var.set(f"Error: {str(e)}")

    def cancel_edit(self):
        """Cancel the current edit operation"""
        # Disable form
        self.enable_form(False)

        # Clear error
        self.error_var.set("")

    def enable_form(self, enabled):
        """Enable or disable the account form"""
        state = "normal" if enabled else "disabled"

        # Enable/disable form fields
        for widget in self.winfo_children():
            if isinstance(widget, (ttk.Entry, ttk.Combobox)):
                widget.configure(state=state)

        # Enable/disable buttons
        self.save_button.configure(state=state)
        self.cancel_button.configure(state=state)

    def show_context_menu(self, event):
        """Show the context menu on right-click"""
        # Select the item under the cursor
        item = self.account_tree.identify_row(event.y)
        if item:
            # Select the item
            self.account_tree.selection_set(item)
            # Show the context menu
            self.context_menu.post(event.x_root, event.y_root)
