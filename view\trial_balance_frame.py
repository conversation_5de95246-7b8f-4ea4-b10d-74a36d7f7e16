import tkinter as tk
from tkinter import messagebox
from datetime import datetime
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.trial_balance import TrialBalance
from view.components.date_picker import DatePicker


class TrialBalanceFrame(ttk.Frame):
    """Frame for generating and viewing trial balance reports"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Trial Balance - {company_name}"

        # Create model
        self.trial_balance_model = TrialBalance(self.db_path)

        self.create_widgets()
        self.generate_trial_balance()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Parameters frame
        params_frame = ttk.LabelFrame(main_frame, text="Report Parameters", padding=10)
        params_frame.pack(fill="x", pady=(0, 10))

        # As of date
        ttk.Label(params_frame, text="As of Date:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.date_picker = DatePicker(params_frame, date_var=None)
        self.date_picker.grid(row=0, column=1, sticky="w", padx=(0, 10))
        self.date_picker.set_date(datetime.now())

        # Generate button
        generate_button = ttk.Button(
            params_frame,
            text="Generate Trial Balance",
            command=self.generate_trial_balance,
            bootstyle=PRIMARY
        )
        generate_button.grid(row=0, column=2, padx=10)

        # Export button
        export_button = ttk.Button(
            params_frame,
            text="Export to CSV",
            command=self.export_trial_balance,
            bootstyle=SUCCESS
        )
        export_button.grid(row=0, column=3, padx=5)

        # Validate button
        validate_button = ttk.Button(
            params_frame,
            text="Validate Balance",
            command=self.validate_trial_balance,
            bootstyle=INFO
        )
        validate_button.grid(row=0, column=4, padx=5)

        # Trial Balance Treeview
        tree_frame = ttk.LabelFrame(main_frame, text="Trial Balance", padding=10)
        tree_frame.pack(fill="both", expand=True)

        # Create treeview
        columns = ("account_number", "account_name", "classification", "debit", "credit")
        self.trial_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

        # Define headings
        self.trial_tree.heading("account_number", text="Account #")
        self.trial_tree.heading("account_name", text="Account Name")
        self.trial_tree.heading("classification", text="Classification")
        self.trial_tree.heading("debit", text="Debit")
        self.trial_tree.heading("credit", text="Credit")

        # Define columns
        self.trial_tree.column("account_number", width=100, stretch=False)
        self.trial_tree.column("account_name", width=200)
        self.trial_tree.column("classification", width=120)
        self.trial_tree.column("debit", width=120, anchor="e")
        self.trial_tree.column("credit", width=120, anchor="e")

        # Add scrollbars
        scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical", command=self.trial_tree.yview)
        scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.trial_tree.xview)
        self.trial_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Pack treeview and scrollbars
        self.trial_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")

        # Summary frame
        summary_frame = ttk.LabelFrame(main_frame, text="Summary", padding=10)
        summary_frame.pack(fill="x", pady=(10, 0))

        # Summary labels
        self.total_debits_var = tk.StringVar(value="Total Debits: $0.00")
        self.total_credits_var = tk.StringVar(value="Total Credits: $0.00")
        self.difference_var = tk.StringVar(value="Difference: $0.00")
        self.status_var = tk.StringVar(value="Ready")

        ttk.Label(summary_frame, textvariable=self.total_debits_var, font=("Segoe UI", 10, "bold")).pack(side="left", padx=(0, 20))
        ttk.Label(summary_frame, textvariable=self.total_credits_var, font=("Segoe UI", 10, "bold")).pack(side="left", padx=(0, 20))
        ttk.Label(summary_frame, textvariable=self.difference_var, font=("Segoe UI", 10, "bold")).pack(side="left", padx=(0, 20))

        # Status bar
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

    def generate_trial_balance(self):
        """Generate and display the trial balance"""
        # Clear existing items
        for item in self.trial_tree.get_children():
            self.trial_tree.delete(item)

        try:
            # Get the selected date
            as_of_date = self.date_picker.get_date()
            if as_of_date:
                as_of_date_str = as_of_date.strftime("%Y-%m-%d")
            else:
                as_of_date_str = datetime.now().strftime("%Y-%m-%d")

            # Generate trial balance
            trial_balance = self.trial_balance_model.generate_trial_balance(as_of_date_str)

            # Add accounts to treeview
            for account in trial_balance['accounts']:
                if account['account_name'] == 'TOTALS':
                    # Add totals row with different styling
                    item = self.trial_tree.insert(
                        "", "end",
                        values=(
                            "",
                            "TOTALS",
                            "",
                            f"${account['debit_balance']:.2f}",
                            f"${account['credit_balance']:.2f}"
                        ),
                        tags=("totals",)
                    )
                else:
                    # Add regular account row
                    debit_str = f"${account['debit_balance']:.2f}" if account['debit_balance'] > 0 else ""
                    credit_str = f"${account['credit_balance']:.2f}" if account['credit_balance'] > 0 else ""
                    
                    self.trial_tree.insert(
                        "", "end",
                        values=(
                            account['account_number'],
                            account['account_name'],
                            account['classification'],
                            debit_str,
                            credit_str
                        )
                    )

            # Configure totals row styling
            self.trial_tree.tag_configure("totals", background="#e6f3ff", font=("Segoe UI", 10, "bold"))

            # Update summary
            self.total_debits_var.set(f"Total Debits: ${trial_balance['total_debits']:.2f}")
            self.total_credits_var.set(f"Total Credits: ${trial_balance['total_credits']:.2f}")
            
            difference = trial_balance['difference']
            if abs(difference) < 0.01:
                self.difference_var.set("Difference: $0.00 ✓")
            else:
                self.difference_var.set(f"Difference: ${difference:.2f} ✗")

            # Update status
            balance_status = "balanced" if trial_balance['is_balanced'] else "out of balance"
            self.status_var.set(f"Trial balance as of {trial_balance['as_of_date']} - {balance_status}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate trial balance: {str(e)}")
            self.status_var.set("Error generating trial balance")

    def validate_trial_balance(self):
        """Validate the trial balance"""
        try:
            # Get the selected date
            as_of_date = self.date_picker.get_date()
            if as_of_date:
                as_of_date_str = as_of_date.strftime("%Y-%m-%d")
            else:
                as_of_date_str = datetime.now().strftime("%Y-%m-%d")

            # Validate trial balance
            validation = self.trial_balance_model.validate_trial_balance(as_of_date_str)

            if validation['is_balanced']:
                message = f"✓ Trial Balance is BALANCED!\n\n"
                message += f"Total Debits: ${validation['total_debits']:.2f}\n"
                message += f"Total Credits: ${validation['total_credits']:.2f}\n"
                message += f"Difference: ${validation['difference']:.2f}"
                messagebox.showinfo("Validation Result", message)
            else:
                message = f"✗ Trial Balance is OUT OF BALANCE!\n\n"
                message += f"Total Debits: ${validation['total_debits']:.2f}\n"
                message += f"Total Credits: ${validation['total_credits']:.2f}\n"
                message += f"Difference: ${validation['difference']:.2f}\n\n"
                message += "Please review your transactions for errors."
                messagebox.showwarning("Validation Result", message)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to validate trial balance: {str(e)}")

    def export_trial_balance(self):
        """Export trial balance to CSV"""
        from tkinter import filedialog
        
        # Get the selected date
        as_of_date = self.date_picker.get_date()
        if as_of_date:
            as_of_date_str = as_of_date.strftime("%Y-%m-%d")
            default_filename = f"trial_balance_{as_of_date_str}.csv"
        else:
            default_filename = "trial_balance.csv"

        file_path = filedialog.asksaveasfilename(
            title="Export Trial Balance",
            defaultextension=".csv",
            initialvalue=default_filename,
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.trial_balance_model.export_trial_balance_to_csv(file_path, as_of_date_str)
                messagebox.showinfo("Success", f"Trial balance exported to {file_path}")
                self.status_var.set("Trial balance exported successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export trial balance: {str(e)}")
