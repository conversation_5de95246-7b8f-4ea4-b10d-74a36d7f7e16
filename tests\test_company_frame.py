import unittest
import tkinter as tk
from unittest.mock import MagicMock, patch

# Import modules to test
from view.company_frame import CompanyFrame


class TestCompanyFrame(unittest.TestCase):
    def setUp(self):
        # Create a root window for testing
        self.root = tk.Tk()
        
        # Create a mock transaction manager
        self.transaction_manager = MagicMock()
        self.transaction_manager.get_transactions.return_value = []
        self.transaction_manager.get_balance_summary.return_value = {
            'income': 1000.0,
            'expenses': 500.0,
            'balance': 500.0
        }
        
        # Create a mock close callback
        self.close_callback = MagicMock()
        
        # Create the company frame
        self.company_frame = CompanyFrame(
            self.root,
            "Test Company",
            self.transaction_manager,
            self.close_callback
        )
    
    def tearDown(self):
        # Destroy the root window
        self.root.destroy()
    
    def test_frame_initialization(self):
        """Test that the frame initializes correctly"""
        # Check that the frame has the correct attributes
        self.assertEqual(self.company_frame.company_name, "Test Company")
        self.assertEqual(self.company_frame.transaction_manager, self.transaction_manager)
        self.assertEqual(self.company_frame.close_callback, self.close_callback)
        
        # Check that the transaction manager methods were called
        self.transaction_manager.get_transactions.assert_called()
        self.transaction_manager.get_balance_summary.assert_called()
    
    def test_summary_display(self):
        """Test that the summary information is displayed correctly"""
        # Check the summary labels
        self.assertEqual(self.company_frame.income_label.cget("text"), "$1000.00")
        self.assertEqual(self.company_frame.expenses_label.cget("text"), "$500.00")
        self.assertEqual(self.company_frame.balance_label.cget("text"), "$500.00")
        
        # Check that the balance label has the correct color (green for positive balance)
        self.assertEqual(self.company_frame.balance_label.cget("foreground"), "green")
    
    def test_toggle_search_panel(self):
        """Test the toggle_search_panel method"""
        # Initially, the search panel should be hidden
        self.assertFalse(self.company_frame.search_panel_visible)
        
        # Toggle the search panel
        self.company_frame.toggle_search_panel()
        
        # Now the search panel should be visible
        self.assertTrue(self.company_frame.search_panel_visible)
        
        # Toggle the search panel again
        self.company_frame.toggle_search_panel()
        
        # Now the search panel should be hidden again
        self.assertFalse(self.company_frame.search_panel_visible)
    
    def test_toggle_filter_panel(self):
        """Test the toggle_filter_panel method"""
        # Initially, the filter panel should be hidden
        self.assertFalse(self.company_frame.filter_panel_visible)
        
        # Toggle the filter panel
        self.company_frame.toggle_filter_panel()
        
        # Now the filter panel should be visible
        self.assertTrue(self.company_frame.filter_panel_visible)
        
        # Toggle the filter panel again
        self.company_frame.toggle_filter_panel()
        
        # Now the filter panel should be hidden again
        self.assertFalse(self.company_frame.filter_panel_visible)
    
    def test_reset_filters(self):
        """Test the reset_filters method"""
        # Set some filter values
        self.company_frame.search_var.set("test")
        self.company_frame.from_date_var.set("2025-01-01")
        self.company_frame.to_date_var.set("2025-01-31")
        self.company_frame.account_filter_var.set("Test Account")
        self.company_frame.category_filter_var.set("Test Category")
        self.company_frame.type_filter_var.set("Income")
        self.company_frame.status_filter_var.set("Categorized")
        
        # Reset the filters
        self.company_frame.reset_filters()
        
        # Check that all filter values are reset
        self.assertEqual(self.company_frame.search_var.get(), "")
        self.assertEqual(self.company_frame.from_date_var.get(), "")
        self.assertEqual(self.company_frame.to_date_var.get(), "")
        self.assertEqual(self.company_frame.account_filter_var.get(), "All Accounts")
        self.assertEqual(self.company_frame.category_filter_var.get(), "All Categories")
        self.assertEqual(self.company_frame.type_filter_var.get(), "All Types")
        self.assertEqual(self.company_frame.status_filter_var.get(), "All")
    
    @patch('datetime.datetime')
    def test_quick_search(self, mock_datetime):
        """Test the quick_search method"""
        # Mock the datetime.now() method
        mock_now = MagicMock()
        mock_now.year = 2025
        mock_now.month = 5
        mock_now.day = 15
        mock_now.weekday.return_value = 2  # Wednesday
        mock_datetime.now.return_value = mock_now
        mock_datetime.timedelta = tk.datetime.timedelta
        
        # Test "today" quick search
        self.company_frame.quick_search("today")
        self.assertEqual(self.company_frame.from_date_var.get(), "2025-05-15")
        self.assertEqual(self.company_frame.to_date_var.get(), "2025-05-15")
        
        # Test "week" quick search
        self.company_frame.quick_search("week")
        self.assertEqual(self.company_frame.from_date_var.get(), "2025-05-13")  # Monday
        self.assertEqual(self.company_frame.to_date_var.get(), "2025-05-19")    # Sunday
        
        # Test "month" quick search
        self.company_frame.quick_search("month")
        self.assertEqual(self.company_frame.from_date_var.get(), "2025-05-01")  # First day of month
        self.assertEqual(self.company_frame.to_date_var.get(), "2025-05-31")    # Last day of month
    
    def test_add_column_separators(self):
        """Test the add_column_separators method"""
        # Call the method
        self.company_frame.add_column_separators()
        
        # Check that the tree has the correct style
        self.assertEqual(self.company_frame.tree.cget("style"), "Custom.Treeview")
    
    def test_filter_transactions(self):
        """Test the filter_transactions method with various filters"""
        # Set up mock transactions
        self.transaction_manager.get_transactions.return_value = [
            {
                'id': 1,
                'date': '2025-01-01',
                'description': 'Test Income',
                'amount': 1000.0,
                'category_name': 'Salary',
                'type': 'income',
                'account_name': 'Bank',
                'account_id': 1
            },
            {
                'id': 2,
                'date': '2025-01-02',
                'description': 'Test Expense',
                'amount': 500.0,
                'category_name': 'Food',
                'type': 'expense',
                'account_name': 'Cash',
                'account_id': 2
            },
            {
                'id': 3,
                'date': '2025-01-03',
                'description': 'Uncategorized',
                'amount': 200.0,
                'category_name': '',
                'type': 'expense',
                'account_name': 'Cash',
                'account_id': 2
            }
        ]
        
        # Create a mock account map
        self.company_frame.account_map_by_name = {'Bank': 1, 'Cash': 2}
        
        # Test search filter
        self.company_frame.search_var.set("income")
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 1 transactions")
        
        # Test date filter
        self.company_frame.search_var.set("")
        self.company_frame.from_date_var.set("2025-01-02")
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 2 transactions")
        
        # Test account filter
        self.company_frame.from_date_var.set("")
        self.company_frame.account_filter_var.set("Bank")
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 1 transactions")
        
        # Test category filter
        self.company_frame.account_filter_var.set("All Accounts")
        self.company_frame.category_filter_var.set("Food")
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 1 transactions")
        
        # Test type filter
        self.company_frame.category_filter_var.set("All Categories")
        self.company_frame.type_filter_var.set("Income")
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 1 transactions")
        
        # Test status filter (uncategorized)
        self.company_frame.type_filter_var.set("All Types")
        self.company_frame.status_filter_var.set("Uncategorized")
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 1 transactions")
        
        # Reset all filters
        self.company_frame.reset_filters()
        self.company_frame.filter_transactions()
        self.assertEqual(self.company_frame.status_var.get(), "Found 3 transactions")


if __name__ == '__main__':
    unittest.main()
