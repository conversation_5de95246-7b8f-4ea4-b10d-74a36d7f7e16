import tkinter as tk
from datetime import datetime
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.account import Account
from model.invoice import Invoice
from model.invoice_payment import InvoicePayment
from model.client import Client
from model.category import Category
from model.transaction import TransactionManager
from view.components.date_picker import DatePicker


class InvoicePaymentFrame(ttk.Frame):
    """Frame for processing invoice payments"""

    def __init__(self, parent, company_name, db_path, close_callback, invoice_id):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.invoice_id = invoice_id
        self.title = f"Record Payment - {company_name}"

        # Initialize models
        self.invoice_model = Invoice(db_path)
        self.invoice_payment_model = InvoicePayment(db_path)
        self.account_model = Account(db_path)
        self.client_model = Client(db_path)
        self.category_model = Category(db_path)
        self.transaction_manager = TransactionManager(db_path)

        # Load invoice data
        self.invoice_data = self.invoice_model.get_by_id(invoice_id)
        if not self.invoice_data:
            messagebox.showerror("Error", f"Invoice with ID {invoice_id} not found")
            self.close_callback()
            return

        # Load client data
        self.client_data = self.client_model.get_by_id(self.invoice_data['client_id'])
        if not self.client_data:
            messagebox.showerror("Error", f"Client with ID {self.invoice_data['client_id']} not found")
            self.close_callback()
            return

        # Calculate remaining balance
        self.remaining_balance = self.invoice_payment_model.get_remaining_balance(invoice_id)

        # Create UI
        self.create_widgets()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with title and close button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(header_frame, text="Record Invoice Payment", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")

        close_button = ttk.Button(header_frame, text="Close", command=self.close_callback, bootstyle=DANGER)
        close_button.pack(side="right")

        # Invoice details section
        details_frame = ttk.LabelFrame(main_frame, text="Invoice Details", padding=10)
        details_frame.pack(fill="x", pady=(0, 20))

        # Invoice number and client
        invoice_info_frame = ttk.Frame(details_frame)
        invoice_info_frame.pack(fill="x", pady=5)

        ttk.Label(invoice_info_frame, text="Invoice Number:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Label(invoice_info_frame, text=self.invoice_data['invoice_number'], font=("Segoe UI", 10, "bold")).grid(row=0, column=1, sticky="w", padx=5, pady=2)

        ttk.Label(invoice_info_frame, text="Client:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        client_name = self.client_data['name']
        if self.client_data.get('company_name'):
            client_name += f" ({self.client_data['company_name']})"
        ttk.Label(invoice_info_frame, text=client_name).grid(row=1, column=1, sticky="w", padx=5, pady=2)

        # Invoice amount and balance
        amount_frame = ttk.Frame(details_frame)
        amount_frame.pack(fill="x", pady=5)

        ttk.Label(amount_frame, text="Invoice Total:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Label(amount_frame, text=f"${self.invoice_data['total_amount']:.2f}", font=("Segoe UI", 10, "bold")).grid(row=0, column=1, sticky="w", padx=5, pady=2)

        ttk.Label(amount_frame, text="Amount Paid:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        amount_paid = self.invoice_data['total_amount'] - self.remaining_balance
        ttk.Label(amount_frame, text=f"${amount_paid:.2f}").grid(row=1, column=1, sticky="w", padx=5, pady=2)

        ttk.Label(amount_frame, text="Remaining Balance:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        ttk.Label(amount_frame, text=f"${self.remaining_balance:.2f}", font=("Segoe UI", 10, "bold"), foreground="red").grid(row=2, column=1, sticky="w", padx=5, pady=2)

        # Payment form
        payment_frame = ttk.LabelFrame(main_frame, text="Payment Details", padding=10)
        payment_frame.pack(fill="x", pady=(0, 20))

        # Payment date
        date_frame = ttk.Frame(payment_frame)
        date_frame.pack(fill="x", pady=5)

        ttk.Label(date_frame, text="Payment Date:").pack(side="left", padx=5)
        self.date_picker = DatePicker(date_frame)
        self.date_picker.pack(side="left", padx=5)
        self.date_picker.set_date(datetime.now().date())

        # Payment amount
        amount_frame = ttk.Frame(payment_frame)
        amount_frame.pack(fill="x", pady=5)

        ttk.Label(amount_frame, text="Payment Amount:").pack(side="left", padx=5)
        self.amount_var = tk.DoubleVar(value=self.remaining_balance)
        self.amount_entry = ttk.Entry(amount_frame, textvariable=self.amount_var, width=15)
        self.amount_entry.pack(side="left", padx=5)

        # Quick buttons for full payment
        full_payment_button = ttk.Button(
            amount_frame,
            text="Full Payment",
            command=self.set_full_payment,
            bootstyle=SUCCESS,
            width=12
        )
        full_payment_button.pack(side="left", padx=5)

        # Payment method
        method_frame = ttk.Frame(payment_frame)
        method_frame.pack(fill="x", pady=5)

        ttk.Label(method_frame, text="Payment Method:").pack(side="left", padx=5)
        self.payment_method_var = tk.StringVar()
        payment_methods = ["Cash", "Check", "Credit Card", "Bank Transfer", "PayPal", "Other"]
        self.payment_method_combo = ttk.Combobox(
            method_frame,
            textvariable=self.payment_method_var,
            values=payment_methods,
            width=15
        )
        self.payment_method_combo.pack(side="left", padx=5)
        self.payment_method_combo.current(0)

        # Account selection
        account_frame = ttk.Frame(payment_frame)
        account_frame.pack(fill="x", pady=5)

        ttk.Label(account_frame, text="Deposit to Account:").pack(side="left", padx=5)
        self.account_var = tk.StringVar()
        
        # Get accounts
        accounts = self.account_model.get_all_accounts()
        self.accounts = accounts
        account_names = [account['name'] for account in accounts]
        
        self.account_combo = ttk.Combobox(
            account_frame,
            textvariable=self.account_var,
            values=account_names,
            width=20
        )
        self.account_combo.pack(side="left", padx=5)
        if account_names:
            self.account_combo.current(0)

        # Notes
        notes_frame = ttk.Frame(payment_frame)
        notes_frame.pack(fill="x", pady=5)

        ttk.Label(notes_frame, text="Notes:").pack(side="left", padx=5)
        self.notes_var = tk.StringVar()
        self.notes_entry = ttk.Entry(notes_frame, textvariable=self.notes_var, width=40)
        self.notes_entry.pack(side="left", padx=5, fill="x", expand=True)

        # Error message
        self.error_var = tk.StringVar()
        error_label = ttk.Label(main_frame, textvariable=self.error_var, foreground="red")
        error_label.pack(fill="x", pady=5)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=10)

        process_button = ttk.Button(
            button_frame,
            text="Process Payment",
            command=self.process_payment,
            bootstyle=SUCCESS,
            width=20
        )
        process_button.pack(side="right", padx=5)

        cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self.close_callback,
            bootstyle=SECONDARY,
            width=10
        )
        cancel_button.pack(side="right", padx=5)

    def set_full_payment(self):
        """Set the payment amount to the full remaining balance"""
        self.amount_var.set(self.remaining_balance)

    def process_payment(self):
        """Process the payment"""
        # Clear error message
        self.error_var.set("")

        # Validate payment amount
        try:
            payment_amount = self.amount_var.get()
            if payment_amount <= 0:
                self.error_var.set("Payment amount must be greater than zero")
                return

            if payment_amount > self.remaining_balance:
                self.error_var.set(f"Payment amount cannot exceed remaining balance (${self.remaining_balance:.2f})")
                return
        except:
            self.error_var.set("Invalid payment amount")
            return

        # Validate payment date
        payment_date = self.date_picker.get_date()
        if not payment_date:
            self.error_var.set("Please select a valid payment date")
            return

        # Validate payment method
        payment_method = self.payment_method_var.get()
        if not payment_method:
            self.error_var.set("Please select a payment method")
            return

        # Validate account
        account_name = self.account_var.get()
        if not account_name:
            self.error_var.set("Please select an account")
            return

        # Get account ID
        account_id = None
        for account in self.accounts:
            if account['name'] == account_name:
                account_id = account['id']
                break

        if not account_id:
            self.error_var.set("Invalid account selected")
            return

        # Get notes
        notes = self.notes_var.get()

        # Create transaction
        try:
            # Format date for transaction
            payment_date_str = payment_date.strftime("%Y-%m-%d")

            # Create transaction description
            client_name = self.client_data['name']
            if self.client_data.get('company_name'):
                client_name = self.client_data['company_name']

            description = f"Payment received for Invoice {self.invoice_data['invoice_number']} from {client_name}"
            if notes:
                description += f" - {notes}"

            # Create transaction
            transaction_id = self.transaction_manager.add_transaction(
                date=payment_date_str,
                amount=payment_amount,
                account_id=account_id,
                description=description,
                category_id=None,  # Could be linked to a specific income category if needed
                type_name="income",
                reconciled=0
            )

            # Create payment record
            payment_data = {
                'invoice_id': self.invoice_id,
                'amount': payment_amount,
                'payment_date': payment_date_str,
                'payment_method': payment_method,
                'notes': notes
            }

            payment_id = self.invoice_payment_model.add_payment(payment_data, transaction_id)

            # Show success message
            messagebox.showinfo("Success", f"Payment of ${payment_amount:.2f} recorded successfully")

            # Return to previous screen
            self.close_callback()
        except Exception as e:
            self.error_var.set(f"Error processing payment: {str(e)}")
