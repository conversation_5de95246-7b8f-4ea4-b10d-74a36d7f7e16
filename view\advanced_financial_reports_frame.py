import tkinter as tk
from tkinter import messagebox, filedialog
from datetime import datetime, timedelta
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.advanced_financial_reports import AdvancedFinancialReports
from view.components.date_picker import DatePicker


class AdvancedFinancialReportsFrame(ttk.Frame):
    """Frame for generating and viewing advanced financial reports"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Advanced Financial Reports - {company_name}"

        # Create model
        self.reports_model = AdvancedFinancialReports(self.db_path)

        # Current report data
        self.current_report_data = None
        self.current_report_type = None

        self.create_widgets()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Report selection and parameters
        params_frame = ttk.LabelFrame(main_frame, text="Report Parameters", padding=10)
        params_frame.pack(fill="x", pady=(0, 10))

        # Report type selection
        ttk.Label(params_frame, text="Report Type:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.report_type_var = tk.StringVar(value="Balance Sheet")
        report_types = ["Balance Sheet", "Income Statement", "Cash Flow Statement", "Comparative Balance Sheet", "Financial Ratios"]
        report_combo = ttk.Combobox(params_frame, textvariable=self.report_type_var, values=report_types, state="readonly", width=20)
        report_combo.grid(row=0, column=1, sticky="w", padx=(0, 10))
        report_combo.bind("<<ComboboxSelected>>", self.on_report_type_changed)

        # Date parameters
        ttk.Label(params_frame, text="As of Date:").grid(row=0, column=2, sticky="w", padx=(10, 5))
        self.as_of_date_picker = DatePicker(params_frame)
        self.as_of_date_picker.grid(row=0, column=3, sticky="w", padx=(0, 10))
        self.as_of_date_picker.set_date(datetime.now())

        # Period dates (for income statement and cash flow)
        ttk.Label(params_frame, text="From Date:").grid(row=1, column=0, sticky="w", padx=(0, 5))
        self.start_date_picker = DatePicker(params_frame)
        self.start_date_picker.grid(row=1, column=1, sticky="w", padx=(0, 10))
        self.start_date_picker.set_date(datetime.now() - timedelta(days=365))

        ttk.Label(params_frame, text="To Date:").grid(row=1, column=2, sticky="w", padx=(0, 5))
        self.end_date_picker = DatePicker(params_frame)
        self.end_date_picker.grid(row=1, column=3, sticky="w", padx=(0, 10))
        self.end_date_picker.set_date(datetime.now())

        # Prior date (for comparative reports)
        ttk.Label(params_frame, text="Prior Date:").grid(row=1, column=4, sticky="w", padx=(10, 5))
        self.prior_date_picker = DatePicker(params_frame)
        self.prior_date_picker.grid(row=1, column=5, sticky="w", padx=(0, 10))
        self.prior_date_picker.set_date(datetime.now() - timedelta(days=365))

        # Generate button
        generate_button = ttk.Button(
            params_frame,
            text="Generate Report",
            command=self.generate_report,
            bootstyle=PRIMARY
        )
        generate_button.grid(row=0, column=6, rowspan=2, padx=20, sticky="ns")

        # Export button
        export_button = ttk.Button(
            params_frame,
            text="Export",
            command=self.export_report,
            bootstyle=SUCCESS
        )
        export_button.grid(row=0, column=7, rowspan=2, padx=5, sticky="ns")

        # Report display area
        display_frame = ttk.LabelFrame(main_frame, text="Report", padding=10)
        display_frame.pack(fill="both", expand=True)

        # Create notebook for different report views
        self.report_notebook = ttk.Notebook(display_frame)
        self.report_notebook.pack(fill="both", expand=True)

        # Summary tab
        self.summary_frame = ttk.Frame(self.report_notebook)
        self.report_notebook.add(self.summary_frame, text="Summary")

        # Details tab
        self.details_frame = ttk.Frame(self.report_notebook)
        self.report_notebook.add(self.details_frame, text="Details")

        # Create text widgets for report display
        self.create_report_display_widgets()

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

        # Initialize with default report type
        self.on_report_type_changed()

    def create_report_display_widgets(self):
        """Create widgets for displaying reports"""
        # Summary display
        self.summary_text = tk.Text(self.summary_frame, wrap=tk.WORD, font=("Consolas", 10))
        summary_scrollbar = ttk.Scrollbar(self.summary_frame, orient="vertical", command=self.summary_text.yview)
        self.summary_text.configure(yscrollcommand=summary_scrollbar.set)
        
        self.summary_text.pack(side="left", fill="both", expand=True)
        summary_scrollbar.pack(side="right", fill="y")

        # Details display (treeview for detailed data)
        columns = ("item", "amount", "percentage")
        self.details_tree = ttk.Treeview(self.details_frame, columns=columns, show="tree headings", height=15)
        
        self.details_tree.heading("#0", text="Account/Item")
        self.details_tree.heading("item", text="Description")
        self.details_tree.heading("amount", text="Amount")
        self.details_tree.heading("percentage", text="% of Total")
        
        self.details_tree.column("#0", width=200)
        self.details_tree.column("item", width=200)
        self.details_tree.column("amount", width=120, anchor="e")
        self.details_tree.column("percentage", width=100, anchor="e")

        details_scrollbar = ttk.Scrollbar(self.details_frame, orient="vertical", command=self.details_tree.yview)
        self.details_tree.configure(yscrollcommand=details_scrollbar.set)
        
        self.details_tree.pack(side="left", fill="both", expand=True)
        details_scrollbar.pack(side="right", fill="y")

    def on_report_type_changed(self, event=None):
        """Handle report type selection change"""
        report_type = self.report_type_var.get()
        
        # Show/hide date controls based on report type
        if report_type in ["Income Statement", "Cash Flow Statement"]:
            # Show period dates
            self.start_date_picker.grid()
            self.end_date_picker.grid()
        else:
            # Hide period dates for balance sheet and ratios
            pass
        
        if report_type == "Comparative Balance Sheet":
            # Show prior date
            self.prior_date_picker.grid()
        else:
            # Hide prior date for other reports
            pass

    def generate_report(self):
        """Generate the selected report"""
        try:
            report_type = self.report_type_var.get()
            self.current_report_type = report_type
            
            if report_type == "Balance Sheet":
                as_of_date = self.as_of_date_picker.get_date().strftime("%Y-%m-%d")
                self.current_report_data = self.reports_model.generate_balance_sheet(as_of_date)
                self.display_balance_sheet()
                
            elif report_type == "Income Statement":
                start_date = self.start_date_picker.get_date().strftime("%Y-%m-%d")
                end_date = self.end_date_picker.get_date().strftime("%Y-%m-%d")
                self.current_report_data = self.reports_model.generate_income_statement(start_date, end_date)
                self.display_income_statement()
                
            elif report_type == "Cash Flow Statement":
                start_date = self.start_date_picker.get_date().strftime("%Y-%m-%d")
                end_date = self.end_date_picker.get_date().strftime("%Y-%m-%d")
                self.current_report_data = self.reports_model.generate_cash_flow_statement(start_date, end_date)
                self.display_cash_flow_statement()
                
            elif report_type == "Comparative Balance Sheet":
                current_date = self.as_of_date_picker.get_date().strftime("%Y-%m-%d")
                prior_date = self.prior_date_picker.get_date().strftime("%Y-%m-%d")
                self.current_report_data = self.reports_model.generate_comparative_balance_sheet(current_date, prior_date)
                self.display_comparative_balance_sheet()
                
            elif report_type == "Financial Ratios":
                as_of_date = self.as_of_date_picker.get_date().strftime("%Y-%m-%d")
                self.current_report_data = self.reports_model.calculate_financial_ratios(as_of_date)
                self.display_financial_ratios()
            
            self.status_var.set(f"{report_type} generated successfully")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")
            self.status_var.set("Error generating report")

    def display_balance_sheet(self):
        """Display balance sheet in the UI"""
        data = self.current_report_data
        
        # Clear previous content
        self.summary_text.delete(1.0, tk.END)
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # Summary display
        summary = f"""
{self.company_name}
BALANCE SHEET
As of {data['as_of_date']}

ASSETS
Current Assets:                     ${sum(item['balance'] for item in data['assets']['current_assets']):,.2f}
Fixed Assets:                       ${sum(item['balance'] for item in data['assets']['fixed_assets']):,.2f}
                                   ________________
Total Assets:                       ${data['assets']['total_assets']:,.2f}

LIABILITIES AND EQUITY
Current Liabilities:                ${sum(item['balance'] for item in data['liabilities']['current_liabilities']):,.2f}
Long-term Liabilities:              ${sum(item['balance'] for item in data['liabilities']['long_term_liabilities']):,.2f}
Total Liabilities:                  ${data['liabilities']['total_liabilities']:,.2f}

Equity:                             ${data['equity']['total_equity']:,.2f}
                                   ________________
Total Liabilities and Equity:       ${data['total_liabilities_and_equity']:,.2f}

Balance Check: {'BALANCED' if data['is_balanced'] else 'UNBALANCED'}
"""
        self.summary_text.insert(1.0, summary)
        
        # Details display
        # Assets
        assets_item = self.details_tree.insert("", "end", text="ASSETS", values=("", f"${data['assets']['total_assets']:,.2f}", "100.0%"), open=True)
        
        if data['assets']['current_assets']:
            current_assets_item = self.details_tree.insert(assets_item, "end", text="Current Assets", values=("", "", ""), open=True)
            for asset in data['assets']['current_assets']:
                self.details_tree.insert(current_assets_item, "end", text=asset['name'], 
                                       values=(asset['type'], f"${asset['balance']:,.2f}", 
                                              f"{(asset['balance']/data['assets']['total_assets']*100):,.1f}%" if data['assets']['total_assets'] > 0 else "0.0%"))
        
        if data['assets']['fixed_assets']:
            fixed_assets_item = self.details_tree.insert(assets_item, "end", text="Fixed Assets", values=("", "", ""), open=True)
            for asset in data['assets']['fixed_assets']:
                self.details_tree.insert(fixed_assets_item, "end", text=asset['name'], 
                                       values=(asset['type'], f"${asset['balance']:,.2f}", 
                                              f"{(asset['balance']/data['assets']['total_assets']*100):,.1f}%" if data['assets']['total_assets'] > 0 else "0.0%"))

    def display_income_statement(self):
        """Display income statement in the UI"""
        data = self.current_report_data
        
        # Clear previous content
        self.summary_text.delete(1.0, tk.END)
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # Summary display
        summary = f"""
{self.company_name}
INCOME STATEMENT
For the Period {data['period_start']} to {data['period_end']}

REVENUE
Total Revenue:                      ${data['revenue']['total_revenue']:,.2f}

EXPENSES
Cost of Goods Sold:                ${sum(item['amount'] for item in data['expenses']['cost_of_goods_sold']):,.2f}
                                   ________________
Gross Profit:                       ${data['gross_profit']:,.2f}

Operating Expenses:                 ${sum(item['amount'] for item in data['expenses']['operating_expenses']):,.2f}
                                   ________________
Operating Income:                   ${data['operating_income']:,.2f}

Other Expenses:                     ${sum(item['amount'] for item in data['expenses']['other_expenses']):,.2f}
Administrative Expenses:            ${sum(item['amount'] for item in data['expenses']['administrative_expenses']):,.2f}
                                   ________________
Net Income:                         ${data['net_income']:,.2f}

Gross Profit Margin:                {(data['gross_profit']/data['revenue']['total_revenue']*100):,.1f}%
Net Profit Margin:                  {(data['net_income']/data['revenue']['total_revenue']*100):,.1f}%
"""
        self.summary_text.insert(1.0, summary)

    def display_cash_flow_statement(self):
        """Display cash flow statement in the UI"""
        data = self.current_report_data
        
        # Clear previous content
        self.summary_text.delete(1.0, tk.END)
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # Summary display
        summary = f"""
{self.company_name}
STATEMENT OF CASH FLOWS
For the Period {data['period_start']} to {data['period_end']}

CASH FLOWS FROM OPERATING ACTIVITIES
Net Cash from Operating Activities:  ${data['operating_activities']['total']:,.2f}

CASH FLOWS FROM INVESTING ACTIVITIES
Net Cash from Investing Activities:  ${data['investing_activities']['total']:,.2f}

CASH FLOWS FROM FINANCING ACTIVITIES
Net Cash from Financing Activities: ${data['financing_activities']['total']:,.2f}

                                   ________________
Net Increase (Decrease) in Cash:    ${data['net_cash_flow']:,.2f}

Cash at Beginning of Period:        ${data['beginning_cash']:,.2f}
Cash at End of Period:              ${data['ending_cash']:,.2f}
"""
        self.summary_text.insert(1.0, summary)

    def display_comparative_balance_sheet(self):
        """Display comparative balance sheet in the UI"""
        data = self.current_report_data
        
        # Clear previous content
        self.summary_text.delete(1.0, tk.END)
        
        # Summary display
        summary = f"""
{self.company_name}
COMPARATIVE BALANCE SHEET
Current: {data['current_date']} vs Prior: {data['prior_date']}

                                Current        Prior         Change
ASSETS
Total Assets:                   ${data['current']['assets']['total_assets']:>10,.2f}  ${data['prior']['assets']['total_assets']:>10,.2f}  ${data['changes']['assets']['total_change']:>10,.2f}

LIABILITIES
Total Liabilities:              ${data['current']['liabilities']['total_liabilities']:>10,.2f}  ${data['prior']['liabilities']['total_liabilities']:>10,.2f}  ${data['changes']['liabilities']['total_change']:>10,.2f}

EQUITY
Total Equity:                   ${data['current']['equity']['total_equity']:>10,.2f}  ${data['prior']['equity']['total_equity']:>10,.2f}  ${data['changes']['equity']['total_change']:>10,.2f}
"""
        self.summary_text.insert(1.0, summary)

    def display_financial_ratios(self):
        """Display financial ratios in the UI"""
        data = self.current_report_data
        
        # Clear previous content
        self.summary_text.delete(1.0, tk.END)
        
        # Summary display
        summary = f"""
{self.company_name}
FINANCIAL RATIOS ANALYSIS
As of {self.as_of_date_picker.get_date().strftime("%Y-%m-%d")}

LIQUIDITY RATIOS
Current Ratio:                      {data['liquidity_ratios'].get('current_ratio', 0):,.2f}

PROFITABILITY RATIOS
Gross Profit Margin:                {data['profitability_ratios'].get('gross_profit_margin', 0):,.1f}%
Net Profit Margin:                  {data['profitability_ratios'].get('net_profit_margin', 0):,.1f}%
Return on Assets (ROA):             {data['profitability_ratios'].get('return_on_assets', 0):,.1f}%
Return on Equity (ROE):             {data['profitability_ratios'].get('return_on_equity', 0):,.1f}%

LEVERAGE RATIOS
Debt to Assets:                     {data['leverage_ratios'].get('debt_to_assets', 0):,.1f}%
Debt to Equity:                     {data['leverage_ratios'].get('debt_to_equity', 0):,.1f}%
"""
        self.summary_text.insert(1.0, summary)

    def export_report(self):
        """Export the current report to a file"""
        if not self.current_report_data:
            messagebox.showwarning("Warning", "Please generate a report first")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Export Report",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # Get the content from summary text widget
                    content = self.summary_text.get(1.0, tk.END)
                    f.write(content)
                
                messagebox.showinfo("Success", f"Report exported to {file_path}")
                self.status_var.set("Report exported successfully")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")
