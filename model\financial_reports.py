import datetime

from model.report_framework import FinancialReport


class IncomeStatementReport(FinancialReport):
    """Income statement report showing income, expenses, and net income"""

    def __init__(self, db_path):
        super().__init__(db_path, "Income Statement", "Income statement showing income, expenses, and net income")

    def generate(self):
        """Generate the income statement report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Get income by category
            income_query = """
            SELECT c.name as category_name, SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.type = 'income'
            """

            income_params = []

            # Add filters
            if date_from:
                income_query += " AND t.date >= ?"
                income_params.append(date_from)

            if date_to:
                income_query += " AND t.date <= ?"
                income_params.append(date_to)

            if account_id:
                income_query += " AND t.account_id = ?"
                income_params.append(account_id)

            # Group by category
            income_query += " GROUP BY c.name"

            # Get expenses by category
            expense_query = """
            SELECT c.name as category_name, SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.type = 'expense'
            """

            expense_params = []

            # Add filters
            if date_from:
                expense_query += " AND t.date >= ?"
                expense_params.append(date_from)

            if date_to:
                expense_query += " AND t.date <= ?"
                expense_params.append(date_to)

            if account_id:
                expense_query += " AND t.account_id = ?"
                expense_params.append(account_id)

            # Group by category
            expense_query += " GROUP BY c.name"

            # Get total income
            total_income_query = """
            SELECT SUM(t.amount) as total_income
            FROM transactions t
            WHERE t.type = 'income'
            """

            total_income_params = []

            # Add filters
            if date_from:
                total_income_query += " AND t.date >= ?"
                total_income_params.append(date_from)

            if date_to:
                total_income_query += " AND t.date <= ?"
                total_income_params.append(date_to)

            if account_id:
                total_income_query += " AND t.account_id = ?"
                total_income_params.append(account_id)

            # Get total expenses
            total_expense_query = """
            SELECT SUM(t.amount) as total_expenses
            FROM transactions t
            WHERE t.type = 'expense'
            """

            total_expense_params = []

            # Add filters
            if date_from:
                total_expense_query += " AND t.date >= ?"
                total_expense_params.append(date_from)

            if date_to:
                total_expense_query += " AND t.date <= ?"
                total_expense_params.append(date_to)

            if account_id:
                total_expense_query += " AND t.account_id = ?"
                total_expense_params.append(account_id)

            # Execute queries
            cursor.execute(income_query, income_params)
            income_categories = [dict(row) for row in cursor.fetchall()]

            cursor.execute(expense_query, expense_params)
            expense_categories = [dict(row) for row in cursor.fetchall()]

            cursor.execute(total_income_query, total_income_params)
            total_income = cursor.fetchone()["total_income"] or 0

            cursor.execute(total_expense_query, total_expense_params)
            total_expenses = cursor.fetchone()["total_expenses"] or 0

            # Calculate net income
            net_income = total_income - total_expenses

            # Create report results
            self.results = {
                "income_categories": income_categories,
                "expense_categories": expense_categories,
                "total_income": total_income,
                "total_expenses": total_expenses,
                "net_income": net_income
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating income statement report: {str(e)}")
            return None


class BalanceSheetReport(FinancialReport):
    """Balance sheet report showing assets, liabilities, and equity"""

    def __init__(self, db_path):
        super().__init__(db_path, "Balance Sheet", "Balance sheet showing assets, liabilities, and equity")

    def generate(self):
        """Generate the balance sheet report"""
        # Get parameters
        date = self.get_parameter("date", datetime.datetime.now().strftime("%Y-%m-%d"))

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Get assets (accounts with positive balance)
            assets_query = """
            SELECT a.id, a.name, a.type, a.current_balance
            FROM accounts a
            WHERE a.current_balance >= 0
            """

            # Get liabilities (accounts with negative balance)
            liabilities_query = """
            SELECT a.id, a.name, a.type, a.current_balance
            FROM accounts a
            WHERE a.current_balance < 0
            """

            # Execute queries
            cursor.execute(assets_query)
            assets = [dict(row) for row in cursor.fetchall()]

            cursor.execute(liabilities_query)
            liabilities = [dict(row) for row in cursor.fetchall()]

            # Calculate totals
            total_assets = sum(asset["current_balance"] for asset in assets)
            total_liabilities = sum(abs(liability["current_balance"]) for liability in liabilities)

            # Calculate equity
            equity = total_assets - total_liabilities

            # Create report results
            self.results = {
                "assets": assets,
                "liabilities": liabilities,
                "total_assets": total_assets,
                "total_liabilities": total_liabilities,
                "equity": equity,
                "date": date
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating balance sheet report: {str(e)}")
            return None


class CashFlowReport(FinancialReport):
    """Cash flow report showing cash inflows and outflows"""

    def __init__(self, db_path):
        super().__init__(db_path, "Cash Flow", "Cash flow report showing cash inflows and outflows")

    def generate(self):
        """Generate the cash flow report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Get cash inflows by month
            inflow_query = """
            SELECT strftime('%Y-%m', t.date) as month,
                   SUM(t.amount) as total_amount
            FROM transactions t
            WHERE t.type = 'income'
            """

            inflow_params = []

            # Add filters
            if date_from:
                inflow_query += " AND t.date >= ?"
                inflow_params.append(date_from)

            if date_to:
                inflow_query += " AND t.date <= ?"
                inflow_params.append(date_to)

            if account_id:
                inflow_query += " AND t.account_id = ?"
                inflow_params.append(account_id)

            # Group by month
            inflow_query += " GROUP BY month ORDER BY month"

            # Get cash outflows by month
            outflow_query = """
            SELECT strftime('%Y-%m', t.date) as month,
                   SUM(t.amount) as total_amount
            FROM transactions t
            WHERE t.type = 'expense'
            """

            outflow_params = []

            # Add filters
            if date_from:
                outflow_query += " AND t.date >= ?"
                outflow_params.append(date_from)

            if date_to:
                outflow_query += " AND t.date <= ?"
                outflow_params.append(date_to)

            if account_id:
                outflow_query += " AND t.account_id = ?"
                outflow_params.append(account_id)

            # Group by month
            outflow_query += " GROUP BY month ORDER BY month"

            # Execute queries
            cursor.execute(inflow_query, inflow_params)
            inflows = [dict(row) for row in cursor.fetchall()]

            cursor.execute(outflow_query, outflow_params)
            outflows = [dict(row) for row in cursor.fetchall()]

            # Create a combined result with net cash flow
            months = set()
            for inflow in inflows:
                months.add(inflow["month"])
            for outflow in outflows:
                months.add(outflow["month"])

            months = sorted(list(months))

            # Create a map for quick lookup
            inflow_map = {inflow["month"]: inflow["total_amount"] for inflow in inflows}
            outflow_map = {outflow["month"]: outflow["total_amount"] for outflow in outflows}

            # Create combined results
            combined_results = []
            for month in months:
                inflow_amount = inflow_map.get(month, 0) or 0
                outflow_amount = outflow_map.get(month, 0) or 0
                net_flow = inflow_amount - outflow_amount

                combined_results.append({
                    "month": month,
                    "inflow": inflow_amount,
                    "outflow": outflow_amount,
                    "net_flow": net_flow
                })

            # Calculate totals
            total_inflow = sum(inflow_map.values())
            total_outflow = sum(outflow_map.values())
            total_net_flow = total_inflow - total_outflow

            # Create report results
            self.results = {
                "monthly_flows": combined_results,
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "total_net_flow": total_net_flow
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating cash flow report: {str(e)}")
            return None


class BudgetComparisonReport(FinancialReport):
    """Budget comparison report showing actual vs. budgeted amounts"""

    def __init__(self, db_path):
        super().__init__(db_path, "Budget Comparison", "Budget comparison report showing actual vs. budgeted amounts")

    def generate(self):
        """Generate the budget comparison report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        budget_id = self.get_parameter("budget_id")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Check if budget table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='budgets'")
            if not cursor.fetchone():
                # No budget table, return empty results
                self.results = {
                    "categories": [],
                    "total_budgeted": 0,
                    "total_actual": 0,
                    "total_variance": 0
                }
                return self.results

            # Get budget items
            budget_query = """
            SELECT b.id, b.category_id, c.name as category_name, b.amount as budgeted_amount
            FROM budgets b
            LEFT JOIN categories c ON b.category_id = c.id
            WHERE b.budget_id = ?
            """

            # Get actual expenses by category
            actual_query = """
            SELECT t.category_id, SUM(t.amount) as actual_amount
            FROM transactions t
            WHERE t.type = 'expense'
            """

            actual_params = []

            # Add filters
            if date_from:
                actual_query += " AND t.date >= ?"
                actual_params.append(date_from)

            if date_to:
                actual_query += " AND t.date <= ?"
                actual_params.append(date_to)

            # Group by category
            actual_query += " GROUP BY t.category_id"

            # Execute queries
            cursor.execute(budget_query, (budget_id,))
            budget_items = [dict(row) for row in cursor.fetchall()]

            cursor.execute(actual_query, actual_params)
            actual_expenses = [dict(row) for row in cursor.fetchall()]

            # Create a map for quick lookup
            actual_map = {expense["category_id"]: expense["actual_amount"] for expense in actual_expenses if expense["category_id"]}

            # Create combined results
            categories = []
            total_budgeted = 0
            total_actual = 0

            for item in budget_items:
                category_id = item["category_id"]
                budgeted_amount = item["budgeted_amount"] or 0
                actual_amount = actual_map.get(category_id, 0) or 0
                variance = budgeted_amount - actual_amount

                categories.append({
                    "category_id": category_id,
                    "category_name": item["category_name"],
                    "budgeted_amount": budgeted_amount,
                    "actual_amount": actual_amount,
                    "variance": variance,
                    "variance_percent": (variance / budgeted_amount * 100) if budgeted_amount else 0
                })

                total_budgeted += budgeted_amount
                total_actual += actual_amount

            # Calculate total variance
            total_variance = total_budgeted - total_actual

            # Create report results
            self.results = {
                "categories": categories,
                "total_budgeted": total_budgeted,
                "total_actual": total_actual,
                "total_variance": total_variance,
                "variance_percent": (total_variance / total_budgeted * 100) if total_budgeted else 0
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating budget comparison report: {str(e)}")
            return None


class MonthlyComparisonReport(FinancialReport):
    """Monthly comparison report showing income and expenses by month"""

    def __init__(self, db_path):
        super().__init__(db_path, "Monthly Comparison", "Monthly comparison of income and expenses")

    def generate(self):
        """Generate the monthly comparison report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Get income by month
            income_query = """
            SELECT strftime('%Y-%m', t.date) as month,
                   SUM(t.amount) as total_amount
            FROM transactions t
            WHERE t.type = 'income'
            """

            income_params = []

            # Add filters
            if date_from:
                income_query += " AND t.date >= ?"
                income_params.append(date_from)

            if date_to:
                income_query += " AND t.date <= ?"
                income_params.append(date_to)

            if account_id:
                income_query += " AND t.account_id = ?"
                income_params.append(account_id)

            # Group by month
            income_query += " GROUP BY month ORDER BY month"

            # Get expenses by month
            expense_query = """
            SELECT strftime('%Y-%m', t.date) as month,
                   SUM(t.amount) as total_amount
            FROM transactions t
            WHERE t.type = 'expense'
            """

            expense_params = []

            # Add filters
            if date_from:
                expense_query += " AND t.date >= ?"
                expense_params.append(date_from)

            if date_to:
                expense_query += " AND t.date <= ?"
                expense_params.append(date_to)

            if account_id:
                expense_query += " AND t.account_id = ?"
                expense_params.append(account_id)

            # Group by month
            expense_query += " GROUP BY month ORDER BY month"

            # Execute queries
            cursor.execute(income_query, income_params)
            incomes = [dict(row) for row in cursor.fetchall()]

            cursor.execute(expense_query, expense_params)
            expenses = [dict(row) for row in cursor.fetchall()]

            # Create a combined result
            months = set()
            for income in incomes:
                months.add(income["month"])
            for expense in expenses:
                months.add(expense["month"])

            months = sorted(list(months))

            # Create maps for quick lookup
            income_map = {income["month"]: income["total_amount"] for income in incomes}
            expense_map = {expense["month"]: expense["total_amount"] for expense in expenses}

            # Create combined results
            monthly_data = []
            for month in months:
                income_amount = income_map.get(month, 0) or 0
                expense_amount = expense_map.get(month, 0) or 0
                net_amount = income_amount - expense_amount

                # Format month for display (YYYY-MM to Month YYYY)
                year, month_num = month.split('-')
                month_name = datetime.datetime(int(year), int(month_num), 1).strftime('%B %Y')

                monthly_data.append({
                    "month": month,
                    "month_name": month_name,
                    "income": income_amount,
                    "expense": expense_amount,
                    "net": net_amount
                })

            # Calculate totals
            total_income = sum(income_map.values())
            total_expense = sum(expense_map.values())
            total_net = total_income - total_expense

            # Calculate averages
            num_months = len(months)
            avg_income = total_income / num_months if num_months > 0 else 0
            avg_expense = total_expense / num_months if num_months > 0 else 0
            avg_net = total_net / num_months if num_months > 0 else 0

            # Create report results
            self.results = {
                "monthly_data": monthly_data,
                "total_income": total_income,
                "total_expense": total_expense,
                "total_net": total_net,
                "avg_income": avg_income,
                "avg_expense": avg_expense,
                "avg_net": avg_net,
                "num_months": num_months
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating monthly comparison report: {str(e)}")
            return None


class CategoryBreakdownReport(FinancialReport):
    """Category breakdown report showing income and expenses by category"""

    def __init__(self, db_path):
        super().__init__(db_path, "Category Breakdown", "Breakdown of income and expenses by category")

    def generate(self):
        """Generate the category breakdown report"""
        # Get parameters
        date_from = self.get_parameter("date_from")
        date_to = self.get_parameter("date_to")
        account_id = self.get_parameter("account_id")

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Get income by category
            income_query = """
            SELECT c.id as category_id, c.name as category_name,
                   COUNT(t.id) as transaction_count,
                   SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.type = 'income'
            """

            income_params = []

            # Add filters
            if date_from:
                income_query += " AND t.date >= ?"
                income_params.append(date_from)

            if date_to:
                income_query += " AND t.date <= ?"
                income_params.append(date_to)

            if account_id:
                income_query += " AND t.account_id = ?"
                income_params.append(account_id)

            # Group by category
            income_query += " GROUP BY c.id, c.name ORDER BY total_amount DESC"

            # Get expenses by category
            expense_query = """
            SELECT c.id as category_id, c.name as category_name,
                   COUNT(t.id) as transaction_count,
                   SUM(t.amount) as total_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.type = 'expense'
            """

            expense_params = []

            # Add filters
            if date_from:
                expense_query += " AND t.date >= ?"
                expense_params.append(date_from)

            if date_to:
                expense_query += " AND t.date <= ?"
                expense_params.append(date_to)

            if account_id:
                expense_query += " AND t.account_id = ?"
                expense_params.append(account_id)

            # Group by category
            expense_query += " GROUP BY c.id, c.name ORDER BY total_amount DESC"

            # Execute queries
            cursor.execute(income_query, income_params)
            income_categories = [dict(row) for row in cursor.fetchall()]

            cursor.execute(expense_query, expense_params)
            expense_categories = [dict(row) for row in cursor.fetchall()]

            # Calculate totals
            total_income = sum(category["total_amount"] for category in income_categories)
            total_expense = sum(category["total_amount"] for category in expense_categories)

            # Calculate percentages
            for category in income_categories:
                category["percentage"] = (category["total_amount"] / total_income * 100) if total_income > 0 else 0

            for category in expense_categories:
                category["percentage"] = (category["total_amount"] / total_expense * 100) if total_expense > 0 else 0

            # Create report results
            self.results = {
                "income_categories": income_categories,
                "expense_categories": expense_categories,
                "total_income": total_income,
                "total_expense": total_expense,
                "net_income": total_income - total_expense
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating category breakdown report: {str(e)}")
            return None


class TaxRelatedTransactionsReport(FinancialReport):
    """Tax-related transactions report showing transactions that may be relevant for tax purposes"""

    def __init__(self, db_path):
        super().__init__(db_path, "Tax-Related Transactions", "Transactions that may be relevant for tax purposes")

    def generate(self):
        """Generate the tax-related transactions report"""
        # Get parameters
        tax_year = self.get_parameter("tax_year", datetime.datetime.now().year)
        account_id = self.get_parameter("account_id")

        # Calculate date range for the tax year
        date_from = f"{tax_year}-01-01"
        date_to = f"{tax_year}-12-31"

        # Check if report is in cache
        if self.load_from_cache():
            return self.results

        # Generate report
        try:
            conn, cursor = self._get_connection()

            # Get tax-related categories
            # This is a simplified approach - in a real app, you might have a "tax_related" flag on categories
            tax_categories_query = """
            SELECT id, name, type
            FROM categories
            WHERE name LIKE '%tax%' OR name LIKE '%Tax%' OR
                  name LIKE '%income%' OR name LIKE '%Income%' OR
                  name LIKE '%salary%' OR name LIKE '%Salary%' OR
                  name LIKE '%dividend%' OR name LIKE '%Dividend%' OR
                  name LIKE '%interest%' OR name LIKE '%Interest%' OR
                  name LIKE '%donation%' OR name LIKE '%Donation%' OR
                  name LIKE '%medical%' OR name LIKE '%Medical%' OR
                  name LIKE '%education%' OR name LIKE '%Education%'
            """

            cursor.execute(tax_categories_query)
            tax_categories = [dict(row) for row in cursor.fetchall()]

            # Get category IDs
            tax_category_ids = [category["id"] for category in tax_categories]

            # If no tax categories found, return empty results
            if not tax_category_ids:
                self.results = {
                    "tax_year": tax_year,
                    "income_transactions": [],
                    "expense_transactions": [],
                    "total_income": 0,
                    "total_expenses": 0,
                    "net_income": 0
                }
                return self.results

            # Get tax-related transactions
            transactions_query = """
            SELECT t.id, t.date, t.amount, t.description, t.type,
                   a.name as account_name, c.name as category_name
            FROM transactions t
            LEFT JOIN accounts a ON t.account_id = a.id
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.category_id IN ({}) AND t.date >= ? AND t.date <= ?
            """.format(','.join(['?'] * len(tax_category_ids)))

            params = tax_category_ids + [date_from, date_to]

            # Add account filter if specified
            if account_id:
                transactions_query += " AND t.account_id = ?"
                params.append(account_id)

            # Add order by
            transactions_query += " ORDER BY t.date"

            # Execute query
            cursor.execute(transactions_query, params)
            transactions = [dict(row) for row in cursor.fetchall()]

            # Separate income and expense transactions
            income_transactions = [t for t in transactions if t["type"] == "income"]
            expense_transactions = [t for t in transactions if t["type"] == "expense"]

            # Calculate totals
            total_income = sum(t["amount"] for t in income_transactions)
            total_expenses = sum(t["amount"] for t in expense_transactions)

            # Create report results
            self.results = {
                "tax_year": tax_year,
                "income_transactions": income_transactions,
                "expense_transactions": expense_transactions,
                "total_income": total_income,
                "total_expenses": total_expenses,
                "net_income": total_income - total_expenses
            }

            # Close connection
            conn.close()

            # Save to cache
            self.save_to_cache()

            return self.results

        except Exception as e:
            print(f"Error generating tax-related transactions report: {str(e)}")
            return None
