import os
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
import numpy as np
import pandas as pd
from datetime import datetime


class ChartGenerator:
    """Class for generating charts from report data"""
    
    def __init__(self, output_dir=None):
        self.output_dir = output_dir or os.path.join(os.getcwd(), "reports", "charts")
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_pie_chart(self, data, labels, title, filename, colors=None, explode=None):
        """Generate a pie chart"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create pie chart
            wedges, texts, autotexts = ax.pie(
                data, 
                labels=labels, 
                autopct='%1.1f%%',
                startangle=90,
                colors=colors,
                explode=explode
            )
            
            # Style the chart
            ax.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
            plt.title(title, fontsize=16)
            
            # Style the text
            for text in texts:
                text.set_fontsize(10)
            for autotext in autotexts:
                autotext.set_fontsize(8)
                autotext.set_color('white')
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating pie chart: {str(e)}")
            return None
    
    def generate_bar_chart(self, data, labels, title, filename, xlabel=None, ylabel=None, color=None):
        """Generate a bar chart"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create bar chart
            bars = ax.bar(labels, data, color=color)
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=12)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=12)
            
            # Add data labels
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.2f}',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
            
            # Rotate x-axis labels if there are many
            if len(labels) > 5:
                plt.xticks(rotation=45, ha='right')
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating bar chart: {str(e)}")
            return None
    
    def generate_line_chart(self, data, labels, title, filename, xlabel=None, ylabel=None, color=None):
        """Generate a line chart"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create line chart
            ax.plot(labels, data, marker='o', linestyle='-', color=color)
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=12)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=12)
            
            # Add grid
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # Rotate x-axis labels if there are many
            if len(labels) > 5:
                plt.xticks(rotation=45, ha='right')
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating line chart: {str(e)}")
            return None
    
    def generate_stacked_bar_chart(self, data_sets, labels, title, filename, xlabel=None, ylabel=None, colors=None, legend_labels=None):
        """Generate a stacked bar chart"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create stacked bar chart
            bottom = np.zeros(len(labels))
            bars = []
            
            for i, data in enumerate(data_sets):
                color = colors[i] if colors and i < len(colors) else None
                bar = ax.bar(labels, data, bottom=bottom, color=color)
                bars.append(bar)
                bottom += np.array(data)
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=12)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=12)
            
            # Add legend
            if legend_labels:
                ax.legend(bars, legend_labels)
            
            # Rotate x-axis labels if there are many
            if len(labels) > 5:
                plt.xticks(rotation=45, ha='right')
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating stacked bar chart: {str(e)}")
            return None
    
    def generate_time_series_chart(self, dates, data, title, filename, xlabel=None, ylabel=None, color=None):
        """Generate a time series chart"""
        try:
            # Convert string dates to datetime objects if needed
            if isinstance(dates[0], str):
                dates = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
            
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create time series chart
            ax.plot(dates, data, marker='o', linestyle='-', color=color)
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=12)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=12)
            
            # Format x-axis as dates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.AutoDateLocator())
            
            # Add grid
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # Rotate x-axis labels
            plt.xticks(rotation=45, ha='right')
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating time series chart: {str(e)}")
            return None
    
    def generate_multi_line_chart(self, data_sets, labels, title, filename, xlabel=None, ylabel=None, colors=None, legend_labels=None):
        """Generate a multi-line chart"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create multi-line chart
            for i, data in enumerate(data_sets):
                color = colors[i] if colors and i < len(colors) else None
                label = legend_labels[i] if legend_labels and i < len(legend_labels) else f"Series {i+1}"
                ax.plot(labels, data, marker='o', linestyle='-', color=color, label=label)
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=12)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=12)
            
            # Add legend
            ax.legend()
            
            # Add grid
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # Rotate x-axis labels if there are many
            if len(labels) > 5:
                plt.xticks(rotation=45, ha='right')
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating multi-line chart: {str(e)}")
            return None
    
    def generate_scatter_plot(self, x_data, y_data, title, filename, xlabel=None, ylabel=None, color=None, marker=None):
        """Generate a scatter plot"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create scatter plot
            ax.scatter(x_data, y_data, color=color, marker=marker or 'o')
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=12)
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=12)
            
            # Add grid
            ax.grid(True, linestyle='--', alpha=0.7)
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating scatter plot: {str(e)}")
            return None
    
    def generate_heatmap(self, data, row_labels, col_labels, title, filename, cmap=None):
        """Generate a heatmap"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # Create heatmap
            im = ax.imshow(data, cmap=cmap or 'viridis')
            
            # Add colorbar
            cbar = ax.figure.colorbar(im, ax=ax)
            
            # Style the chart
            ax.set_title(title, fontsize=16)
            
            # Set ticks and labels
            ax.set_xticks(np.arange(len(col_labels)))
            ax.set_yticks(np.arange(len(row_labels)))
            ax.set_xticklabels(col_labels)
            ax.set_yticklabels(row_labels)
            
            # Rotate x-axis labels if there are many
            if len(col_labels) > 5:
                plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
            
            # Add text annotations
            for i in range(len(row_labels)):
                for j in range(len(col_labels)):
                    ax.text(j, i, f"{data[i, j]:.1f}", ha="center", va="center", color="w")
            
            plt.tight_layout()
            
            # Save the chart
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating heatmap: {str(e)}")
            return None
    
    def generate_dashboard(self, charts, title, filename, layout=None):
        """Generate a dashboard with multiple charts"""
        try:
            # Determine layout
            if not layout:
                # Default layout: 2 charts per row
                num_charts = len(charts)
                num_rows = (num_charts + 1) // 2
                num_cols = min(2, num_charts)
                layout = (num_rows, num_cols)
            
            # Create figure
            fig, axes = plt.subplots(layout[0], layout[1], figsize=(15, 10 * layout[0] / layout[1]))
            
            # Flatten axes array for easier indexing
            if isinstance(axes, np.ndarray):
                axes = axes.flatten()
            else:
                axes = [axes]
            
            # Add charts to dashboard
            for i, chart_data in enumerate(charts):
                if i < len(axes):
                    # Load chart image
                    chart_img = plt.imread(chart_data["file_path"])
                    
                    # Display chart
                    axes[i].imshow(chart_img)
                    axes[i].set_title(chart_data["title"])
                    axes[i].axis('off')
            
            # Hide unused axes
            for i in range(len(charts), len(axes)):
                axes[i].axis('off')
            
            # Add main title
            fig.suptitle(title, fontsize=20)
            
            plt.tight_layout(rect=[0, 0, 1, 0.96])  # Adjust for suptitle
            
            # Save the dashboard
            file_path = os.path.join(self.output_dir, filename)
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            return file_path
            
        except Exception as e:
            print(f"Error generating dashboard: {str(e)}")
            return None
