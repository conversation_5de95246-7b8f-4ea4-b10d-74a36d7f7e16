import sqlite3
from datetime import datetime

from model.base_model import BaseModel


class Account(BaseModel):
    """
    Class for handling account operations
    Represents financial accounts in the cashbook system with proper accounting classifications
    """

    # Account Classifications (Standard Accounting Categories)
    CLASSIFICATION_ASSET = "Asset"
    CLASSIFICATION_LIABILITY = "Liability"
    CLASSIFICATION_EQUITY = "Equity"
    CLASSIFICATION_REVENUE = "Revenue"
    CLASSIFICATION_EXPENSE = "Expense"

    # Account Types by Classification
    ASSET_TYPES = {
        "Current Assets": ["Cash", "Checking Account", "Savings Account", "Petty Cash", "Accounts Receivable", "Inventory", "Prepaid Expenses"],
        "Fixed Assets": ["Equipment", "Furniture", "Buildings", "Land", "Vehicles", "Accumulated Depreciation"]
    }

    LIABILITY_TYPES = {
        "Current Liabilities": ["Accounts Payable", "Accrued Expenses", "Short-term Loans", "Credit Cards", "Sales Tax Payable"],
        "Long-term Liabilities": ["Long-term Loans", "Mortgages", "Bonds Payable"]
    }

    EQUITY_TYPES = {
        "Owner's Equity": ["Owner's Capital", "Retained Earnings", "Owner's Drawings"]
    }

    REVENUE_TYPES = {
        "Operating Revenue": ["Sales Revenue", "Service Revenue", "Interest Income", "Rental Income"],
        "Other Revenue": ["Gain on Sale of Assets", "Miscellaneous Income"]
    }

    EXPENSE_TYPES = {
        "Operating Expenses": ["Cost of Goods Sold", "Salaries Expense", "Rent Expense", "Utilities Expense", "Office Supplies", "Insurance Expense"],
        "Administrative Expenses": ["Professional Fees", "Bank Charges", "Depreciation Expense"],
        "Other Expenses": ["Interest Expense", "Loss on Sale of Assets"]
    }

    def __init__(self, db_path, id=None, name=None, type=None, currency=None, opening_balance=0.0, current_balance=0.0,
                 classification=None, account_number=None, parent_id=None, is_active=True):
        super().__init__(db_path)
        self.id = id
        self.name = name
        self.type = type
        self.currency = currency
        self.opening_balance = opening_balance
        self.current_balance = current_balance
        self.classification = classification
        self.account_number = account_number
        self.parent_id = parent_id
        self.is_active = is_active

    def table_name(self):
        return "accounts"

    def fields(self):
        return ["id", "name", "type", "currency", "opening_balance", "current_balance", "description", "created_date",
                "classification", "account_number", "parent_id", "is_active"]

    def primary_key(self):
        return "id"

    def deposit(self, amount):
        if amount <= 0:
            raise ValueError("Deposit amount must be positive")
        self.current_balance += amount

    def withdraw(self, amount):
        if amount <= 0:
            raise ValueError("Withdrawal amount must be positive")
        if amount > self.current_balance:
            raise ValueError("Insufficient funds")
        self.current_balance -= amount

    def __str__(self):
        return f"Account({self.name}, {self.type}, Balance: {self.current_balance} {self.currency})"

    @classmethod
    def get_all_classifications(cls):
        """Get all account classifications"""
        return [
            cls.CLASSIFICATION_ASSET,
            cls.CLASSIFICATION_LIABILITY,
            cls.CLASSIFICATION_EQUITY,
            cls.CLASSIFICATION_REVENUE,
            cls.CLASSIFICATION_EXPENSE
        ]

    @classmethod
    def get_types_for_classification(cls, classification):
        """Get account types for a specific classification"""
        if classification == cls.CLASSIFICATION_ASSET:
            return cls.ASSET_TYPES
        elif classification == cls.CLASSIFICATION_LIABILITY:
            return cls.LIABILITY_TYPES
        elif classification == cls.CLASSIFICATION_EQUITY:
            return cls.EQUITY_TYPES
        elif classification == cls.CLASSIFICATION_REVENUE:
            return cls.REVENUE_TYPES
        elif classification == cls.CLASSIFICATION_EXPENSE:
            return cls.EXPENSE_TYPES
        else:
            return {}

    @classmethod
    def get_classification_for_type(cls, account_type):
        """Get the classification for a specific account type"""
        all_types = {
            **{t: cls.CLASSIFICATION_ASSET for subcat in cls.ASSET_TYPES.values() for t in subcat},
            **{t: cls.CLASSIFICATION_LIABILITY for subcat in cls.LIABILITY_TYPES.values() for t in subcat},
            **{t: cls.CLASSIFICATION_EQUITY for subcat in cls.EQUITY_TYPES.values() for t in subcat},
            **{t: cls.CLASSIFICATION_REVENUE for subcat in cls.REVENUE_TYPES.values() for t in subcat},
            **{t: cls.CLASSIFICATION_EXPENSE for subcat in cls.EXPENSE_TYPES.values() for t in subcat}
        }

        # Handle backward compatibility with old account types
        legacy_mappings = {
            "Checking": cls.CLASSIFICATION_ASSET,
            "Savings": cls.CLASSIFICATION_ASSET,
            "Cash": cls.CLASSIFICATION_ASSET,
            "Investment": cls.CLASSIFICATION_ASSET,
            "Credit Card": cls.CLASSIFICATION_LIABILITY,
            "Other": cls.CLASSIFICATION_ASSET  # Default to asset for "Other"
        }

        # First try the new comprehensive mapping, then fall back to legacy
        return all_types.get(account_type) or legacy_mappings.get(account_type, cls.CLASSIFICATION_ASSET)

    def get_normal_balance_side(self):
        """Get the normal balance side for this account (debit or credit)"""
        if self.classification in [self.CLASSIFICATION_ASSET, self.CLASSIFICATION_EXPENSE]:
            return "debit"
        elif self.classification in [self.CLASSIFICATION_LIABILITY, self.CLASSIFICATION_EQUITY, self.CLASSIFICATION_REVENUE]:
            return "credit"
        else:
            return "debit"  # Default to debit

    def is_debit_account(self):
        """Check if this is a debit account"""
        return self.get_normal_balance_side() == "debit"

    def is_credit_account(self):
        """Check if this is a credit account"""
        return self.get_normal_balance_side() == "credit"

    def _ensure_new_columns_exist(self, cursor):
        """Ensure new columns exist in the accounts table for backward compatibility"""
        try:
            # Check if new columns exist, if not add them
            cursor.execute("PRAGMA table_info(accounts)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'classification' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN classification TEXT")

            if 'account_number' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN account_number TEXT")

            if 'parent_id' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN parent_id INTEGER")

            if 'is_active' not in columns:
                cursor.execute("ALTER TABLE accounts ADD COLUMN is_active BOOLEAN DEFAULT 1")

            # Update existing accounts with classifications if they don't have them
            cursor.execute("SELECT id, type, classification FROM accounts WHERE classification IS NULL OR classification = ''")
            accounts_to_update = cursor.fetchall()

            for account_id, account_type, classification in accounts_to_update:
                if not classification:
                    new_classification = self.get_classification_for_type(account_type)
                    if new_classification:
                        cursor.execute("UPDATE accounts SET classification = ? WHERE id = ?", (new_classification, account_id))

        except sqlite3.Error as e:
            print(f"Error adding columns: {e}")
            # Continue anyway - the columns might already exist

    def create_account(self, name, account_type, currency, opening_balance=0.0, description="",
                      classification=None, account_number=None, parent_id=None):
        """Create a new account in the database"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")

            # First, check if new columns exist and add them if they don't
            self._ensure_new_columns_exist(cursor)

            current_date = datetime.now().strftime("%Y-%m-%d")

            # Auto-determine classification if not provided
            if not classification:
                classification = self.get_classification_for_type(account_type)

            # Insert the account
            cursor.execute('''
                INSERT INTO accounts
                (name, type, currency, opening_balance, current_balance, description, created_date,
                 classification, account_number, parent_id, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, account_type, currency, opening_balance, opening_balance, description, current_date,
                  classification, account_number, parent_id, True))

            account_id = cursor.lastrowid

            # If opening balance is not zero, create an initial transaction
            if opening_balance != 0:
                cursor.execute('''
                    INSERT INTO transactions
                    (date, amount, description, category_id, account_id, reconciled, type)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (current_date, opening_balance, "Opening Balance",
                      None, account_id, 1, "initial"))

            conn.commit()
            return account_id

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def get_account(self, account_id):
        """Get account details by ID"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # First, check if new columns exist and add them if they don't
            self._ensure_new_columns_exist(cursor)

            cursor.execute('''
                SELECT id, name, type, currency, opening_balance,
                       current_balance, description, created_date,
                       classification, account_number, parent_id, is_active
                FROM accounts WHERE id = ?
            ''', (account_id,))

            account = cursor.fetchone()

            if account:
                return {
                    'id': account[0],
                    'name': account[1],
                    'type': account[2],
                    'currency': account[3],
                    'opening_balance': account[4],
                    'current_balance': account[5],
                    'description': account[6],
                    'created_date': account[7],
                    'classification': account[8],
                    'account_number': account[9],
                    'parent_id': account[10],
                    'is_active': account[11]
                }
            return None

        except sqlite3.Error as e:
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def get_all_accounts(self):
        """Get all accounts"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # First, check if new columns exist and add them if they don't
            self._ensure_new_columns_exist(cursor)

            cursor.execute('''
                SELECT id, name, type, currency, opening_balance,
                       current_balance, description, created_date,
                       classification, account_number, parent_id, is_active
                FROM accounts
                ORDER BY account_number, name
            ''')

            accounts = []
            for row in cursor.fetchall():
                accounts.append({
                    'id': row[0],
                    'name': row[1],
                    'type': row[2],
                    'currency': row[3],
                    'opening_balance': row[4],
                    'current_balance': row[5],
                    'description': row[6],
                    'created_date': row[7],
                    'classification': row[8],
                    'account_number': row[9],
                    'parent_id': row[10],
                    'is_active': row[11]
                })

            return accounts

        except sqlite3.Error as e:
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def update_account(self, account_id, name=None, account_type=None,
                      currency=None, description=None):
        """Update account details"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get current account data
            cursor.execute('''
                SELECT name, type, currency, description FROM accounts WHERE id = ?
            ''', (account_id,))

            current = cursor.fetchone()
            if not current:
                raise ValueError(f"Account with ID {account_id} not found")

            # Use current values if new ones are not provided
            name = name if name is not None else current[0]
            account_type = account_type if account_type is not None else current[1]
            currency = currency if currency is not None else current[2]
            description = description if description is not None else current[3]

            # Update the account
            cursor.execute('''
                UPDATE accounts
                SET name = ?, type = ?, currency = ?, description = ?
                WHERE id = ?
            ''', (name, account_type, currency, description, account_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def delete_account(self, account_id):
        """Delete an account (only if it has no transactions)"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Check if there are transactions for this account
            cursor.execute("SELECT COUNT(*) FROM transactions WHERE account_id = ?", (account_id,))
            count = cursor.fetchone()[0]

            if count > 0:
                raise ValueError("Cannot delete account with existing transactions")

            # Delete the account
            cursor.execute("DELETE FROM accounts WHERE id = ?", (account_id,))

            conn.commit()
            return True

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def update_balance(self, account_id, new_amount):
        """Update account current balance (should be called after transactions change)"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Update the account balance
            cursor.execute('''
                UPDATE accounts SET current_balance = ?
                WHERE id = ?
            ''', (new_amount, account_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def recalculate_balance(self, account_id):
        """Recalculate account balance based on all transactions"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get opening balance
            cursor.execute("SELECT opening_balance FROM accounts WHERE id = ?", (account_id,))
            opening_balance = cursor.fetchone()[0]

            # Sum all transactions
            cursor.execute('''
                SELECT SUM(CASE WHEN type = 'expense' THEN -amount ELSE amount END)
                FROM transactions
                WHERE account_id = ? AND type != 'initial'
            ''', (account_id,))

            transaction_sum = cursor.fetchone()[0] or 0

            # Calculate new balance
            new_balance = opening_balance + transaction_sum

            # Update account
            cursor.execute('''
                UPDATE accounts SET current_balance = ?
                WHERE id = ?
            ''', (new_balance, account_id))

            conn.commit()
            return new_balance

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()