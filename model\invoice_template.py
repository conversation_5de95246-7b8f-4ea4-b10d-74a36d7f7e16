from datetime import datetime

from model.base_model import BaseModel


class InvoiceTemplate(BaseModel):
    """Model for managing invoice templates"""

    def __init__(self, db_path):
        """Initialize the InvoiceTemplate model with database path"""
        super().__init__(db_path)
        self._table_name = "invoice_templates"
        self._create_tables()

    def table_name(self):
        """Return the table name for this model"""
        return self._table_name

    def fields(self):
        """Return a list of field names for this model"""
        return [
            'id', 'name', 'description', 'company_name', 'company_address',
            'company_phone', 'company_email', 'company_website', 'company_tax_id',
            'logo_path', 'primary_color', 'secondary_color', 'font_family',
            'show_payment_info', 'payment_info', 'footer_text', 'is_default',
            'created_at', 'updated_at'
        ]

    def primary_key(self):
        """Return the primary key field name"""
        return 'id'

    def _create_tables(self):
        """Create the invoice_templates table if it doesn't exist"""
        query = '''
        CREATE TABLE IF NOT EXISTS invoice_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            company_name TEXT NOT NULL,
            company_address TEXT,
            company_phone TEXT,
            company_email TEXT,
            company_website TEXT,
            company_tax_id TEXT,
            logo_path TEXT,
            primary_color TEXT DEFAULT '#3498db',
            secondary_color TEXT DEFAULT '#2c3e50',
            font_family TEXT DEFAULT 'Helvetica',
            show_payment_info BOOLEAN DEFAULT 1,
            payment_info TEXT,
            footer_text TEXT,
            is_default BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.execute_query(query)

        # Create default template if none exists
        query = '''
        SELECT COUNT(*) as count FROM invoice_templates
        '''
        result = self.execute_query(query)
        if len(result) == 0 or result[0]['count'] == 0:
            self.create_default_template()

    def create_default_template(self):
        """Create a default invoice template"""
        default_template = {
            'name': 'Default Template',
            'description': 'Default invoice template',
            'company_name': 'Your Company Name',
            'company_address': '123 Business St, City, State, ZIP',
            'company_phone': '(*************',
            'company_email': '<EMAIL>',
            'company_website': 'www.yourcompany.com',
            'company_tax_id': 'TAX-ID-12345',
            'primary_color': '#3498db',
            'secondary_color': '#2c3e50',
            'font_family': 'Helvetica',
            'show_payment_info': True,
            'payment_info': 'Please make payment within 30 days.',
            'footer_text': 'Thank you for your business!',
            'is_default': True
        }

        self.add_template(default_template)

    def add_template(self, template_data):
        """Add a new invoice template

        Args:
            template_data (dict): Dictionary containing template information
                - name: Template name (required)
                - description: Template description
                - company_name: Company name (required)
                - company_address: Company address
                - company_phone: Company phone
                - company_email: Company email
                - company_website: Company website
                - company_tax_id: Company tax ID
                - logo_path: Path to company logo
                - primary_color: Primary color for template
                - secondary_color: Secondary color for template
                - font_family: Font family for template
                - show_payment_info: Whether to show payment info
                - payment_info: Payment information text
                - footer_text: Footer text
                - is_default: Whether this is the default template

        Returns:
            int: ID of the newly created template
        """
        # Validate required fields
        if not template_data.get('name'):
            raise ValueError("Template name is required")

        if not template_data.get('company_name'):
            raise ValueError("Company name is required")

        # If this is the default template, unset any existing default
        if template_data.get('is_default'):
            self.unset_default_template()

        # Prepare data for insertion
        columns = []
        values = []
        placeholders = []

        for key, value in template_data.items():
            if key in ['name', 'description', 'company_name', 'company_address',
                      'company_phone', 'company_email', 'company_website',
                      'company_tax_id', 'logo_path', 'primary_color',
                      'secondary_color', 'font_family', 'show_payment_info',
                      'payment_info', 'footer_text', 'is_default']:
                columns.append(key)
                values.append(value)
                placeholders.append('?')

        # Add timestamps
        columns.extend(['created_at', 'updated_at'])
        values.extend([datetime.now(), datetime.now()])
        placeholders.extend(['?', '?'])

        # Build the query
        query = f'''
        INSERT INTO {self.table_name()}
        ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        '''

        # Execute the query
        result = self.execute_query(query, values)
        # For INSERT queries, execute_query returns the lastrowid
        return result

    def update_template(self, template_id, template_data):
        """Update an existing template

        Args:
            template_id (int): ID of the template to update
            template_data (dict): Dictionary containing template information to update

        Returns:
            bool: True if the update was successful, False otherwise
        """
        # If this is being set as the default template, unset any existing default
        if template_data.get('is_default'):
            self.unset_default_template()

        # Prepare data for update
        set_clause = []
        values = []

        for key, value in template_data.items():
            if key in ['name', 'description', 'company_name', 'company_address',
                      'company_phone', 'company_email', 'company_website',
                      'company_tax_id', 'logo_path', 'primary_color',
                      'secondary_color', 'font_family', 'show_payment_info',
                      'payment_info', 'footer_text', 'is_default']:
                set_clause.append(f"{key} = ?")
                values.append(value)

        # Add updated_at timestamp
        set_clause.append("updated_at = ?")
        values.append(datetime.now())

        # Add template_id to values
        values.append(template_id)

        # Build the query
        query = f'''
        UPDATE {self.table_name()}
        SET {', '.join(set_clause)}
        WHERE id = ?
        '''

        # Execute the query
        result = self.execute_query(query, values)
        # For UPDATE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False

    def delete_template(self, template_id):
        """Delete a template

        Args:
            template_id (int): ID of the template to delete

        Returns:
            bool: True if the deletion was successful, False otherwise
        """
        # Check if this is the default template
        template = self.get_by_id(template_id)
        if template and template.get('is_default'):
            # Cannot delete the default template if it's the only one
            count_query = f"SELECT COUNT(*) as count FROM {self.table_name()}"
            result = self.execute_query(count_query)
            if len(result) == 0 or result[0]['count'] <= 1:
                raise ValueError("Cannot delete the only template")

            # Find another template to set as default
            query = f"SELECT id FROM {self.table_name()} WHERE id != ? LIMIT 1"
            result = self.execute_query(query, [template_id])
            if len(result) > 0:
                new_default_id = result[0]['id']
                # Set the new default
                self.set_default_template(new_default_id)

        # Delete the template
        query = f"DELETE FROM {self.table_name()} WHERE id = ?"
        result = self.execute_query(query, [template_id])
        # For DELETE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False

    def get_all_templates(self):
        """Get all templates

        Returns:
            list: List of dictionaries containing template information
        """
        query = f"SELECT * FROM {self.table_name()} ORDER BY name ASC"
        result = self.execute_query(query)

        # For SELECT queries, execute_query returns a list of dictionaries
        templates = result

        return templates

    def get_default_template(self):
        """Get the default template

        Returns:
            dict: Dictionary containing default template information, or None if not found
        """
        query = f"SELECT * FROM {self.table_name()} WHERE is_default = 1 LIMIT 1"
        result = self.execute_query(query)

        if not result or len(result) == 0:
            return None

        return result[0]

    def set_default_template(self, template_id):
        """Set a template as the default

        Args:
            template_id (int): ID of the template to set as default

        Returns:
            bool: True if successful, False otherwise
        """
        # First, unset any existing default
        self.unset_default_template()

        # Then set the new default
        query = f"UPDATE {self.table_name()} SET is_default = 1 WHERE id = ?"
        result = self.execute_query(query, [template_id])
        # For UPDATE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False

    def unset_default_template(self):
        """Unset any existing default template

        Returns:
            bool: True if successful, False otherwise
        """
        query = f"UPDATE {self.table_name()} SET is_default = 0 WHERE is_default = 1"
        result = self.execute_query(query)
        # For UPDATE queries, execute_query returns the number of affected rows
        return result > 0 if isinstance(result, int) else False
