import tkinter as tk
import unittest
from unittest.mock import MagicMock, patch

# Import modules to test
from view.company import CompanyWindow


class TestCompanyWindow(unittest.TestCase):
    def setUp(self):
        # Create a root window for testing
        self.root = tk.Tk()

        # Create a mock transaction manager
        self.transaction_manager = MagicMock()
        self.transaction_manager.get_transactions.return_value = []
        self.transaction_manager.get_balance_summary.return_value = {
            'income': 1000.0,
            'expenses': 500.0,
            'balance': 500.0
        }

        # Create a mock close callback
        self.close_callback = MagicMock()

        # Create the company window
        self.company_window = CompanyWindow(
            self.root,
            "Test Company",
            self.transaction_manager,
            self.close_callback
        )

    def tearDown(self):
        # Destroy the root window
        self.root.destroy()

    def test_window_initialization(self):
        """Test that the window initializes correctly"""
        # Check that the window has the correct attributes
        self.assertEqual(self.company_window.company_name, "Test Company")
        self.assertEqual(self.company_window.transaction_manager, self.transaction_manager)
        self.assertEqual(self.company_window.close_callback, self.close_callback)

        # Check that the transaction manager methods were called
        self.transaction_manager.get_transactions.assert_called_once()
        self.transaction_manager.get_balance_summary.assert_called_once()

    def test_summary_display(self):
        """Test that the summary information is displayed correctly"""
        # Check the summary labels
        self.assertEqual(self.company_window.income_label.cget("text"), "$1000.00")
        self.assertEqual(self.company_window.expenses_label.cget("text"), "$500.00")
        self.assertEqual(self.company_window.balance_label.cget("text"), "$500.00")

        # Check that the balance label has the correct color (green for positive balance)
        # The color can be either "green" or "#008000" depending on the platform
        balance_color = self.company_window.balance_label.cget("foreground")
        self.assertTrue(balance_color == "green" or balance_color == "#008000",
                       f"Balance color '{balance_color}' is not a valid green color")

    @patch('tkinter.messagebox.showinfo')
    def test_add_transaction(self, mock_showinfo):
        """Test the add_transaction method"""
        # Call the add_transaction method
        self.company_window.add_transaction()

        # Check that the info message was shown
        mock_showinfo.assert_called_once_with("Add Transaction", "This feature is not yet implemented.")

    @patch('tkinter.messagebox.showinfo')
    def test_edit_transaction(self, mock_showinfo):
        """Test the edit_transaction method"""
        # Call the edit_transaction method
        self.company_window.edit_transaction()

        # Check that the info message was shown
        mock_showinfo.assert_called_once_with("Edit Transaction", "This feature is not yet implemented.")

    @patch('tkinter.messagebox.showinfo')
    def test_delete_transaction(self, mock_showinfo):
        """Test the delete_transaction method"""
        # Call the delete_transaction method
        self.company_window.delete_transaction()

        # Check that the info message was shown
        mock_showinfo.assert_called_once_with("Delete Transaction", "This feature is not yet implemented.")

    @patch('tkinter.messagebox.showinfo')
    def test_import_file(self, mock_showinfo):
        """Test the import_file method"""
        # Call the import_file method
        self.company_window.import_file()

        # Check that the info message was shown
        mock_showinfo.assert_called_once_with("Import File", "This feature is not yet implemented.")

    @patch('tkinter.messagebox.showinfo')
    def test_generate_report(self, mock_showinfo):
        """Test the generate_report method"""
        # Call the generate_report method
        self.company_window.generate_report()

        # Check that the info message was shown
        mock_showinfo.assert_called_once_with("Generate Report", "This feature is not yet implemented.")

    @patch('tkinter.messagebox.showinfo')
    def test_share_data(self, mock_showinfo):
        """Test the share_data method"""
        # Call the share_data method
        self.company_window.share_data()

        # Check that the info message was shown
        mock_showinfo.assert_called_once_with("Share Data", "This feature is not yet implemented.")

    def test_filter_transactions(self):
        """Test the filter_transactions method"""
        # Set up mock transactions
        self.transaction_manager.get_transactions.return_value = [
            {
                'id': 1,
                'date': '2025-01-01',
                'description': 'Test Income',
                'amount': 1000.0,
                'category_name': 'Salary',
                'type': 'income',
                'account_name': 'Bank'
            },
            {
                'id': 2,
                'date': '2025-01-02',
                'description': 'Test Expense',
                'amount': 500.0,
                'category_name': 'Food',
                'type': 'expense',
                'account_name': 'Cash'
            }
        ]

        # Set the search text to filter for income transactions
        self.company_window.search_var.set("income")

        # Call the filter_transactions method
        self.company_window.filter_transactions()

        # Check that the status var was updated
        self.assertEqual(self.company_window.status_var.get(), "Found 1 transactions")

        # Clear the search text
        self.company_window.search_var.set("")

        # Call the filter_transactions method again
        self.company_window.filter_transactions()

        # Check that the status var was updated
        self.assertEqual(self.company_window.status_var.get(), "Found 2 transactions")


if __name__ == '__main__':
    unittest.main()
