import datetime
import tkinter as tk
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.account import Account
from model.category import Category
from model.journal_entry import JournalEntry
from model.transaction import TransactionManager
from view.components.date_picker import DatePicker


class JournalEntryFrame(ttk.Frame):
    """Journal entry frame for creating double-entry transactions"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Journal Entry - {company_name}"

        # Create models
        self.account_model = Account(self.db_path)
        self.category_model = Category(self.db_path)
        self.transaction_manager = TransactionManager(self.db_path)
        self.journal_entry_model = JournalEntry(self.db_path)

        # Entry lines
        self.entry_lines = []
        self.next_line_id = 1

        self.create_widgets()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with title and close button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(header_frame, text="Journal Entry", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")

        close_button = ttk.Button(header_frame, text="Close", command=self.close_callback, bootstyle=DANGER)
        close_button.pack(side="right")

        # Journal entry form
        form_frame = ttk.LabelFrame(main_frame, text="Journal Entry Details", padding=10)
        form_frame.pack(fill="x", pady=(0, 20))

        # Date and reference
        date_ref_frame = ttk.Frame(form_frame)
        date_ref_frame.pack(fill="x", pady=5)

        # Date
        ttk.Label(date_ref_frame, text="Date:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        date_frame = ttk.Frame(date_ref_frame)
        date_frame.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        self.date_picker = DatePicker(date_frame)

        # Reference
        ttk.Label(date_ref_frame, text="Reference:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.reference_var = tk.StringVar()
        reference_entry = ttk.Entry(date_ref_frame, textvariable=self.reference_var, width=20)
        reference_entry.grid(row=0, column=3, padx=5, pady=5, sticky="w")

        # Description
        ttk.Label(form_frame, text="Description:").pack(anchor="w", padx=5, pady=(5, 0))
        self.description_var = tk.StringVar()
        description_entry = ttk.Entry(form_frame, textvariable=self.description_var, width=50)
        description_entry.pack(fill="x", padx=5, pady=(0, 5))

        # Journal lines
        lines_frame = ttk.LabelFrame(main_frame, text="Journal Lines", padding=10)
        lines_frame.pack(fill="both", expand=True)

        # Column headers
        headers_frame = ttk.Frame(lines_frame)
        headers_frame.pack(fill="x", pady=(0, 5))

        ttk.Label(headers_frame, text="Account", width=20).grid(row=0, column=0, padx=5)
        ttk.Label(headers_frame, text="Description", width=30).grid(row=0, column=1, padx=5)
        ttk.Label(headers_frame, text="Category", width=15).grid(row=0, column=2, padx=5)
        ttk.Label(headers_frame, text="Debit", width=10).grid(row=0, column=3, padx=5)
        ttk.Label(headers_frame, text="Credit", width=10).grid(row=0, column=4, padx=5)
        ttk.Label(headers_frame, text="Actions", width=10).grid(row=0, column=5, padx=5)

        # Journal lines container (scrollable)
        self.canvas = tk.Canvas(lines_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(lines_frame, orient="vertical", command=self.canvas.yview)

        self.lines_container = ttk.Frame(self.canvas)
        self.lines_container.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))

        self.canvas.create_window((0, 0), window=self.lines_container, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Add line button
        add_line_button = ttk.Button(lines_frame, text="Add Line", command=self.add_journal_line, bootstyle=SUCCESS)
        add_line_button.pack(anchor="w", padx=5, pady=10)

        # Totals frame
        totals_frame = ttk.Frame(lines_frame)
        totals_frame.pack(fill="x", pady=10)

        ttk.Label(totals_frame, text="Totals:").grid(row=0, column=0, padx=5, sticky="e")

        self.debit_total_var = tk.StringVar(value="$0.00")
        self.credit_total_var = tk.StringVar(value="$0.00")

        ttk.Label(totals_frame, textvariable=self.debit_total_var, width=10).grid(row=0, column=1, padx=5)
        ttk.Label(totals_frame, textvariable=self.credit_total_var, width=10).grid(row=0, column=2, padx=5)

        self.difference_var = tk.StringVar(value="Difference: $0.00")
        difference_label = ttk.Label(totals_frame, textvariable=self.difference_var, foreground="red")
        difference_label.grid(row=0, column=3, padx=5)

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x", pady=10)

        save_button = ttk.Button(buttons_frame, text="Save Journal Entry", command=self.save_journal_entry, bootstyle=SUCCESS)
        save_button.pack(side="left", padx=(0, 5))

        clear_button = ttk.Button(buttons_frame, text="Clear", command=self.clear_form, bootstyle=SECONDARY)
        clear_button.pack(side="left", padx=5)

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

        # Add initial journal lines
        self.add_journal_line()
        self.add_journal_line()

    def add_journal_line(self):
        """Add a new journal line to the form"""
        line_id = self.next_line_id
        self.next_line_id += 1

        line_frame = ttk.Frame(self.lines_container)
        line_frame.pack(fill="x", pady=2)

        # Get accounts
        accounts = self.account_model.get_all_accounts()
        account_names = [account["name"] for account in accounts]
        account_map = {account["name"]: account["id"] for account in accounts}

        # Get categories
        categories = self.category_model.get_all_categories()
        category_names = ["None"] + [category["name"] for category in categories]
        category_map = {category["name"]: category["id"] for category in categories}

        # Account dropdown
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(line_frame, textvariable=account_var, values=account_names, width=20, state="readonly")
        account_combo.grid(row=0, column=0, padx=5)

        # Description
        description_var = tk.StringVar()
        description_entry = ttk.Entry(line_frame, textvariable=description_var, width=30)
        description_entry.grid(row=0, column=1, padx=5)

        # Category dropdown
        category_var = tk.StringVar(value="None")
        category_combo = ttk.Combobox(line_frame, textvariable=category_var, values=category_names, width=15, state="readonly")
        category_combo.grid(row=0, column=2, padx=5)

        # Debit amount
        debit_var = tk.StringVar()
        debit_entry = ttk.Entry(line_frame, textvariable=debit_var, width=10)
        debit_entry.grid(row=0, column=3, padx=5)

        # Credit amount
        credit_var = tk.StringVar()
        credit_entry = ttk.Entry(line_frame, textvariable=credit_var, width=10)
        credit_entry.grid(row=0, column=4, padx=5)

        # Delete button
        delete_button = ttk.Button(
            line_frame,
            text="X",
            command=lambda lid=line_id: self.delete_journal_line(lid),
            bootstyle=DANGER,
            width=3
        )
        delete_button.grid(row=0, column=5, padx=5)

        # Add trace to update totals when amounts change
        debit_var.trace_add("write", lambda *args: self.update_totals())
        credit_var.trace_add("write", lambda *args: self.update_totals())

        # Store line data
        line_data = {
            "id": line_id,
            "frame": line_frame,
            "account_var": account_var,
            "account_map": account_map,
            "description_var": description_var,
            "category_var": category_var,
            "category_map": category_map,
            "debit_var": debit_var,
            "credit_var": credit_var
        }

        self.entry_lines.append(line_data)

        # Update canvas scroll region
        self.lines_container.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def delete_journal_line(self, line_id):
        """Delete a journal line"""
        # Find the line
        for i, line in enumerate(self.entry_lines):
            if line["id"] == line_id:
                # Remove the frame
                line["frame"].destroy()
                # Remove from list
                self.entry_lines.pop(i)
                break

        # Update totals
        self.update_totals()

        # Update canvas scroll region
        self.lines_container.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def update_totals(self):
        """Update the debit and credit totals"""
        debit_total = 0
        credit_total = 0

        for line in self.entry_lines:
            # Get debit amount
            try:
                debit_str = line["debit_var"].get().strip()
                if debit_str:
                    debit_amount = float(debit_str)
                    debit_total += debit_amount
            except ValueError:
                pass

            # Get credit amount
            try:
                credit_str = line["credit_var"].get().strip()
                if credit_str:
                    credit_amount = float(credit_str)
                    credit_total += credit_amount
            except ValueError:
                pass

        # Update total displays
        self.debit_total_var.set(f"${debit_total:.2f}")
        self.credit_total_var.set(f"${credit_total:.2f}")

        # Calculate difference
        difference = debit_total - credit_total

        if abs(difference) < 0.01:  # Allow for small rounding errors
            self.difference_var.set("Balanced")
            self.status_var.set("Journal entry is balanced")
        else:
            self.difference_var.set(f"Difference: ${difference:.2f}")
            self.status_var.set("Journal entry is not balanced")

    def save_journal_entry(self):
        """Save the journal entry using the new multi-line system"""
        try:
            # Validate form
            if not self.validate_form():
                return

            # Get form data
            date = self.date_picker.get_date()
            reference = self.reference_var.get().strip()
            description = self.description_var.get().strip()

            # Prepare lines data for the new journal entry system
            lines = []
            for line in self.entry_lines:
                account_name = line["account_var"].get()
                if not account_name:
                    continue

                account_id = line["account_map"].get(account_name)
                if not account_id:
                    continue

                line_description = line["description_var"].get().strip()
                category_name = line["category_var"].get()
                category_id = None
                if category_name and category_name != "None":
                    category_id = line["category_map"].get(category_name)

                # Get debit/credit amounts
                debit_str = line["debit_var"].get().strip()
                credit_str = line["credit_var"].get().strip()

                debit_amount = float(debit_str) if debit_str else 0.0
                credit_amount = float(credit_str) if credit_str else 0.0

                # Only add lines with amounts
                if debit_amount > 0 or credit_amount > 0:
                    lines.append({
                        'account_id': account_id,
                        'description': line_description,
                        'debit_amount': debit_amount,
                        'credit_amount': credit_amount,
                        'category_id': category_id
                    })

            if not lines:
                messagebox.showerror("Error", "No valid journal lines found")
                return

            # Create the journal entry using the new system
            entry_id = self.journal_entry_model.create_journal_entry(
                date=date.strftime("%Y-%m-%d"),
                description=description,
                lines=lines,
                reference=reference,
                tags="",  # Could add tags field to UI later
                created_by="admin"  # Could get from current user
            )

            if entry_id:
                # Get the generated entry number for display
                entry = self.journal_entry_model.get_journal_entry(entry_id)
                entry_number = entry['entry_number'] if entry else str(entry_id)

                messagebox.showinfo("Success", f"Journal entry {entry_number} saved successfully!")
                self.clear_form()
                self.status_var.set(f"Journal entry {entry_number} saved successfully")
            else:
                messagebox.showerror("Error", "Failed to save journal entry")
                self.status_var.set("Error saving journal entry")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save journal entry: {str(e)}")
            self.status_var.set("Error saving journal entry")

    def validate_form(self):
        """Validate the journal entry form"""
        # Check date
        if not self.date_picker.get_date():
            messagebox.showerror("Error", "Please enter a valid date")
            return False

        # Check description
        if not self.description_var.get().strip():
            messagebox.showerror("Error", "Please enter a description")
            return False

        # Check if there are any lines
        if not self.entry_lines:
            messagebox.showerror("Error", "Please add at least one journal line")
            return False

        # Check if at least one line has data
        has_data = False
        for line in self.entry_lines:
            account = line["account_var"].get()
            debit = line["debit_var"].get().strip()
            credit = line["credit_var"].get().strip()

            if account and (debit or credit):
                has_data = True
                break

        if not has_data:
            messagebox.showerror("Error", "Please enter data for at least one journal line")
            return False

        # Check if debits and credits balance
        debit_total = 0
        credit_total = 0

        for line in self.entry_lines:
            # Skip lines without an account
            if not line["account_var"].get():
                continue

            # Get debit amount
            try:
                debit_str = line["debit_var"].get().strip()
                if debit_str:
                    debit_amount = float(debit_str)
                    debit_total += debit_amount
            except ValueError:
                messagebox.showerror("Error", "Please enter valid debit amounts")
                return False

            # Get credit amount
            try:
                credit_str = line["credit_var"].get().strip()
                if credit_str:
                    credit_amount = float(credit_str)
                    credit_total += credit_amount
            except ValueError:
                messagebox.showerror("Error", "Please enter valid credit amounts")
                return False

            # Check that line has either debit or credit, not both
            if debit_str and credit_str:
                messagebox.showerror("Error", "A line cannot have both debit and credit amounts")
                return False

        # Check if debits and credits balance
        if abs(debit_total - credit_total) > 0.01:  # Allow for small rounding errors
            messagebox.showerror("Error", "Debits and credits must balance")
            return False

        return True

    def clear_form(self):
        """Clear the form"""
        # Reset date to today
        self.date_picker.set_date(datetime.date.today())

        # Clear reference and description
        self.reference_var.set("")
        self.description_var.set("")

        # Remove all journal lines
        for line in self.entry_lines:
            line["frame"].destroy()

        self.entry_lines = []

        # Reset totals
        self.debit_total_var.set("$0.00")
        self.credit_total_var.set("$0.00")
        self.difference_var.set("Difference: $0.00")

        # Add initial journal lines
        self.add_journal_line()
        self.add_journal_line()

        # Update status
        self.status_var.set("Form cleared")
