import sqlite3
import json
from datetime import datetime
from difflib import <PERSON><PERSON><PERSON><PERSON><PERSON>
from model.base_model import BaseModel


class BankReconciliationSession(BaseModel):
    """
    Model class for bank reconciliation sessions
    Manages the reconciliation process between bank statements and book transactions
    """
    
    def __init__(self, db_path, id=None, account_id=None, statement_date=None,
                 statement_balance=0.0, book_balance=0.0, difference=0.0,
                 status='pending', created_by=None, notes=None):
        super().__init__(db_path)
        self.id = id
        self.account_id = account_id
        self.statement_date = statement_date
        self.statement_balance = statement_balance
        self.book_balance = book_balance
        self.difference = difference
        self.status = status
        self.created_by = created_by
        self.notes = notes

    def table_name(self):
        return "bank_reconciliation_sessions"

    def fields(self):
        return ["id", "account_id", "statement_date", "statement_balance", "book_balance",
                "difference", "status", "created_date", "completed_date", "created_by", "notes"]

    def primary_key(self):
        return "id"

    def create_reconciliation_session(self, account_id, statement_date, statement_balance, created_by=None):
        """Create a new bank reconciliation session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Calculate book balance for the statement date
            book_balance = self._calculate_book_balance(cursor, account_id, statement_date)
            difference = statement_balance - book_balance
            
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute('''
                INSERT INTO bank_reconciliation_sessions 
                (account_id, statement_date, statement_balance, book_balance, difference, 
                 status, created_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (account_id, statement_date, statement_balance, book_balance, difference,
                  'pending', current_date, created_by))
            
            session_id = cursor.lastrowid
            conn.commit()
            return session_id
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error creating reconciliation session: {str(e)}")
        finally:
            if conn:
                conn.close()

    def _calculate_book_balance(self, cursor, account_id, statement_date):
        """Calculate the book balance up to the statement date"""
        cursor.execute('''
            SELECT COALESCE(SUM(
                CASE 
                    WHEN type = 'income' THEN amount
                    WHEN type = 'expense' THEN -amount
                    ELSE 0
                END
            ), 0) as balance
            FROM transactions 
            WHERE account_id = ? AND date <= ?
        ''', (account_id, statement_date))
        
        result = cursor.fetchone()
        return result[0] if result else 0.0

    def get_reconciliation_session(self, session_id):
        """Get a specific reconciliation session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT rs.*, a.name as account_name
                FROM bank_reconciliation_sessions rs
                JOIN accounts a ON rs.account_id = a.id
                WHERE rs.id = ?
            ''', (session_id,))
            
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'account_id': row[1],
                    'statement_date': row[2],
                    'statement_balance': row[3],
                    'book_balance': row[4],
                    'difference': row[5],
                    'status': row[6],
                    'created_date': row[7],
                    'completed_date': row[8],
                    'created_by': row[9],
                    'notes': row[10],
                    'account_name': row[11]
                }
            return None
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving reconciliation session: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_all_reconciliation_sessions(self, account_id=None, status=None):
        """Get all reconciliation sessions with optional filtering"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            query = '''
                SELECT rs.*, a.name as account_name
                FROM bank_reconciliation_sessions rs
                JOIN accounts a ON rs.account_id = a.id
                WHERE 1=1
            '''
            params = []
            
            if account_id:
                query += " AND rs.account_id = ?"
                params.append(account_id)
            
            if status:
                query += " AND rs.status = ?"
                params.append(status)
            
            query += " ORDER BY rs.created_date DESC"
            
            cursor.execute(query, params)
            
            sessions = []
            for row in cursor.fetchall():
                session = {
                    'id': row[0],
                    'account_id': row[1],
                    'statement_date': row[2],
                    'statement_balance': row[3],
                    'book_balance': row[4],
                    'difference': row[5],
                    'status': row[6],
                    'created_date': row[7],
                    'completed_date': row[8],
                    'created_by': row[9],
                    'notes': row[10],
                    'account_name': row[11]
                }
                sessions.append(session)
            
            return sessions
            
        except sqlite3.Error as e:
            raise Exception(f"Error retrieving reconciliation sessions: {str(e)}")
        finally:
            if conn:
                conn.close()

    def update_reconciliation_status(self, session_id, status, notes=None):
        """Update the status of a reconciliation session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            update_fields = ["status = ?"]
            params = [status]
            
            if status == 'completed':
                update_fields.append("completed_date = ?")
                params.append(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            
            if notes:
                update_fields.append("notes = ?")
                params.append(notes)
            
            params.append(session_id)
            
            cursor.execute(f'''
                UPDATE bank_reconciliation_sessions 
                SET {', '.join(update_fields)}
                WHERE id = ?
            ''', params)
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating reconciliation status: {str(e)}")
        finally:
            if conn:
                conn.close()

    def delete_reconciliation_session(self, session_id):
        """Delete a reconciliation session and all related data"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Delete related records first
            cursor.execute("DELETE FROM reconciliation_matches WHERE reconciliation_session_id = ?", (session_id,))
            cursor.execute("DELETE FROM bank_statement_transactions WHERE reconciliation_session_id = ?", (session_id,))
            cursor.execute("DELETE FROM bank_reconciliation_sessions WHERE id = ?", (session_id,))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting reconciliation session: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_reconciliation_summary(self, session_id):
        """Get a summary of the reconciliation session"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()
            
            # Get session details
            session = self.get_reconciliation_session(session_id)
            if not session:
                return None
            
            # Get bank statement transaction counts
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_bank_transactions,
                    COUNT(CASE WHEN match_status = 'matched' THEN 1 END) as matched_bank_transactions,
                    COUNT(CASE WHEN match_status = 'unmatched' THEN 1 END) as unmatched_bank_transactions
                FROM bank_statement_transactions 
                WHERE reconciliation_session_id = ?
            ''', (session_id,))
            
            bank_stats = cursor.fetchone()
            
            # Get book transaction counts for the period
            cursor.execute('''
                SELECT COUNT(*) as total_book_transactions
                FROM transactions 
                WHERE account_id = ? AND date <= ? AND reconciled = 0
            ''', (session['account_id'], session['statement_date']))
            
            book_stats = cursor.fetchone()
            
            # Get match statistics
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(match_confidence) as avg_confidence
                FROM reconciliation_matches 
                WHERE reconciliation_session_id = ?
            ''', (session_id,))
            
            match_stats = cursor.fetchone()
            
            summary = {
                'session': session,
                'bank_transactions': {
                    'total': bank_stats[0] if bank_stats else 0,
                    'matched': bank_stats[1] if bank_stats else 0,
                    'unmatched': bank_stats[2] if bank_stats else 0
                },
                'book_transactions': {
                    'total': book_stats[0] if book_stats else 0
                },
                'matches': {
                    'total': match_stats[0] if match_stats else 0,
                    'avg_confidence': match_stats[1] if match_stats else 0.0
                }
            }
            
            return summary
            
        except sqlite3.Error as e:
            raise Exception(f"Error getting reconciliation summary: {str(e)}")
        finally:
            if conn:
                conn.close()
