import tkinter as tk
from tkinter import messagebox
from datetime import datetime, timedelta
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

from model.journal_entry import JournalEntry
from view.components.date_picker import DatePicker


class JournalEntryListFrame(ttk.Frame):
    """Frame for viewing and managing journal entries"""

    def __init__(self, parent, company_name, db_path, close_callback):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Journal Entries - {company_name}"

        # Create model
        self.journal_entry_model = JournalEntry(self.db_path)

        self.create_widgets()
        self.load_journal_entries()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk.Label(
            header_frame,
            text=self.title,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(side="left")

        # Close button
        close_button = ttk.Button(
            header_frame,
            text="Close",
            command=self.close_callback,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")

        # Filters frame
        filters_frame = ttk.LabelFrame(main_frame, text="Filters", padding=10)
        filters_frame.pack(fill="x", pady=(0, 10))

        # Date range filters
        ttk.Label(filters_frame, text="From Date:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.start_date_picker = DatePicker(filters_frame)
        self.start_date_picker.grid(row=0, column=1, sticky="w", padx=(0, 10))
        self.start_date_picker.set_date(datetime.now() - timedelta(days=30))

        ttk.Label(filters_frame, text="To Date:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        self.end_date_picker = DatePicker(filters_frame)
        self.end_date_picker.grid(row=0, column=3, sticky="w", padx=(0, 10))
        self.end_date_picker.set_date(datetime.now())

        # Tags filter
        ttk.Label(filters_frame, text="Tags:").grid(row=0, column=4, sticky="w", padx=(0, 5))
        self.tags_var = tk.StringVar()
        tags_entry = ttk.Entry(filters_frame, textvariable=self.tags_var, width=15)
        tags_entry.grid(row=0, column=5, sticky="w", padx=(0, 10))

        # Filter button
        filter_button = ttk.Button(
            filters_frame,
            text="Apply Filters",
            command=self.load_journal_entries,
            bootstyle=PRIMARY
        )
        filter_button.grid(row=0, column=6, padx=10)

        # Clear filters button
        clear_button = ttk.Button(
            filters_frame,
            text="Clear",
            command=self.clear_filters,
            bootstyle=SECONDARY
        )
        clear_button.grid(row=0, column=7, padx=5)

        # Journal Entries Treeview
        tree_frame = ttk.LabelFrame(main_frame, text="Journal Entries", padding=10)
        tree_frame.pack(fill="both", expand=True)

        # Create treeview
        columns = ("entry_number", "date", "description", "reference", "total_debits", "total_credits", "status", "created_by")
        self.journal_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

        # Define headings
        self.journal_tree.heading("entry_number", text="Entry #")
        self.journal_tree.heading("date", text="Date")
        self.journal_tree.heading("description", text="Description")
        self.journal_tree.heading("reference", text="Reference")
        self.journal_tree.heading("total_debits", text="Total Debits")
        self.journal_tree.heading("total_credits", text="Total Credits")
        self.journal_tree.heading("status", text="Status")
        self.journal_tree.heading("created_by", text="Created By")

        # Define columns
        self.journal_tree.column("entry_number", width=100, stretch=False)
        self.journal_tree.column("date", width=100, stretch=False)
        self.journal_tree.column("description", width=200)
        self.journal_tree.column("reference", width=100)
        self.journal_tree.column("total_debits", width=100, anchor="e")
        self.journal_tree.column("total_credits", width=100, anchor="e")
        self.journal_tree.column("status", width=80, anchor="center")
        self.journal_tree.column("created_by", width=100)

        # Add scrollbars
        scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical", command=self.journal_tree.yview)
        scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.journal_tree.xview)
        self.journal_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Pack treeview and scrollbars
        self.journal_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")

        # Bind double-click to view details
        self.journal_tree.bind("<Double-1>", self.view_journal_entry_details)

        # Context menu
        self.create_context_menu()

        # Action buttons frame
        actions_frame = ttk.Frame(main_frame)
        actions_frame.pack(fill="x", pady=(10, 0))

        # View Details button
        view_button = ttk.Button(
            actions_frame,
            text="View Details",
            command=self.view_journal_entry_details,
            bootstyle=INFO
        )
        view_button.pack(side="left", padx=(0, 5))

        # Validate button
        validate_button = ttk.Button(
            actions_frame,
            text="Validate Entry",
            command=self.validate_selected_entry,
            bootstyle=SUCCESS
        )
        validate_button.pack(side="left", padx=5)

        # Delete button
        delete_button = ttk.Button(
            actions_frame,
            text="Delete Entry",
            command=self.delete_selected_entry,
            bootstyle=DANGER
        )
        delete_button.pack(side="left", padx=5)

        # Refresh button
        refresh_button = ttk.Button(
            actions_frame,
            text="Refresh",
            command=self.load_journal_entries,
            bootstyle=SECONDARY
        )
        refresh_button.pack(side="right")

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))

    def create_context_menu(self):
        """Create context menu for the treeview"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="View Details", command=self.view_journal_entry_details)
        self.context_menu.add_command(label="Validate Entry", command=self.validate_selected_entry)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Delete Entry", command=self.delete_selected_entry)

        # Bind right-click to show context menu
        self.journal_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """Show context menu"""
        # Select the item under cursor
        item = self.journal_tree.identify_row(event.y)
        if item:
            self.journal_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def clear_filters(self):
        """Clear all filters"""
        self.start_date_picker.set_date(datetime.now() - timedelta(days=30))
        self.end_date_picker.set_date(datetime.now())
        self.tags_var.set("")
        self.load_journal_entries()

    def load_journal_entries(self):
        """Load journal entries into the treeview"""
        # Clear existing items
        for item in self.journal_tree.get_children():
            self.journal_tree.delete(item)

        try:
            # Get filter values
            start_date = self.start_date_picker.get_date()
            end_date = self.end_date_picker.get_date()
            tags = self.tags_var.get().strip()

            start_date_str = start_date.strftime("%Y-%m-%d") if start_date else None
            end_date_str = end_date.strftime("%Y-%m-%d") if end_date else None
            tags_filter = tags if tags else None

            # Get journal entries
            entries = self.journal_entry_model.get_all_journal_entries(
                start_date=start_date_str,
                end_date=end_date_str,
                tags=tags_filter
            )

            # Add entries to treeview
            for entry in entries:
                status = "Balanced" if entry['is_balanced'] else "Unbalanced"
                
                self.journal_tree.insert(
                    "", "end",
                    values=(
                        entry['entry_number'],
                        entry['date'],
                        entry['description'],
                        entry['reference'] or "",
                        f"${entry['total_debits']:.2f}",
                        f"${entry['total_credits']:.2f}",
                        status,
                        entry['created_by'] or ""
                    ),
                    tags=(status.lower(),)
                )

            # Configure row colors
            self.journal_tree.tag_configure("balanced", background="#e6ffe6")
            self.journal_tree.tag_configure("unbalanced", background="#ffe6e6")

            self.status_var.set(f"Loaded {len(entries)} journal entries")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load journal entries: {str(e)}")
            self.status_var.set("Error loading journal entries")

    def view_journal_entry_details(self, event=None):
        """View details of the selected journal entry"""
        selection = self.journal_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a journal entry to view")
            return

        try:
            # Get the selected entry
            item = selection[0]
            entry_number = self.journal_tree.item(item)['values'][0]
            
            # Find the entry by entry number
            entries = self.journal_entry_model.get_all_journal_entries()
            selected_entry = None
            for entry in entries:
                if entry['entry_number'] == entry_number:
                    selected_entry = entry
                    break
            
            if not selected_entry:
                messagebox.showerror("Error", "Journal entry not found")
                return

            # Get full entry details with lines
            entry_details = self.journal_entry_model.get_journal_entry(selected_entry['id'])
            
            if entry_details:
                self.show_entry_details_dialog(entry_details)
            else:
                messagebox.showerror("Error", "Failed to load entry details")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to view entry details: {str(e)}")

    def show_entry_details_dialog(self, entry):
        """Show a dialog with journal entry details"""
        # Create details window
        details_window = tk.Toplevel(self)
        details_window.title(f"Journal Entry Details - {entry['entry_number']}")
        details_window.geometry("800x600")
        details_window.transient(self)
        details_window.grab_set()

        # Main frame
        main_frame = ttk.Frame(details_window, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header information
        header_frame = ttk.LabelFrame(main_frame, text="Entry Information", padding=10)
        header_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(header_frame, text=f"Entry Number: {entry['entry_number']}", font=("Segoe UI", 10, "bold")).pack(anchor="w")
        ttk.Label(header_frame, text=f"Date: {entry['date']}").pack(anchor="w")
        ttk.Label(header_frame, text=f"Description: {entry['description']}").pack(anchor="w")
        ttk.Label(header_frame, text=f"Reference: {entry['reference'] or 'N/A'}").pack(anchor="w")
        ttk.Label(header_frame, text=f"Created By: {entry['created_by'] or 'N/A'}").pack(anchor="w")
        ttk.Label(header_frame, text=f"Status: {'Balanced' if entry['is_balanced'] else 'Unbalanced'}").pack(anchor="w")

        # Lines information
        lines_frame = ttk.LabelFrame(main_frame, text="Journal Lines", padding=10)
        lines_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Create treeview for lines
        line_columns = ("line_number", "account", "description", "debit", "credit")
        lines_tree = ttk.Treeview(lines_frame, columns=line_columns, show="headings", height=10)

        # Define headings
        lines_tree.heading("line_number", text="Line #")
        lines_tree.heading("account", text="Account")
        lines_tree.heading("description", text="Description")
        lines_tree.heading("debit", text="Debit")
        lines_tree.heading("credit", text="Credit")

        # Define columns
        lines_tree.column("line_number", width=60, stretch=False)
        lines_tree.column("account", width=200)
        lines_tree.column("description", width=200)
        lines_tree.column("debit", width=100, anchor="e")
        lines_tree.column("credit", width=100, anchor="e")

        # Add lines to treeview
        for line in entry['lines']:
            debit_str = f"${line['debit_amount']:.2f}" if line['debit_amount'] > 0 else ""
            credit_str = f"${line['credit_amount']:.2f}" if line['credit_amount'] > 0 else ""
            
            lines_tree.insert(
                "", "end",
                values=(
                    line['line_number'],
                    line['account_name'],
                    line['description'] or "",
                    debit_str,
                    credit_str
                )
            )

        lines_tree.pack(fill="both", expand=True)

        # Totals frame
        totals_frame = ttk.Frame(main_frame)
        totals_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(totals_frame, text=f"Total Debits: ${entry['total_debits']:.2f}", font=("Segoe UI", 10, "bold")).pack(side="left", padx=(0, 20))
        ttk.Label(totals_frame, text=f"Total Credits: ${entry['total_credits']:.2f}", font=("Segoe UI", 10, "bold")).pack(side="left")

        # Close button
        close_button = ttk.Button(main_frame, text="Close", command=details_window.destroy, bootstyle=SECONDARY)
        close_button.pack(pady=10)

    def validate_selected_entry(self):
        """Validate the selected journal entry"""
        selection = self.journal_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a journal entry to validate")
            return

        try:
            # Get the selected entry
            item = selection[0]
            entry_number = self.journal_tree.item(item)['values'][0]
            
            # Find the entry by entry number
            entries = self.journal_entry_model.get_all_journal_entries()
            selected_entry = None
            for entry in entries:
                if entry['entry_number'] == entry_number:
                    selected_entry = entry
                    break
            
            if not selected_entry:
                messagebox.showerror("Error", "Journal entry not found")
                return

            # Validate the entry
            is_valid, message = self.journal_entry_model.validate_journal_entry(selected_entry['id'])
            
            if is_valid:
                messagebox.showinfo("Validation Result", f"✓ {message}")
            else:
                messagebox.showwarning("Validation Result", f"✗ {message}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to validate entry: {str(e)}")

    def delete_selected_entry(self):
        """Delete the selected journal entry"""
        selection = self.journal_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a journal entry to delete")
            return

        try:
            # Get the selected entry
            item = selection[0]
            entry_number = self.journal_tree.item(item)['values'][0]
            
            # Confirm deletion
            if not messagebox.askyesno("Confirm Deletion", f"Are you sure you want to delete journal entry {entry_number}?\n\nThis action cannot be undone and will reverse all account balance changes."):
                return
            
            # Find the entry by entry number
            entries = self.journal_entry_model.get_all_journal_entries()
            selected_entry = None
            for entry in entries:
                if entry['entry_number'] == entry_number:
                    selected_entry = entry
                    break
            
            if not selected_entry:
                messagebox.showerror("Error", "Journal entry not found")
                return

            # Delete the entry
            if self.journal_entry_model.delete_journal_entry(selected_entry['id']):
                messagebox.showinfo("Success", f"Journal entry {entry_number} deleted successfully")
                self.load_journal_entries()  # Refresh the list
                self.status_var.set(f"Journal entry {entry_number} deleted")
            else:
                messagebox.showerror("Error", "Failed to delete journal entry")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete entry: {str(e)}")
