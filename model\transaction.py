import sqlite3
import time
from datetime import datetime


class Transaction:
    def __init__(self, id, date, amount, description, category_id, account_id, reconciled):
        self.id = id
        self.date = date
        self.amount = amount
        self.description = description
        self.category_id = category_id
        self.account_id = account_id
        self.reconciled = reconciled

    def validate(self):
        if self.amount <= 0:
            raise ValueError("Transaction amount must be positive")
        if not self.date:
            raise ValueError("Transaction date is required")

    def __str__(self):
        return f"Transaction({self.date}, {self.amount}, {self.description})"


class TransactionManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.connection = None

    def _get_connection(self):
        """Get a database connection with proper settings"""
        conn = sqlite3.connect(self.db_path, timeout=30)
        cursor = conn.cursor()
        cursor.execute("PRAGMA busy_timeout = 10000")
        cursor.execute("PRAGMA foreign_keys = ON")
        return conn, cursor

    def begin_transaction(self):
        """Begin a database transaction"""
        if self.connection is not None:
            raise ValueError("Transaction already in progress")

        self.connection = sqlite3.connect(self.db_path)
        cursor = self.connection.cursor()
        cursor.execute("PRAGMA busy_timeout = 10000")
        cursor.execute("PRAGMA foreign_keys = ON")
        self.connection.execute("BEGIN TRANSACTION")

    def commit_transaction(self):
        """Commit the current transaction"""
        if self.connection is None:
            raise ValueError("No transaction in progress")

        self.connection.commit()
        self.connection.close()
        self.connection = None

    def rollback_transaction(self):
        """Rollback the current transaction"""
        if self.connection is None:
            raise ValueError("No transaction in progress")

        self.connection.rollback()
        self.connection.close()
        self.connection = None

    def add_transaction(self, date, amount, account_id, description="", category_id=None,
                        type_name="expense", reconciled=0, currency_code=None, exchange_rate=None, base_amount=None):
        """
        Add a new transaction to the database with multi-currency support
        type_name should be 'income', 'expense', 'transfer', or 'initial'
        """
        conn = None
        try:
            conn, cursor = self._get_connection()

            # Get account currency if not provided
            if not currency_code:
                cursor.execute("SELECT currency FROM accounts WHERE id = ?", (account_id,))
                result = cursor.fetchone()
                currency_code = result[0] if result else 'USD'

            # Calculate base amount if not provided
            if base_amount is None:
                if exchange_rate and exchange_rate != 1.0:
                    base_amount = float(amount) * exchange_rate
                else:
                    base_amount = amount

            # Set default exchange rate
            if exchange_rate is None:
                exchange_rate = 1.0

            cursor.execute(
                """INSERT INTO transactions
                   (date, amount, account_id, description, category_id, type, reconciled,
                    currency_code, exchange_rate, base_amount)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (date, amount, account_id, description, category_id, type_name, reconciled,
                 currency_code, exchange_rate, base_amount)
            )

            transaction_id = cursor.lastrowid

            # Update account balance
            self._update_account_balance(cursor, account_id)

            conn.commit()
            return transaction_id
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error adding transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def edit_transaction(self, transaction_id, date=None, amount=None, account_id=None,
                         description=None, category_id=None, type_name=None, reconciled=None):
        """
        Edit an existing transaction
        Only provided parameters will be updated
        """
        conn = None
        try:
            conn, cursor = self._get_connection()

            # Get current transaction data
            cursor.execute(
                """SELECT date, amount, account_id, description, category_id, type, reconciled
                   FROM transactions WHERE id = ?""",
                (transaction_id,)
            )

            current = cursor.fetchone()
            if not current:
                raise ValueError(f"Transaction with ID {transaction_id} not found")

            # Use current values if new ones are not provided
            date = date if date is not None else current[0]
            amount = amount if amount is not None else current[1]
            old_account_id = current[2]
            new_account_id = account_id if account_id is not None else old_account_id
            description = description if description is not None else current[3]
            category_id = category_id if category_id is not None else current[4]
            type_name = type_name if type_name is not None else current[5]
            reconciled = reconciled if reconciled is not None else current[6]

            # Update the transaction
            cursor.execute(
                """UPDATE transactions
                   SET date = ?, amount = ?, account_id = ?, description = ?,
                       category_id = ?, type = ?, reconciled = ?
                   WHERE id = ?""",
                (date, amount, new_account_id, description, category_id, type_name, reconciled, transaction_id)
            )

            # Update account balances if account changed
            if old_account_id != new_account_id:
                self._update_account_balance(cursor, old_account_id)
                self._update_account_balance(cursor, new_account_id)
            else:
                self._update_account_balance(cursor, new_account_id)

            conn.commit()
            return True
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error editing transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def delete_transaction(self, transaction_id):
        """
        Delete a transaction from the database
        """
        conn = None
        try:
            conn, cursor = self._get_connection()

            # Get account ID before deleting
            cursor.execute("SELECT account_id FROM transactions WHERE id = ?", (transaction_id,))
            result = cursor.fetchone()

            if not result:
                raise ValueError(f"Transaction with ID {transaction_id} not found")

            account_id = result[0]

            # Delete the transaction
            cursor.execute("DELETE FROM transactions WHERE id = ?", (transaction_id,))

            # Update account balance
            self._update_account_balance(cursor, account_id)

            conn.commit()
            return True
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def _update_account_balance(self, cursor, account_id):
        """Update account balance based on transactions"""
        # Get opening balance
        cursor.execute("SELECT opening_balance FROM accounts WHERE id = ?", (account_id,))
        opening_balance = cursor.fetchone()[0]

        # Sum all non-initial transactions
        cursor.execute("""
            SELECT SUM(CASE WHEN type = 'expense' THEN -amount
                            WHEN type = 'income' THEN amount
                            ELSE 0 END)
            FROM transactions
            WHERE account_id = ? AND type != 'initial'
        """, (account_id,))

        transaction_sum = cursor.fetchone()[0] or 0
        new_balance = opening_balance + transaction_sum

        # Update account
        cursor.execute("""
            UPDATE accounts SET current_balance = ?
            WHERE id = ?
        """, (new_balance, account_id))

    def add_transfer(self, date, amount, from_account_id, to_account_id, description="Transfer",
                    exchange_rate=None, to_amount=None):
        """Add a transfer between accounts with multi-currency support"""
        conn = None
        try:
            conn, cursor = self._get_connection()

            # Get account currencies
            cursor.execute("SELECT currency FROM accounts WHERE id = ?", (from_account_id,))
            from_currency = cursor.fetchone()[0] if cursor.fetchone() else 'USD'

            cursor.execute("SELECT currency FROM accounts WHERE id = ?", (to_account_id,))
            to_currency = cursor.fetchone()[0] if cursor.fetchone() else 'USD'

            # Calculate amounts for different currencies
            if from_currency != to_currency:
                if exchange_rate is None:
                    # Try to get exchange rate from currency model
                    try:
                        from model.currency import Currency
                        currency_model = Currency(self.db_path)
                        exchange_rate = currency_model.get_exchange_rate(from_currency, to_currency)
                    except:
                        exchange_rate = 1.0

                if to_amount is None:
                    to_amount = float(amount) * exchange_rate
            else:
                exchange_rate = 1.0
                to_amount = amount

            # Add withdrawal transaction from source account
            cursor.execute(
                """INSERT INTO transactions
                   (date, amount, account_id, description, type, reconciled,
                    currency_code, exchange_rate, base_amount)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (date, amount, from_account_id,
                 f"{description} (to {self._get_account_name(cursor, to_account_id)})",
                 "expense", 0, from_currency, 1.0, amount)
            )

            from_transaction_id = cursor.lastrowid

            # Add deposit transaction to destination account
            cursor.execute(
                """INSERT INTO transactions
                   (date, amount, account_id, description, type, reconciled,
                    currency_code, exchange_rate, base_amount)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (date, to_amount, to_account_id,
                 f"{description} (from {self._get_account_name(cursor, from_account_id)})",
                 "income", 0, to_currency, exchange_rate, amount)
            )

            to_transaction_id = cursor.lastrowid

            # Update account balances
            self._update_account_balance(cursor, from_account_id)
            self._update_account_balance(cursor, to_account_id)

            conn.commit()
            return (from_transaction_id, to_transaction_id)
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error adding transfer: {str(e)}")
        finally:
            if conn:
                conn.close()

    def _get_account_name(self, cursor, account_id):
        """Get the name of an account"""
        cursor.execute("SELECT name FROM accounts WHERE id = ?", (account_id,))
        result = cursor.fetchone()
        return result[0] if result else "Unknown Account"

    def edit_transaction(self, transaction_id, updates):
        """Edit an existing transaction"""
        conn = None
        try:
            conn, cursor = self._get_connection()

            # Get current account ID
            cursor.execute("SELECT account_id FROM transactions WHERE id = ?", (transaction_id,))
            result = cursor.fetchone()
            if not result:
                raise ValueError(f"Transaction {transaction_id} not found")

            current_account_id = result[0]

            # Build update query dynamically based on updates dictionary
            set_clause = ", ".join([f"{key} = ?" for key in updates.keys()])
            values = list(updates.values())
            values.append(transaction_id)

            cursor.execute(
                f"UPDATE transactions SET {set_clause} WHERE id = ?",
                values
            )

            # Get new account ID if it was updated
            new_account_id = updates.get('account_id', current_account_id)

            # Update account balance(s)
            self._update_account_balance(cursor, current_account_id)
            if new_account_id != current_account_id:
                self._update_account_balance(cursor, new_account_id)

            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error editing transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def delete_transaction(self, transaction_id):
        """Delete a transaction by ID"""
        conn = None
        try:
            conn, cursor = self._get_connection()

            # Get account ID before deleting
            cursor.execute("SELECT account_id FROM transactions WHERE id = ?", (transaction_id,))
            result = cursor.fetchone()
            if not result:
                return False

            account_id = result[0]

            # Delete the transaction
            cursor.execute("DELETE FROM transactions WHERE id = ?", (transaction_id,))

            # Update account balance
            self._update_account_balance(cursor, account_id)

            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error deleting transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_transaction(self, transaction_id):
        """Get a single transaction by ID"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT t.*,
                       a.name as account_name,
                       c.name as category_name,
                       c.type as category_type
                FROM transactions t
                LEFT JOIN accounts a ON t.account_id = a.id
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.id = ?
            """, (transaction_id,))

            result = cursor.fetchone()
            return dict(result) if result else None

        except sqlite3.Error as e:
            raise Exception(f"Error fetching transaction: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_transactions(self, filters=None, date_from=None, date_to=None, account_id=None,
                         category_id=None, type_name=None, reconciled=None, sort_by="date",
                         sort_order="DESC", limit=None, offset=0):
        """
        Get transactions with optional filters

        Parameters:
        - filters: Dictionary with column name keys and substring value filters
        - date_from: Start date for filtering (inclusive)
        - date_to: End date for filtering (inclusive)
        - account_id: Filter by account ID
        - category_id: Filter by category ID
        - type_name: Filter by transaction type
        - reconciled: Filter by reconciliation status (0=no, 1=yes)
        - sort_by: Column to sort by
        - sort_order: "ASC" or "DESC"
        - limit: Maximum number of rows to return
        - offset: Number of rows to skip
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = """
                SELECT t.*,
                       a.name as account_name,
                       c.name as category_name,
                       c.type as category_type
                FROM transactions t
                LEFT JOIN accounts a ON t.account_id = a.id
                LEFT JOIN categories c ON t.category_id = c.id
            """

            conditions = []
            params = []

            # Add specific filters
            if date_from:
                conditions.append("t.date >= ?")
                params.append(date_from)

            if date_to:
                conditions.append("t.date <= ?")
                params.append(date_to)

            if account_id:
                conditions.append("t.account_id = ?")
                params.append(account_id)

            if category_id:
                conditions.append("t.category_id = ?")
                params.append(category_id)

            if type_name:
                conditions.append("t.type = ?")
                params.append(type_name)

            if reconciled is not None:
                conditions.append("t.reconciled = ?")
                params.append(reconciled)

            # Add text search filters
            if filters:
                for key, value in filters.items():
                    if key.startswith("t."):
                        conditions.append(f"{key} LIKE ?")
                        params.append(f"%{value}%")
                    elif key == "description":
                        conditions.append("t.description LIKE ?")
                        params.append(f"%{value}%")

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            # Add sorting
            valid_sort_columns = ["date", "amount", "description", "type", "reconciled"]
            sort_column = sort_by if sort_by in valid_sort_columns else "date"

            valid_sort_orders = ["ASC", "DESC"]
            order = sort_order.upper() if sort_order.upper() in valid_sort_orders else "DESC"

            query += f" ORDER BY t.{sort_column} {order}"

            # Add pagination
            if limit is not None:
                query += " LIMIT ? OFFSET ?"
                params.extend([limit, offset])
            elif offset > 0:
                # In SQLite, OFFSET requires LIMIT, so use a very large limit if none specified
                query += " LIMIT -1 OFFSET ?"
                params.append(offset)

            cursor.execute(query, params)
            results = [dict(row) for row in cursor.fetchall()]

            return results
        except sqlite3.Error as e:
            raise Exception(f"Error fetching transactions: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_balance_summary(self, account_id=None, date_from=None, date_to=None):
        """Get summary of income, expenses, and balance"""
        conn = None
        try:
            conn, cursor = self._get_connection()

            conditions = []
            params = []

            if account_id:
                conditions.append("account_id = ?")
                params.append(account_id)

            if date_from:
                conditions.append("date >= ?")
                params.append(date_from)

            if date_to:
                conditions.append("date <= ?")
                params.append(date_to)

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            # Get total income
            income_query = f"SELECT SUM(amount) FROM transactions WHERE type = 'income'{where_clause}"
            cursor.execute(income_query, params)
            total_income = cursor.fetchone()[0] or 0

            # Get total expenses
            expense_query = f"SELECT SUM(amount) FROM transactions WHERE type = 'expense'{where_clause}"
            cursor.execute(expense_query, params)
            total_expenses = cursor.fetchone()[0] or 0

            return {
                "income": total_income,
                "expenses": total_expenses,
                "balance": total_income - total_expenses
            }
        except sqlite3.Error as e:
            raise Exception(f"Error calculating balance: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_transactions_by_category(self, date_from=None, date_to=None, account_id=None):
        """Get transaction totals grouped by category"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            conditions = []
            params = []

            if date_from:
                conditions.append("t.date >= ?")
                params.append(date_from)

            if date_to:
                conditions.append("t.date <= ?")
                params.append(date_to)

            if account_id:
                conditions.append("t.account_id = ?")
                params.append(account_id)

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
                SELECT c.id as category_id, c.name, c.type as category_type,
                       SUM(t.amount) as total
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                {where_clause}
                GROUP BY t.category_id, c.name, c.type
                ORDER BY c.type, total DESC
            """

            cursor.execute(query, params)
            results = [dict(row) for row in cursor.fetchall()]

            return results
        except sqlite3.Error as e:
            raise Exception(f"Error fetching category summary: {str(e)}")
        finally:
            if conn:
                conn.close()

    def mark_as_reconciled(self, transaction_id, reconciled=1):
        """Mark a transaction as reconciled or unreconciled"""
        conn = None
        try:
            conn, cursor = self._get_connection()

            cursor.execute(
                "UPDATE transactions SET reconciled = ? WHERE id = ?",
                (reconciled, transaction_id)
            )

            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise Exception(f"Error updating reconciliation status: {str(e)}")
        finally:
            if conn:
                conn.close()

    def get_unreconciled_transactions(self, account_id):
        """Get all unreconciled transactions for an account"""
        return self.get_transactions(
            account_id=account_id,
            reconciled=0,
            sort_by="date",
            sort_order="ASC",
            limit=1000
        )

    def is_duplicate_transaction(self, date, amount, description, account_id):
        """Check if a transaction with the same date, amount, and description exists"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = """
            SELECT COUNT(*) as count
            FROM transactions
            WHERE date = ? AND amount = ? AND description = ? AND account_id = ?
            """

            cursor.execute(query, (date, amount, description, account_id))
            result = cursor.fetchone()

            conn.close()

            return result['count'] > 0

        except Exception as e:
            raise Exception(f"Error checking for duplicate transaction: {str(e)}")

    def delete_duplicate_transaction(self, date, amount, description, account_id):
        """Delete a transaction with the same date, amount, and description"""
        try:
            conn, cursor = self._get_connection()

            # Find the transaction
            query = """
            SELECT id
            FROM transactions
            WHERE date = ? AND amount = ? AND description = ? AND account_id = ?
            LIMIT 1
            """

            cursor.execute(query, (date, amount, description, account_id))
            result = cursor.fetchone()

            if result:
                transaction_id = result[0]

                # Delete the transaction
                cursor.execute("DELETE FROM transactions WHERE id = ?", (transaction_id,))

                # Update account balance
                self.update_account_balance(account_id)

                conn.commit()

            conn.close()

            return True

        except Exception as e:
            conn.rollback()
            conn.close()
            raise Exception(f"Error deleting duplicate transaction: {str(e)}")

    def get_category_by_name(self, name):
        """Get a category by name"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = "SELECT * FROM categories WHERE name = ?"

            cursor.execute(query, (name,))
            category = cursor.fetchone()

            conn.close()

            if category:
                return dict(category)
            else:
                return None

        except Exception as e:
            raise Exception(f"Error getting category by name: {str(e)}")

    def get_accounts(self):
        """Get all accounts"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = "SELECT * FROM accounts ORDER BY name"

            cursor.execute(query)
            accounts = [dict(row) for row in cursor.fetchall()]

            conn.close()

            return accounts

        except Exception as e:
            raise Exception(f"Error getting accounts: {str(e)}")
