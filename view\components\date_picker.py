import calendar
import datetime
import tkinter as tk
from tkinter import ttk

import ttkbootstrap as ttk
from ttkbootstrap.constants import *


class DatePicker(ttk.Frame):
    """
    A date picker component that allows selecting a date from a calendar
    """
    def __init__(self, parent, initial_date=None, command=None):
        super().__init__(parent)

        # Set initial date (default to today)
        if isinstance(initial_date, tk.StringVar):
            # If a StringVar is passed, use its value
            self.date_var = initial_date
            date_str = self.date_var.get()
            try:
                self.initial_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
            except (ValueError, TypeError):
                self.initial_date = datetime.date.today()
                self.date_var.set(self.format_date(self.initial_date))
        else:
            # Otherwise, create a new StringVar
            self.initial_date = initial_date or datetime.date.today()
            self.date_var = tk.StringVar(value=self.format_date(self.initial_date))

        self.selected_date = self.initial_date
        self.command = command  # Callback when date is selected

        # Create the UI components
        self.create_widgets()

    def create_widgets(self):
        """Create the date picker widgets"""
        # Create a frame for the entry and button
        entry_frame = ttk.Frame(self)
        entry_frame.pack(fill="x")

        # Date entry field
        self.date_entry = ttk.Entry(entry_frame, textvariable=self.date_var, width=12)
        self.date_entry.pack(side="left", fill="x", expand=True)

        # Calendar button
        self.calendar_button = ttk.Button(
            entry_frame,
            text="📅",
            width=3,
            command=self.show_calendar
        )
        self.calendar_button.pack(side="left", padx=(2, 0))

        # Bind events
        self.date_entry.bind("<FocusOut>", self.validate_date)
        self.date_entry.bind("<Return>", self.validate_date)

    def format_date(self, date):
        """Format date as YYYY-MM-DD"""
        return date.strftime("%Y-%m-%d")

    def parse_date(self, date_str):
        """Parse date string in YYYY-MM-DD format"""
        try:
            return datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            return None

    def validate_date(self, event=None):
        """Validate the date entered in the entry field"""
        date_str = self.date_var.get()
        date = self.parse_date(date_str)

        if date:
            self.selected_date = date
            if self.command:
                self.command(self.selected_date)
        else:
            # Reset to the previously selected date
            self.date_var.set(self.format_date(self.selected_date))

    def show_calendar(self):
        """Show the calendar popup"""
        # Create a toplevel window
        top = tk.Toplevel(self)
        top.title("Select Date")
        top.geometry("300x250")
        top.resizable(False, False)
        top.transient(self)  # Set to be on top of the parent window
        top.grab_set()  # Modal

        # Get the current date components
        year = self.selected_date.year
        month = self.selected_date.month

        # Create calendar header
        header_frame = ttk.Frame(top)
        header_frame.pack(fill="x", padx=5, pady=5)

        # Previous month button
        prev_button = ttk.Button(
            header_frame,
            text="<",
            width=3,
            command=lambda: self.change_month(top, year, month, -1)
        )
        prev_button.pack(side="left")

        # Month and year label
        self.month_year_label = ttk.Label(
            header_frame,
            text=f"{calendar.month_name[month]} {year}",
            font=("Segoe UI", 10, "bold")
        )
        self.month_year_label.pack(side="left", expand=True)

        # Next month button
        next_button = ttk.Button(
            header_frame,
            text=">",
            width=3,
            command=lambda: self.change_month(top, year, month, 1)
        )
        next_button.pack(side="right")

        # Create calendar
        self.create_calendar(top, year, month)

        # Position the calendar near the entry field
        x = self.winfo_rootx() + self.date_entry.winfo_x()
        y = self.winfo_rooty() + self.date_entry.winfo_y() + self.date_entry.winfo_height()
        top.geometry(f"+{x}+{y}")

    def create_calendar(self, parent, year, month):
        """Create the calendar for the specified month and year"""
        # Create a frame for the calendar
        if hasattr(self, 'calendar_frame'):
            self.calendar_frame.destroy()

        self.calendar_frame = ttk.Frame(parent)
        self.calendar_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Update the month and year label
        self.month_year_label.config(text=f"{calendar.month_name[month]} {year}")

        # Create day headers
        days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        for i, day in enumerate(days):
            ttk.Label(
                self.calendar_frame,
                text=day,
                font=("Segoe UI", 9, "bold"),
                anchor="center"
            ).grid(row=0, column=i, sticky="nsew", padx=1, pady=1)

        # Get the calendar for the month
        cal = calendar.monthcalendar(year, month)

        # Create day buttons
        for week_idx, week in enumerate(cal):
            for day_idx, day in enumerate(week):
                if day != 0:
                    # Create a button for each day
                    day_button = ttk.Button(
                        self.calendar_frame,
                        text=str(day),
                        width=3,
                        bootstyle=SECONDARY,
                        command=lambda d=day: self.select_date(parent, year, month, d)
                    )

                    # Highlight the selected date
                    if (year, month, day) == (self.selected_date.year, self.selected_date.month, self.selected_date.day):
                        day_button.configure(bootstyle=SUCCESS)

                    day_button.grid(row=week_idx+1, column=day_idx, sticky="nsew", padx=1, pady=1)

        # Configure grid to expand
        for i in range(7):
            self.calendar_frame.columnconfigure(i, weight=1)

    def change_month(self, parent, year, month, delta):
        """Change the displayed month by delta months"""
        month += delta

        if month > 12:
            month = 1
            year += 1
        elif month < 1:
            month = 12
            year -= 1

        self.create_calendar(parent, year, month)

    def select_date(self, parent, year, month, day):
        """Select a date and close the calendar"""
        self.selected_date = datetime.date(year, month, day)
        self.date_var.set(self.format_date(self.selected_date))

        if self.command:
            self.command(self.selected_date)

        parent.destroy()

    def get_date(self):
        """Get the currently selected date"""
        return self.selected_date

    def set_date(self, date):
        """Set the date"""
        self.selected_date = date
        self.date_var.set(self.format_date(date))
