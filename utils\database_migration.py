import os
import sqlite3
from datetime import datetime


class DatabaseMigration:
    """Utility class for migrating and fixing database schemas"""

    @staticmethod
    def migrate_company_database(db_path):
        """Migrate a company database to the latest schema"""
        if not os.path.exists(db_path):
            print(f"Database {db_path} does not exist")
            return False

        try:
            conn = sqlite3.connect(db_path, timeout=30)
            cursor = conn.cursor()

            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")

            # Check if accounts table exists
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='accounts'
            """)

            if not cursor.fetchone():
                print(f"Creating accounts table in {db_path}")
                DatabaseMigration._create_accounts_table(cursor)
            else:
                print(f"Updating accounts table structure in {db_path}")
                DatabaseMigration._update_accounts_table(cursor)

            # Check and create other required tables
            DatabaseMigration._ensure_all_tables_exist(cursor)

            # Update metadata
            DatabaseMigration._update_metadata(cursor, db_path)

            conn.commit()
            print(f"Successfully migrated {db_path}")
            return True

        except sqlite3.Error as e:
            print(f"Error migrating {db_path}: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    @staticmethod
    def _create_accounts_table(cursor):
        """Create the accounts table with full schema"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                currency TEXT NOT NULL DEFAULT 'USD',
                opening_balance REAL NOT NULL DEFAULT 0,
                current_balance REAL NOT NULL DEFAULT 0,
                description TEXT,
                created_date TEXT NOT NULL,
                classification TEXT,
                account_number TEXT,
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (parent_id) REFERENCES accounts (id)
            )
        ''')

    @staticmethod
    def _update_accounts_table(cursor):
        """Update existing accounts table with new columns"""
        # Get current table structure
        cursor.execute("PRAGMA table_info(accounts)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add missing columns
        new_columns = [
            ('classification', 'TEXT'),
            ('account_number', 'TEXT'),
            ('parent_id', 'INTEGER'),
            ('is_active', 'BOOLEAN DEFAULT 1')
        ]

        for column_name, column_type in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE accounts ADD COLUMN {column_name} {column_type}")
                    print(f"Added column {column_name} to accounts table")
                except sqlite3.Error as e:
                    print(f"Error adding column {column_name}: {e}")

        # Update classification for existing accounts
        DatabaseMigration._update_account_classifications(cursor)

    @staticmethod
    def _update_account_classifications(cursor):
        """Update classification for existing accounts based on their type"""
        # Classification mapping
        type_to_classification = {
            # Assets
            'Cash': 'Asset',
            'Checking Account': 'Asset',
            'Savings Account': 'Asset',
            'Petty Cash': 'Asset',
            'Accounts Receivable': 'Asset',
            'Inventory': 'Asset',
            'Equipment': 'Asset',
            'Furniture': 'Asset',
            'Buildings': 'Asset',
            'Land': 'Asset',
            'Vehicles': 'Asset',

            # Liabilities
            'Accounts Payable': 'Liability',
            'Credit Card': 'Liability',
            'Loan': 'Liability',
            'Mortgage': 'Liability',
            'Accrued Expenses': 'Liability',
            'Sales Tax Payable': 'Liability',

            # Equity
            'Owner Equity': 'Equity',
            'Retained Earnings': 'Equity',
            'Capital': 'Equity',

            # Revenue
            'Sales Revenue': 'Revenue',
            'Service Revenue': 'Revenue',
            'Interest Income': 'Revenue',
            'Other Income': 'Revenue',

            # Expenses
            'Office Supplies': 'Expense',
            'Rent Expense': 'Expense',
            'Utilities Expense': 'Expense',
            'Insurance Expense': 'Expense',
            'Professional Fees': 'Expense',
            'Bank Charges': 'Expense',
            'Depreciation Expense': 'Expense',
            'Cost of Goods Sold': 'Expense',
            'Salaries Expense': 'Expense'
        }

        for account_type, classification in type_to_classification.items():
            cursor.execute('''
                UPDATE accounts
                SET classification = ?
                WHERE type = ? AND (classification IS NULL OR classification = '')
            ''', (classification, account_type))

    @staticmethod
    def _ensure_all_tables_exist(cursor):
        """Ensure all required tables exist"""

        # Categories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL
            )
        ''')

        # Transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                category_id INTEGER,
                account_id INTEGER,
                reconciled BOOLEAN NOT NULL DEFAULT 0,
                type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id),
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        ''')

        # Metadata table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS metadata (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        ''')

        # Journal entries table (for advanced double-entry)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number TEXT UNIQUE NOT NULL,
                date TEXT NOT NULL,
                description TEXT NOT NULL,
                reference TEXT,
                total_debits REAL NOT NULL DEFAULT 0.0,
                total_credits REAL NOT NULL DEFAULT 0.0,
                is_balanced BOOLEAN NOT NULL DEFAULT 0,
                tags TEXT,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Journal lines table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_lines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                journal_entry_id INTEGER NOT NULL,
                line_number INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                debit_amount REAL NOT NULL DEFAULT 0.0,
                credit_amount REAL NOT NULL DEFAULT 0.0,
                category_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES accounts (id),
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # Add multi-currency support to transactions table
        DatabaseMigration._add_currency_columns_to_transactions(cursor)

    @staticmethod
    def _add_currency_columns_to_transactions(cursor):
        """Add currency support columns to transactions table"""
        try:
            # Check if currency columns exist, if not add them
            cursor.execute("PRAGMA table_info(transactions)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'currency_code' not in columns:
                cursor.execute("ALTER TABLE transactions ADD COLUMN currency_code TEXT DEFAULT 'USD'")

            if 'exchange_rate' not in columns:
                cursor.execute("ALTER TABLE transactions ADD COLUMN exchange_rate DECIMAL(15,6) DEFAULT 1.0")

            if 'base_amount' not in columns:
                cursor.execute("ALTER TABLE transactions ADD COLUMN base_amount DECIMAL(15,2)")

            # Update base_amount for existing transactions where it's NULL
            cursor.execute('''
                UPDATE transactions
                SET base_amount = amount
                WHERE base_amount IS NULL
            ''')

        except sqlite3.Error as e:
            print(f"Error adding currency columns to transactions: {e}")

    @staticmethod
    def _update_metadata(cursor, db_path):
        """Update metadata table with required entries"""
        # Check if metadata entries exist
        cursor.execute("SELECT key FROM metadata WHERE key = 'created_date'")
        if not cursor.fetchone():
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cursor.execute(
                "INSERT INTO metadata (key, value) VALUES (?, ?)",
                ("created_date", current_time)
            )

        # Add company name if missing
        cursor.execute("SELECT key FROM metadata WHERE key = 'company_name'")
        if not cursor.fetchone():
            # Extract company name from database filename
            company_name = os.path.basename(db_path).replace('.db', '').replace('_', ' ').title()
            cursor.execute(
                "INSERT INTO metadata (key, value) VALUES (?, ?)",
                ("company_name", company_name)
            )

    @staticmethod
    def migrate_all_company_databases():
        """Migrate all company databases in the current directory"""
        # Get all .db files except system databases
        excluded_files = ['users.db', 'settings.db']
        db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

        print(f"Found {len(db_files)} company databases to migrate")

        success_count = 0
        for db_file in db_files:
            print(f"\nMigrating {db_file}...")
            if DatabaseMigration.migrate_company_database(db_file):
                success_count += 1
            else:
                print(f"Failed to migrate {db_file}")

        print(f"\nMigration complete: {success_count}/{len(db_files)} databases migrated successfully")
        return success_count == len(db_files)

    @staticmethod
    def check_database_health(db_path):
        """Check the health of a database and return issues"""
        issues = []

        if not os.path.exists(db_path):
            issues.append(f"Database file {db_path} does not exist")
            return issues

        try:
            conn = sqlite3.connect(db_path, timeout=30)
            cursor = conn.cursor()

            # Check required tables
            required_tables = ['accounts', 'transactions', 'categories', 'metadata']
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]

            for table in required_tables:
                if table not in existing_tables:
                    issues.append(f"Missing required table: {table}")

            # Check accounts table structure
            if 'accounts' in existing_tables:
                cursor.execute("PRAGMA table_info(accounts)")
                columns = [column[1] for column in cursor.fetchall()]

                required_columns = ['id', 'name', 'type', 'currency', 'opening_balance', 'current_balance']
                for column in required_columns:
                    if column not in columns:
                        issues.append(f"Missing required column in accounts table: {column}")

            # Check metadata
            if 'metadata' in existing_tables:
                cursor.execute("SELECT key FROM metadata WHERE key IN ('created_date', 'company_name')")
                metadata_keys = [row[0] for row in cursor.fetchall()]

                if 'created_date' not in metadata_keys:
                    issues.append("Missing created_date in metadata")
                if 'company_name' not in metadata_keys:
                    issues.append("Missing company_name in metadata")

        except sqlite3.Error as e:
            issues.append(f"Database error: {e}")
        finally:
            if conn:
                conn.close()

        return issues


def main():
    """Main function for running database migration"""
    print("Database Migration Utility")
    print("=" * 40)

    # Check all databases
    print("Checking database health...")
    excluded_files = ['users.db', 'settings.db']
    db_files = [f for f in os.listdir() if f.endswith('.db') and f not in excluded_files and not f.startswith('test_')]

    databases_with_issues = []
    for db_file in db_files:
        issues = DatabaseMigration.check_database_health(db_file)
        if issues:
            databases_with_issues.append((db_file, issues))
            print(f"\n❌ {db_file} has issues:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print(f"✅ {db_file} is healthy")

    if databases_with_issues:
        print(f"\nFound {len(databases_with_issues)} databases with issues")
        response = input("Do you want to migrate all databases? (y/n): ")

        if response.lower() == 'y':
            print("\nStarting migration...")
            if DatabaseMigration.migrate_all_company_databases():
                print("✅ All databases migrated successfully!")
            else:
                print("❌ Some databases failed to migrate")
        else:
            print("Migration cancelled")
    else:
        print("\n✅ All databases are healthy!")


if __name__ == "__main__":
    main()
