# tests/test_model.py
import os
import sqlite3
import unittest

from model.database import Database
from model.transaction import TransactionManager


class TestDatabase(unittest.TestCase):
    def setUp(self):
        # Use a test database
        self.db = Database()
        self.db.users_db = "test_users.db"
        self.db.init_users_db()

    def tearDown(self):
        # Clean up after tests
        if os.path.exists("test_users.db"):
            os.remove("test_users.db")
        if os.path.exists("test_company.db"):
            os.remove("test_company.db")

    def test_init_users_db(self):
        # Check if users database was created with admin user
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        conn.close()

        self.assertIsNotNone(result)
        self.assertEqual(result[1], "Admin")

    def test_init_company_db(self):
        # Create test company
        db_path = self.db.init_company_db("Test Company")

        # Check if database was created with transactions table
        self.assertTrue(os.path.exists(db_path))

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
        result = cursor.fetchone()
        conn.close()

        self.assertIsNotNone(result)

    def test_authenticate_user(self):
        # Add a test user
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()

        import hashlib
        hashed_password = hashlib.sha256("testpass".encode()).hexdigest()
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser", hashed_password, "Client")
        )

        conn.commit()
        conn.close()

        # Test authentication
        result = self.db.authenticate_user("testuser", "testpass")
        self.assertEqual(result, "Client")

        # Test invalid authentication
        result = self.db.authenticate_user("testuser", "wrongpass")
        self.assertIsNone(result)

    def test_get_all_users(self):
        # Add test users
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()

        import hashlib
        hashed_password = hashlib.sha256("testpass".encode()).hexdigest()
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser1", hashed_password, "Client")
        )
        cursor.execute(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser2", hashed_password, "Admin")
        )

        conn.commit()
        conn.close()

        # Get all users
        users = self.db.get_all_users()

        # Should have at least 3 users (admin + 2 test users)
        self.assertGreaterEqual(len(users), 3)

        # Check if test users are in the list
        usernames = [user["username"] for user in users]
        self.assertIn("testuser1", usernames)
        self.assertIn("testuser2", usernames)

    def test_add_user(self):
        # Add a new user
        user_id = self.db.add_user("newuser", "newpass", "Client")

        # Check if user was added
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        cursor.execute("SELECT username, role FROM users WHERE id = ?", (user_id,))
        result = cursor.fetchone()
        conn.close()

        self.assertIsNotNone(result)
        self.assertEqual(result[0], "newuser")
        self.assertEqual(result[1], "Client")

        # Test adding user with invalid role
        with self.assertRaises(ValueError):
            self.db.add_user("invaliduser", "pass", "InvalidRole")

        # Test adding duplicate username
        with self.assertRaises(ValueError):
            self.db.add_user("newuser", "anotherpass", "Admin")

    def test_delete_user(self):
        # Add a test user
        user_id = self.db.add_user("userToDelete", "pass", "Client")

        # Delete the user
        result = self.db.delete_user(user_id)
        self.assertTrue(result)

        # Check if user was deleted
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users WHERE id = ?", (user_id,))
        count = cursor.fetchone()[0]
        conn.close()

        self.assertEqual(count, 0)

        # Test deleting non-existent user
        with self.assertRaises(ValueError):
            self.db.delete_user(9999)

        # Test deleting the last admin user
        # First, delete all non-admin users
        conn = sqlite3.connect("test_users.db")
        cursor = conn.cursor()
        cursor.execute("DELETE FROM users WHERE role != 'Admin'")
        conn.commit()

        # Then, get the admin user ID
        cursor.execute("SELECT id FROM users WHERE role = 'Admin'")
        admin_id = cursor.fetchone()[0]
        conn.close()

        # Try to delete the admin user
        with self.assertRaises(ValueError):
            self.db.delete_user(admin_id)


class TestTransactionManager(unittest.TestCase):
    def setUp(self):
        # Create a test database and manager
        self.db = Database()
        self.db_path = self.db.init_company_db("Test Company")
        self.manager = TransactionManager(self.db_path)

        # Create test categories and accounts
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create test categories
        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Food", "expense"))
        self.expense_category_id = cursor.lastrowid

        cursor.execute("INSERT INTO categories (name, type) VALUES (?, ?)", ("Salary", "income"))
        self.income_category_id = cursor.lastrowid

        # Create test account
        cursor.execute(
            "INSERT INTO accounts (name, type, currency, opening_balance, current_balance, created_date) VALUES (?, ?, ?, ?, ?, ?)",
            ("Test Account", "Checking", "USD", 1000.0, 1000.0, "2023-01-01")
        )
        self.account_id = cursor.lastrowid

        conn.commit()
        conn.close()

    def tearDown(self):
        # Clean up after tests
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def test_add_transaction(self):
        # Add a test transaction
        transaction_id = self.manager.add_transaction(
            date="2025-01-01",
            amount=100.00,
            account_id=self.account_id,
            description="Test Transaction",
            category_id=self.income_category_id,
            type_name="income"
        )

        # Check if transaction was added
        transactions = self.manager.get_transactions()
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0]["description"], "Test Transaction")
        self.assertEqual(transactions[0]["amount"], 100.00)
        self.assertEqual(transactions[0]["account_id"], self.account_id)
        self.assertEqual(transactions[0]["category_id"], self.income_category_id)

    def test_edit_transaction(self):
        # Add a test transaction
        transaction_id = self.manager.add_transaction(
            date="2025-01-01",
            amount=100.00,
            account_id=self.account_id,
            description="Test Transaction",
            category_id=self.income_category_id,
            type_name="income"
        )

        # Edit transaction
        self.manager.edit_transaction(
            transaction_id=transaction_id,
            date="2025-01-02",
            amount=200.00,
            description="Updated Transaction",
            category_id=self.income_category_id
        )

        # Check if transaction was updated
        updated = self.manager.get_transactions()[0]
        self.assertEqual(updated["description"], "Updated Transaction")
        self.assertEqual(updated["amount"], 200.00)
        self.assertEqual(updated["date"], "2025-01-02")

    def test_delete_transaction(self):
        # Add a test transaction
        transaction_id = self.manager.add_transaction(
            date="2025-01-01",
            amount=100.00,
            account_id=self.account_id,
            description="Test Transaction",
            category_id=self.income_category_id,
            type_name="income"
        )

        # Delete transaction
        self.manager.delete_transaction(transaction_id)

        # Check if transaction was deleted
        transactions = self.manager.get_transactions()
        self.assertEqual(len(transactions), 0)

    def test_get_balance_summary(self):
        # Add income and expense transactions
        self.manager.add_transaction(
            date="2025-01-01",
            amount=100.00,
            account_id=self.account_id,
            description="Test Income",
            category_id=self.income_category_id,
            type_name="income"
        )
        self.manager.add_transaction(
            date="2025-01-02",
            amount=40.00,
            account_id=self.account_id,
            description="Test Expense",
            category_id=self.expense_category_id,
            type_name="expense"
        )

        # Get summary
        summary = self.manager.get_balance_summary()

        # Check summary values
        self.assertEqual(summary["income"], 100.00)
        self.assertEqual(summary["expenses"], 40.00)
        self.assertEqual(summary["balance"], 60.00)

    def test_mark_as_reconciled(self):
        # Add a test transaction
        transaction_id = self.manager.add_transaction(
            date="2025-01-01",
            amount=100.00,
            account_id=self.account_id,
            description="Test Transaction",
            category_id=self.income_category_id,
            type_name="income",
            reconciled=0
        )

        # Mark as reconciled
        self.manager.mark_as_reconciled(transaction_id, 1)

        # Check if transaction was marked as reconciled
        transactions = self.manager.get_transactions()
        self.assertEqual(transactions[0]["reconciled"], 1)

        # Mark as unreconciled
        self.manager.mark_as_reconciled(transaction_id, 0)

        # Check if transaction was marked as unreconciled
        transactions = self.manager.get_transactions()
        self.assertEqual(transactions[0]["reconciled"], 0)