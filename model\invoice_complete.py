from datetime import datetime, timedelta

from model.base_model import BaseModel


class Invoice(BaseModel):
    """Invoice model for managing invoices"""

    # Invoice status constants
    STATUS_DRAFT = "draft"
    STATUS_SENT = "sent"
    STATUS_PAID = "paid"
    STATUS_OVERDUE = "overdue"
    STATUS_CANCELLED = "cancelled"

    def __init__(self, db_path):
        """Initialize the Invoice model with database path"""
        super().__init__(db_path)
        self._table_name = "invoices"
        self._create_tables()

    def table_name(self):
        """Return the table name for this model"""
        return self._table_name

    def fields(self):
        """Return a list of field names for this model"""
        return [
            'id', 'invoice_number', 'client_id', 'issue_date', 'due_date',
            'status', 'subtotal', 'tax_amount', 'total_amount', 'notes',
            'terms', 'created_at', 'updated_at'
        ]

    def primary_key(self):
        """Return the primary key field name"""
        return 'id'

    def _create_tables(self):
        """Create the invoices and invoice_items tables if they don't exist"""
        # Create invoices table
        query = '''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT NOT NULL UNIQUE,
            client_id INTEGER NOT NULL,
            issue_date TEXT NOT NULL,
            due_date TEXT NOT NULL,
            status TEXT NOT NULL,
            subtotal REAL NOT NULL,
            tax_amount REAL NOT NULL,
            total_amount REAL NOT NULL,
            notes TEXT,
            terms TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
        '''
        self.execute_query(query)

        # Create invoice_items table
        query = '''
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            description TEXT NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            tax_rate REAL NOT NULL,
            amount REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
        )
        '''
        self.execute_query(query)

        # Create invoice_settings table for storing next invoice number
        query = '''
        CREATE TABLE IF NOT EXISTS invoice_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_name TEXT NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.execute_query(query)

        # Initialize next_invoice_number if it doesn't exist
        query = '''
        INSERT OR IGNORE INTO invoice_settings (setting_name, setting_value)
        VALUES ('next_invoice_number', '1001')
        '''
        self.execute_query(query)

    def generate_invoice_number(self, prefix="INV-"):
        """Generate a unique invoice number

        Args:
            prefix (str): Prefix for the invoice number (default: "INV-")

        Returns:
            str: Generated invoice number
        """
        # Get the next invoice number from settings
        query = '''
        SELECT setting_value FROM invoice_settings WHERE setting_name = 'next_invoice_number'
        '''
        result = self.execute_query(query)

        if not result or len(result) == 0:
            # If no result, use default starting number
            next_number = 1001
        else:
            next_number = int(result[0]['setting_value'])

        # Generate the invoice number
        invoice_number = f"{prefix}{next_number}"

        # Update the next invoice number
        query = '''
        UPDATE invoice_settings
        SET setting_value = ?, updated_at = ?
        WHERE setting_name = 'next_invoice_number'
        '''
        self.execute_query(query, [str(next_number + 1), datetime.now()])

        return invoice_number

    def add_invoice(self, invoice_data, items_data):
        """Add a new invoice with items

        Args:
            invoice_data (dict): Dictionary containing invoice information
                - invoice_number: Invoice number (optional, generated if not provided)
                - client_id: Client ID (required)
                - issue_date: Issue date (required)
                - due_date: Due date (required)
                - status: Invoice status (default: "draft")
                - notes: Additional notes
                - terms: Invoice terms and conditions
            items_data (list): List of dictionaries containing invoice items
                - description: Item description (required)
                - quantity: Item quantity (required)
                - unit_price: Unit price (required)
                - tax_rate: Tax rate percentage (required)

        Returns:
            int: ID of the newly created invoice
        """
        # Validate required fields
        if not invoice_data.get('client_id'):
            raise ValueError("Client ID is required")

        if not invoice_data.get('issue_date'):
            raise ValueError("Issue date is required")

        # Set default values
        if not invoice_data.get('invoice_number'):
            invoice_data['invoice_number'] = self.generate_invoice_number()

        if not invoice_data.get('status'):
            invoice_data['status'] = self.STATUS_DRAFT

        if not invoice_data.get('due_date'):
            # Default due date is 30 days from issue date
            issue_date = datetime.strptime(invoice_data['issue_date'], "%Y-%m-%d")
            due_date = issue_date + timedelta(days=30)
            invoice_data['due_date'] = due_date.strftime("%Y-%m-%d")

        # Calculate totals
        subtotal = 0
        tax_amount = 0

        for item in items_data:
            if not item.get('description'):
                raise ValueError("Item description is required")

            quantity = float(item.get('quantity', 0))
            unit_price = float(item.get('unit_price', 0))
            tax_rate = float(item.get('tax_rate', 0))

            item_amount = quantity * unit_price
            item_tax = item_amount * (tax_rate / 100)

            item['amount'] = item_amount
            subtotal += item_amount
            tax_amount += item_tax

        total_amount = subtotal + tax_amount

        # Add calculated values to invoice data
        invoice_data['subtotal'] = subtotal
        invoice_data['tax_amount'] = tax_amount
        invoice_data['total_amount'] = total_amount

        # Start a transaction
        conn = self._get_connection()
        conn.execute("BEGIN TRANSACTION")

        try:
            # Insert invoice
            columns = []
            values = []
            placeholders = []

            for key, value in invoice_data.items():
                if key in ['invoice_number', 'client_id', 'issue_date', 'due_date',
                          'status', 'subtotal', 'tax_amount', 'total_amount', 'notes', 'terms']:
                    columns.append(key)
                    values.append(value)
                    placeholders.append('?')

            # Add timestamps
            columns.extend(['created_at', 'updated_at'])
            values.extend([datetime.now(), datetime.now()])
            placeholders.extend(['?', '?'])

            # Build the query
            query = f'''
            INSERT INTO {self.table_name()}
            ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            '''

            # Execute the query
            cursor = conn.execute(query, values)
            invoice_id = cursor.lastrowid

            # Insert invoice items
            for item in items_data:
                item_columns = ['invoice_id', 'description', 'quantity', 'unit_price',
                               'tax_rate', 'amount', 'created_at', 'updated_at']
                item_values = [
                    invoice_id,
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['tax_rate'],
                    item['amount'],
                    datetime.now(),
                    datetime.now()
                ]
                item_placeholders = ['?'] * len(item_columns)

                # Build the query
                query = f'''
                INSERT INTO invoice_items
                ({', '.join(item_columns)})
                VALUES ({', '.join(item_placeholders)})
                '''

                # Execute the query
                conn.execute(query, item_values)

            # Commit the transaction
            conn.commit()
            return invoice_id

        except Exception as e:
            # Rollback the transaction in case of error
            conn.rollback()
            raise e
        finally:
            conn.close()

    def update_invoice(self, invoice_id, invoice_data, items_data=None):
        """Update an existing invoice

        Args:
            invoice_id (int): ID of the invoice to update
            invoice_data (dict): Dictionary containing invoice information to update
            items_data (list, optional): List of dictionaries containing invoice items

        Returns:
            bool: True if the update was successful, False otherwise
        """
        # Start a transaction
        conn = self._get_connection()
        conn.execute("BEGIN TRANSACTION")

        try:
            # If items_data is provided, recalculate totals
            if items_data is not None:
                subtotal = 0
                tax_amount = 0

                # Delete existing items
                query = '''
                DELETE FROM invoice_items WHERE invoice_id = ?
                '''
                conn.execute(query, [invoice_id])

                # Insert new items
                for item in items_data:
                    if not item.get('description'):
                        raise ValueError("Item description is required")

                    quantity = float(item.get('quantity', 0))
                    unit_price = float(item.get('unit_price', 0))
                    tax_rate = float(item.get('tax_rate', 0))

                    item_amount = quantity * unit_price
                    item_tax = item_amount * (tax_rate / 100)

                    item['amount'] = item_amount
                    subtotal += item_amount
                    tax_amount += item_tax

                    item_columns = ['invoice_id', 'description', 'quantity', 'unit_price',
                                   'tax_rate', 'amount', 'created_at', 'updated_at']
                    item_values = [
                        invoice_id,
                        item['description'],
                        item['quantity'],
                        item['unit_price'],
                        item['tax_rate'],
                        item['amount'],
                        datetime.now(),
                        datetime.now()
                    ]
                    item_placeholders = ['?'] * len(item_columns)

                    # Build the query
                    query = f'''
                    INSERT INTO invoice_items
                    ({', '.join(item_columns)})
                    VALUES ({', '.join(item_placeholders)})
                    '''

                    # Execute the query
                    conn.execute(query, item_values)

                total_amount = subtotal + tax_amount

                # Add calculated values to invoice data
                invoice_data['subtotal'] = subtotal
                invoice_data['tax_amount'] = tax_amount
                invoice_data['total_amount'] = total_amount

            # Update invoice
            set_clause = []
            values = []

            for key, value in invoice_data.items():
                if key in ['invoice_number', 'client_id', 'issue_date', 'due_date',
                          'status', 'subtotal', 'tax_amount', 'total_amount', 'notes', 'terms']:
                    set_clause.append(f"{key} = ?")
                    values.append(value)

            # Add updated_at timestamp
            set_clause.append("updated_at = ?")
            values.append(datetime.now())

            # Add invoice_id to values
            values.append(invoice_id)

            # Build the query
            query = f'''
            UPDATE {self.table_name()}
            SET {', '.join(set_clause)}
            WHERE id = ?
            '''

            # Execute the query
            cursor = conn.execute(query, values)
            success = cursor.rowcount > 0

            # Commit the transaction
            conn.commit()
            return success

        except Exception as e:
            # Rollback the transaction in case of error
            conn.rollback()
            raise e
        finally:
            conn.close()

    def delete_invoice(self, invoice_id):
        """Delete an invoice and its items

        Args:
            invoice_id (int): ID of the invoice to delete

        Returns:
            bool: True if the deletion was successful, False otherwise
        """
        # Start a transaction
        conn = self._get_connection()
        conn.execute("BEGIN TRANSACTION")

        try:
            # Delete invoice items
            query = '''
            DELETE FROM invoice_items WHERE invoice_id = ?
            '''
            conn.execute(query, [invoice_id])

            # Delete invoice
            query = f'''
            DELETE FROM {self.table_name()} WHERE id = ?
            '''
            cursor = conn.execute(query, [invoice_id])
            success = cursor.rowcount > 0

            # Commit the transaction
            conn.commit()
            return success

        except Exception as e:
            # Rollback the transaction in case of error
            conn.rollback()
            raise e
        finally:
            conn.close()
