import os
import smtplib
import sqlite3
from datetime import datetime
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText


class EmailService:
    """Service for sending emails and tracking email history"""

    def __init__(self, db_path):
        """Initialize the email service with database path"""
        self.db_path = db_path
        self._create_tables()

    def _get_connection(self):
        """Get a database connection"""
        return sqlite3.connect(self.db_path)

    def _create_tables(self):
        """Create the necessary tables for email tracking"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create email_settings table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            smtp_server TEXT NOT NULL,
            smtp_port INTEGER NOT NULL,
            username TEXT NOT NULL,
            password TEXT NOT NULL,
            from_email TEXT NOT NULL,
            from_name TEXT,
            signature TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create email_templates table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            subject TEXT NOT NULL,
            body TEXT NOT NULL,
            is_default INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create email_history table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER,
            recipient TEXT NOT NULL,
            subject TEXT NOT NULL,
            body TEXT NOT NULL,
            status TEXT NOT NULL,
            error_message TEXT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id)
        )
        ''')

        # Create default email templates if they don't exist
        cursor.execute("SELECT COUNT(*) FROM email_templates")
        if cursor.fetchone()[0] == 0:
            self._create_default_templates(cursor)

        conn.commit()
        conn.close()

    def _create_default_templates(self, cursor):
        """Create default email templates"""
        # Invoice sent template
        cursor.execute('''
        INSERT INTO email_templates (name, subject, body, is_default)
        VALUES (?, ?, ?, ?)
        ''', (
            "Invoice Sent",
            "Invoice #{invoice_number} from {company_name}",
            """Dear {client_name},

Please find attached invoice #{invoice_number} for {total_amount}.

Due date: {due_date}

If you have any questions about this invoice, please don't hesitate to contact us.

Thank you for your business.

Best regards,
{company_name}
{signature}
""",
            1
        ))

        # Payment reminder template
        cursor.execute('''
        INSERT INTO email_templates (name, subject, body, is_default)
        VALUES (?, ?, ?, ?)
        ''', (
            "Payment Reminder",
            "Reminder: Invoice #{invoice_number} from {company_name}",
            """Dear {client_name},

This is a friendly reminder that invoice #{invoice_number} for {total_amount} is due on {due_date}.

If you have already made the payment, please disregard this reminder.

Thank you for your business.

Best regards,
{company_name}
{signature}
""",
            0
        ))

        # Payment received template
        cursor.execute('''
        INSERT INTO email_templates (name, subject, body, is_default)
        VALUES (?, ?, ?, ?)
        ''', (
            "Payment Received",
            "Payment Received for Invoice #{invoice_number}",
            """Dear {client_name},

Thank you for your payment of {total_amount} for invoice #{invoice_number}.

We appreciate your business and look forward to working with you again.

Best regards,
{company_name}
{signature}
""",
            0
        ))

    def get_email_settings(self):
        """Get email settings from the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM email_settings LIMIT 1")
        row = cursor.fetchone()

        conn.close()

        if not row:
            return None

        # Convert to dictionary
        columns = ["id", "smtp_server", "smtp_port", "username", "password",
                  "from_email", "from_name", "signature", "created_at", "updated_at"]
        return dict(zip(columns, row))

    def save_email_settings(self, settings):
        """Save email settings to the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check if settings already exist
        cursor.execute("SELECT COUNT(*) FROM email_settings")
        count = cursor.fetchone()[0]

        if count > 0:
            # Update existing settings
            cursor.execute('''
            UPDATE email_settings
            SET smtp_server = ?, smtp_port = ?, username = ?, password = ?,
                from_email = ?, from_name = ?, signature = ?, updated_at = ?
            ''', (
                settings.get('smtp_server'),
                settings.get('smtp_port'),
                settings.get('username'),
                settings.get('password'),
                settings.get('from_email'),
                settings.get('from_name'),
                settings.get('signature'),
                datetime.now()
            ))
        else:
            # Insert new settings
            cursor.execute('''
            INSERT INTO email_settings
            (smtp_server, smtp_port, username, password, from_email, from_name, signature)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                settings.get('smtp_server'),
                settings.get('smtp_port'),
                settings.get('username'),
                settings.get('password'),
                settings.get('from_email'),
                settings.get('from_name'),
                settings.get('signature')
            ))

        conn.commit()
        conn.close()
        return True

    def get_email_templates(self):
        """Get all email templates from the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM email_templates ORDER BY name ASC")
        rows = cursor.fetchall()

        conn.close()

        # Convert to list of dictionaries
        templates = []
        columns = ["id", "name", "subject", "body", "is_default", "created_at", "updated_at"]
        for row in rows:
            templates.append(dict(zip(columns, row)))

        return templates

    def get_template_by_name(self, name):
        """Get an email template by name"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM email_templates WHERE name = ?", (name,))
        row = cursor.fetchone()

        conn.close()

        if not row:
            return None

        # Convert to dictionary
        columns = ["id", "name", "subject", "body", "is_default", "created_at", "updated_at"]
        return dict(zip(columns, row))

    def send_invoice_email(self, invoice_id, template_name, pdf_path=None, additional_context=None):
        """Send an invoice email using a template"""
        # Get email settings
        settings = self.get_email_settings()
        if not settings:
            self._log_email(invoice_id, "", "", "", "Failed", "Email settings not configured")
            return False, "Email settings not configured"

        # Get invoice details
        from model.invoice import Invoice
        invoice_model = Invoice(self.db_path)
        invoice = invoice_model.get_invoice(invoice_id)
        if not invoice:
            self._log_email(invoice_id, "", "", "", "Failed", f"Invoice {invoice_id} not found")
            return False, f"Invoice {invoice_id} not found"

        # Get client details
        from model.client import Client
        client_model = Client(self.db_path)
        client = client_model.get_client(invoice['client_id'])
        if not client:
            self._log_email(invoice_id, "", "", "", "Failed", f"Client for invoice {invoice_id} not found")
            return False, f"Client for invoice {invoice_id} not found"

        # Get email template
        template = self.get_template_by_name(template_name)
        if not template:
            self._log_email(invoice_id, "", "", "", "Failed", f"Email template '{template_name}' not found")
            return False, f"Email template '{template_name}' not found"

        # Prepare context for template rendering
        context = {
            'invoice_number': invoice['invoice_number'],
            'company_name': settings.get('from_name', ''),
            'client_name': client['name'],
            'total_amount': f"${invoice['total_amount']:.2f}",
            'due_date': invoice['due_date'],
            'signature': settings.get('signature', '')
        }

        # Add additional context if provided
        if additional_context:
            context.update(additional_context)

        # Render template
        subject = self._render_template(template['subject'], context)
        body = self._render_template(template['body'], context)

        # Create email message
        msg = MIMEMultipart()
        msg['From'] = f"{settings['from_name']} <{settings['from_email']}>"
        msg['To'] = client['email']
        msg['Subject'] = subject

        # Add body
        msg.attach(MIMEText(body, 'plain'))

        # Add PDF attachment if provided
        if pdf_path and os.path.exists(pdf_path):
            with open(pdf_path, 'rb') as file:
                attachment = MIMEApplication(file.read(), _subtype="pdf")
                attachment.add_header('Content-Disposition', 'attachment',
                                     filename=f"Invoice_{invoice['invoice_number']}.pdf")
                msg.attach(attachment)

        # Send email
        try:
            server = smtplib.SMTP(settings['smtp_server'], settings['smtp_port'])
            server.starttls()
            server.login(settings['username'], settings['password'])
            server.send_message(msg)
            server.quit()

            # Log successful email
            self._log_email(invoice_id, client['email'], subject, body, "Sent")
            return True, "Email sent successfully"
        except Exception as e:
            # Log failed email
            error_message = str(e)
            self._log_email(invoice_id, client['email'], subject, body, "Failed", error_message)
            return False, f"Failed to send email: {error_message}"

    def _render_template(self, template, context):
        """Render a template with the given context"""
        result = template
        for key, value in context.items():
            result = result.replace(f"{{{key}}}", str(value))
        return result

    def _log_email(self, invoice_id, recipient, subject, body, status, error_message=None):
        """Log an email to the history table"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        INSERT INTO email_history
        (invoice_id, recipient, subject, body, status, error_message)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (invoice_id, recipient, subject, body, status, error_message))

        conn.commit()
        conn.close()

    def get_email_history(self, invoice_id=None):
        """Get email history from the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if invoice_id:
            cursor.execute('''
            SELECT * FROM email_history
            WHERE invoice_id = ?
            ORDER BY sent_at DESC
            ''', (invoice_id,))
        else:
            cursor.execute('''
            SELECT * FROM email_history
            ORDER BY sent_at DESC
            ''')

        rows = cursor.fetchall()
        conn.close()

        # Convert to list of dictionaries
        history = []
        columns = ["id", "invoice_id", "recipient", "subject", "body",
                  "status", "error_message", "sent_at"]
        for row in rows:
            history.append(dict(zip(columns, row)))

        return history
