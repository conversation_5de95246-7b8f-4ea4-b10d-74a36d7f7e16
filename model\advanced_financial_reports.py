import sqlite3
from datetime import datetime, timedelta

from model.base_model import BaseModel


class AdvancedFinancialReports(BaseModel):
    """Model for generating advanced financial reports with proper accounting classifications"""

    def __init__(self, db_path):
        super().__init__(db_path)

    def table_name(self):
        return "financial_reports_temp"  # Virtual table for reporting

    def fields(self):
        return ['report_type', 'as_of_date', 'data']

    def primary_key(self):
        return 'report_type'

    def generate_balance_sheet(self, as_of_date=None):
        """Generate a comprehensive balance sheet with proper accounting classifications"""
        if as_of_date is None:
            as_of_date = datetime.now().strftime("%Y-%m-%d")

        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get all accounts with their balances and classifications
            cursor.execute('''
                SELECT
                    a.id,
                    a.name,
                    a.type,
                    a.classification,
                    a.account_number,
                    a.current_balance,
                    a.opening_balance
                FROM accounts a
                WHERE a.is_active = 1 AND a.classification IN ('Asset', 'Liability', 'Equity')
                ORDER BY a.account_number, a.name
            ''')

            accounts = cursor.fetchall()

            # Organize accounts by classification and subcategory
            balance_sheet = {
                'as_of_date': as_of_date,
                'assets': {
                    'current_assets': [],
                    'fixed_assets': [],
                    'total_assets': 0.0
                },
                'liabilities': {
                    'current_liabilities': [],
                    'long_term_liabilities': [],
                    'total_liabilities': 0.0
                },
                'equity': {
                    'equity_accounts': [],
                    'total_equity': 0.0
                },
                'total_liabilities_and_equity': 0.0,
                'is_balanced': False
            }

            for account in accounts:
                account_id, name, account_type, classification, account_number, current_balance, opening_balance = account

                account_data = {
                    'id': account_id,
                    'name': name,
                    'type': account_type,
                    'account_number': account_number or '',
                    'balance': current_balance or 0.0
                }

                if classification == 'Asset':
                    if account_type in ['Cash', 'Checking Account', 'Savings Account', 'Accounts Receivable', 'Inventory', 'Prepaid Expenses']:
                        balance_sheet['assets']['current_assets'].append(account_data)
                    else:
                        balance_sheet['assets']['fixed_assets'].append(account_data)
                    balance_sheet['assets']['total_assets'] += account_data['balance']

                elif classification == 'Liability':
                    if account_type in ['Accounts Payable', 'Accrued Expenses', 'Short-term Loans', 'Credit Cards', 'Sales Tax Payable']:
                        balance_sheet['liabilities']['current_liabilities'].append(account_data)
                    else:
                        balance_sheet['liabilities']['long_term_liabilities'].append(account_data)
                    balance_sheet['liabilities']['total_liabilities'] += account_data['balance']

                elif classification == 'Equity':
                    balance_sheet['equity']['equity_accounts'].append(account_data)
                    balance_sheet['equity']['total_equity'] += account_data['balance']

            # Calculate total liabilities and equity
            balance_sheet['total_liabilities_and_equity'] = (
                balance_sheet['liabilities']['total_liabilities'] +
                balance_sheet['equity']['total_equity']
            )

            # Check if balanced (Assets = Liabilities + Equity)
            difference = abs(balance_sheet['assets']['total_assets'] - balance_sheet['total_liabilities_and_equity'])
            balance_sheet['is_balanced'] = difference < 0.01
            balance_sheet['difference'] = balance_sheet['assets']['total_assets'] - balance_sheet['total_liabilities_and_equity']

            return balance_sheet

        except sqlite3.Error as e:
            print(f"Error generating balance sheet: {e}")
            raise
        finally:
            conn.close()

    def generate_income_statement(self, start_date=None, end_date=None):
        """Generate a comprehensive income statement with proper revenue and expense classifications"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get revenue and expense accounts with their activity
            cursor.execute('''
                SELECT
                    a.id,
                    a.name,
                    a.type,
                    a.classification,
                    a.account_number,
                    COALESCE(SUM(
                        CASE
                            WHEN jl.credit_amount > 0 AND a.classification = 'Revenue' THEN jl.credit_amount
                            WHEN jl.debit_amount > 0 AND a.classification = 'Expense' THEN jl.debit_amount
                            ELSE 0
                        END
                    ), 0) as period_activity
                FROM accounts a
                LEFT JOIN journal_lines jl ON a.id = jl.account_id
                LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id
                WHERE a.is_active = 1
                AND a.classification IN ('Revenue', 'Expense')
                AND (je.date IS NULL OR (je.date >= ? AND je.date <= ?))
                GROUP BY a.id, a.name, a.type, a.classification, a.account_number
                ORDER BY a.classification DESC, a.account_number, a.name
            ''', (start_date, end_date))

            accounts = cursor.fetchall()

            income_statement = {
                'period_start': start_date,
                'period_end': end_date,
                'revenue': {
                    'operating_revenue': [],
                    'other_revenue': [],
                    'total_revenue': 0.0
                },
                'expenses': {
                    'cost_of_goods_sold': [],
                    'operating_expenses': [],
                    'administrative_expenses': [],
                    'other_expenses': [],
                    'total_expenses': 0.0
                },
                'gross_profit': 0.0,
                'operating_income': 0.0,
                'net_income': 0.0
            }

            for account in accounts:
                account_id, name, account_type, classification, account_number, period_activity = account

                account_data = {
                    'id': account_id,
                    'name': name,
                    'type': account_type,
                    'account_number': account_number or '',
                    'amount': period_activity or 0.0
                }

                if classification == 'Revenue':
                    if account_type in ['Sales Revenue', 'Service Revenue']:
                        income_statement['revenue']['operating_revenue'].append(account_data)
                    else:
                        income_statement['revenue']['other_revenue'].append(account_data)
                    income_statement['revenue']['total_revenue'] += account_data['amount']

                elif classification == 'Expense':
                    if account_type == 'Cost of Goods Sold':
                        income_statement['expenses']['cost_of_goods_sold'].append(account_data)
                    elif account_type in ['Salaries Expense', 'Rent Expense', 'Utilities Expense', 'Office Supplies', 'Insurance Expense']:
                        income_statement['expenses']['operating_expenses'].append(account_data)
                    elif account_type in ['Professional Fees', 'Bank Charges', 'Depreciation Expense']:
                        income_statement['expenses']['administrative_expenses'].append(account_data)
                    else:
                        income_statement['expenses']['other_expenses'].append(account_data)
                    income_statement['expenses']['total_expenses'] += account_data['amount']

            # Calculate derived figures
            cogs_total = sum(item['amount'] for item in income_statement['expenses']['cost_of_goods_sold'])
            income_statement['gross_profit'] = income_statement['revenue']['total_revenue'] - cogs_total

            operating_expenses_total = sum(item['amount'] for item in income_statement['expenses']['operating_expenses'])
            income_statement['operating_income'] = income_statement['gross_profit'] - operating_expenses_total

            income_statement['net_income'] = income_statement['revenue']['total_revenue'] - income_statement['expenses']['total_expenses']

            return income_statement

        except sqlite3.Error as e:
            print(f"Error generating income statement: {e}")
            raise
        finally:
            conn.close()

    def generate_cash_flow_statement(self, start_date=None, end_date=None):
        """Generate a statement of cash flows with operating, investing, and financing activities"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Get cash account activities
            cursor.execute('''
                SELECT
                    je.date,
                    je.description,
                    jl.debit_amount,
                    jl.credit_amount,
                    a.name as account_name,
                    a.type as account_type,
                    a.classification
                FROM journal_lines jl
                JOIN journal_entries je ON jl.journal_entry_id = je.id
                JOIN accounts a ON jl.account_id = a.id
                WHERE a.type IN ('Cash', 'Checking Account')
                AND je.date >= ? AND je.date <= ?
                ORDER BY je.date
            ''', (start_date, end_date))

            cash_transactions = cursor.fetchall()

            cash_flow = {
                'period_start': start_date,
                'period_end': end_date,
                'operating_activities': {
                    'items': [],
                    'total': 0.0
                },
                'investing_activities': {
                    'items': [],
                    'total': 0.0
                },
                'financing_activities': {
                    'items': [],
                    'total': 0.0
                },
                'net_cash_flow': 0.0,
                'beginning_cash': 0.0,
                'ending_cash': 0.0
            }

            # Get beginning cash balance
            cursor.execute('''
                SELECT SUM(a.opening_balance)
                FROM accounts a
                WHERE a.type IN ('Cash', 'Checking Account') AND a.is_active = 1
            ''')
            result = cursor.fetchone()
            cash_flow['beginning_cash'] = result[0] if result[0] else 0.0

            # Categorize cash flows (simplified categorization)
            for transaction in cash_transactions:
                date, description, debit_amount, credit_amount, account_name, account_type, classification = transaction

                net_amount = (debit_amount or 0.0) - (credit_amount or 0.0)

                cash_item = {
                    'date': date,
                    'description': description,
                    'amount': net_amount,
                    'account': account_name
                }

                # Simple categorization based on description keywords
                description_lower = description.lower()
                if any(keyword in description_lower for keyword in ['sale', 'revenue', 'income', 'expense', 'operating']):
                    cash_flow['operating_activities']['items'].append(cash_item)
                    cash_flow['operating_activities']['total'] += net_amount
                elif any(keyword in description_lower for keyword in ['equipment', 'asset', 'investment', 'purchase']):
                    cash_flow['investing_activities']['items'].append(cash_item)
                    cash_flow['investing_activities']['total'] += net_amount
                elif any(keyword in description_lower for keyword in ['loan', 'capital', 'dividend', 'financing']):
                    cash_flow['financing_activities']['items'].append(cash_item)
                    cash_flow['financing_activities']['total'] += net_amount
                else:
                    # Default to operating activities
                    cash_flow['operating_activities']['items'].append(cash_item)
                    cash_flow['operating_activities']['total'] += net_amount

            # Calculate totals
            cash_flow['net_cash_flow'] = (
                cash_flow['operating_activities']['total'] +
                cash_flow['investing_activities']['total'] +
                cash_flow['financing_activities']['total']
            )
            cash_flow['ending_cash'] = cash_flow['beginning_cash'] + cash_flow['net_cash_flow']

            return cash_flow

        except sqlite3.Error as e:
            print(f"Error generating cash flow statement: {e}")
            raise
        finally:
            conn.close()

    def generate_comparative_balance_sheet(self, current_date=None, prior_date=None):
        """Generate a comparative balance sheet showing current vs prior period"""
        if current_date is None:
            current_date = datetime.now().strftime("%Y-%m-%d")
        if prior_date is None:
            prior_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        current_bs = self.generate_balance_sheet(current_date)
        prior_bs = self.generate_balance_sheet(prior_date)

        # Create comparative structure
        comparative_bs = {
            'current_date': current_date,
            'prior_date': prior_date,
            'current': current_bs,
            'prior': prior_bs,
            'changes': {
                'assets': {
                    'current_assets': [],
                    'fixed_assets': [],
                    'total_change': 0.0
                },
                'liabilities': {
                    'current_liabilities': [],
                    'long_term_liabilities': [],
                    'total_change': 0.0
                },
                'equity': {
                    'equity_accounts': [],
                    'total_change': 0.0
                }
            }
        }

        # Calculate changes (simplified - would need more sophisticated matching in production)
        comparative_bs['changes']['assets']['total_change'] = (
            current_bs['assets']['total_assets'] - prior_bs['assets']['total_assets']
        )
        comparative_bs['changes']['liabilities']['total_change'] = (
            current_bs['liabilities']['total_liabilities'] - prior_bs['liabilities']['total_liabilities']
        )
        comparative_bs['changes']['equity']['total_change'] = (
            current_bs['equity']['total_equity'] - prior_bs['equity']['total_equity']
        )

        return comparative_bs

    def calculate_financial_ratios(self, as_of_date=None):
        """Calculate key financial ratios"""
        balance_sheet = self.generate_balance_sheet(as_of_date)

        # Get income statement for the year
        year_start = f"{datetime.now().year}-01-01"
        income_statement = self.generate_income_statement(year_start, as_of_date)

        ratios = {
            'liquidity_ratios': {},
            'profitability_ratios': {},
            'efficiency_ratios': {},
            'leverage_ratios': {}
        }

        # Liquidity Ratios
        current_assets = sum(item['balance'] for item in balance_sheet['assets']['current_assets'])
        current_liabilities = sum(item['balance'] for item in balance_sheet['liabilities']['current_liabilities'])

        if current_liabilities > 0:
            ratios['liquidity_ratios']['current_ratio'] = current_assets / current_liabilities
        else:
            ratios['liquidity_ratios']['current_ratio'] = float('inf')

        # Profitability Ratios
        total_revenue = income_statement['revenue']['total_revenue']
        if total_revenue > 0:
            ratios['profitability_ratios']['gross_profit_margin'] = (income_statement['gross_profit'] / total_revenue) * 100
            ratios['profitability_ratios']['net_profit_margin'] = (income_statement['net_income'] / total_revenue) * 100

        total_assets = balance_sheet['assets']['total_assets']
        if total_assets > 0:
            ratios['profitability_ratios']['return_on_assets'] = (income_statement['net_income'] / total_assets) * 100

        total_equity = balance_sheet['equity']['total_equity']
        if total_equity > 0:
            ratios['profitability_ratios']['return_on_equity'] = (income_statement['net_income'] / total_equity) * 100

        # Leverage Ratios
        total_liabilities = balance_sheet['liabilities']['total_liabilities']
        if total_assets > 0:
            ratios['leverage_ratios']['debt_to_assets'] = (total_liabilities / total_assets) * 100

        if total_equity > 0:
            ratios['leverage_ratios']['debt_to_equity'] = (total_liabilities / total_equity) * 100

        return ratios

    def create_budget_vs_actual_report(self, budget_data, start_date=None, end_date=None):
        """
        Create a budget vs actual report

        Args:
            budget_data (dict): Budget data by account or category
            start_date (str): Start date for actual data
            end_date (str): End date for actual data
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        # Get actual data from income statement
        income_statement = self.generate_income_statement(start_date, end_date)

        budget_vs_actual = {
            'period_start': start_date,
            'period_end': end_date,
            'revenue': {
                'budget': budget_data.get('revenue', {}),
                'actual': income_statement['revenue'],
                'variance': {},
                'variance_percentage': {}
            },
            'expenses': {
                'budget': budget_data.get('expenses', {}),
                'actual': income_statement['expenses'],
                'variance': {},
                'variance_percentage': {}
            },
            'summary': {
                'total_revenue_budget': 0.0,
                'total_revenue_actual': 0.0,
                'total_expense_budget': 0.0,
                'total_expense_actual': 0.0,
                'net_income_budget': 0.0,
                'net_income_actual': 0.0,
                'net_income_variance': 0.0
            }
        }

        # Calculate revenue variances
        revenue_budget = budget_data.get('revenue', {})
        for category in ['operating_revenue', 'other_revenue']:
            budget_amount = revenue_budget.get(category, 0.0)
            actual_amount = sum(item['amount'] for item in income_statement['revenue'][category])
            variance = actual_amount - budget_amount
            variance_pct = (variance / budget_amount * 100) if budget_amount > 0 else 0

            budget_vs_actual['revenue']['variance'][category] = variance
            budget_vs_actual['revenue']['variance_percentage'][category] = variance_pct

        # Calculate expense variances
        expense_budget = budget_data.get('expenses', {})
        for category in ['cost_of_goods_sold', 'operating_expenses', 'administrative_expenses', 'other_expenses']:
            budget_amount = expense_budget.get(category, 0.0)
            actual_amount = sum(item['amount'] for item in income_statement['expenses'][category])
            variance = actual_amount - budget_amount  # Positive variance = over budget
            variance_pct = (variance / budget_amount * 100) if budget_amount > 0 else 0

            budget_vs_actual['expenses']['variance'][category] = variance
            budget_vs_actual['expenses']['variance_percentage'][category] = variance_pct

        # Calculate summary totals
        budget_vs_actual['summary']['total_revenue_budget'] = sum(revenue_budget.values())
        budget_vs_actual['summary']['total_revenue_actual'] = income_statement['revenue']['total_revenue']
        budget_vs_actual['summary']['total_expense_budget'] = sum(expense_budget.values())
        budget_vs_actual['summary']['total_expense_actual'] = income_statement['expenses']['total_expenses']

        budget_vs_actual['summary']['net_income_budget'] = (
            budget_vs_actual['summary']['total_revenue_budget'] -
            budget_vs_actual['summary']['total_expense_budget']
        )
        budget_vs_actual['summary']['net_income_actual'] = income_statement['net_income']
        budget_vs_actual['summary']['net_income_variance'] = (
            budget_vs_actual['summary']['net_income_actual'] -
            budget_vs_actual['summary']['net_income_budget']
        )

        return budget_vs_actual

    def generate_trend_analysis(self, periods=12, period_type='month'):
        """
        Generate trend analysis for key financial metrics

        Args:
            periods (int): Number of periods to analyze
            period_type (str): 'month', 'quarter', or 'year'
        """
        trend_data = {
            'period_type': period_type,
            'periods': [],
            'revenue_trend': [],
            'expense_trend': [],
            'net_income_trend': [],
            'cash_trend': []
        }

        current_date = datetime.now()

        for i in range(periods):
            if period_type == 'month':
                period_end = current_date - timedelta(days=30*i)
                period_start = period_end - timedelta(days=30)
                period_label = period_end.strftime("%Y-%m")
            elif period_type == 'quarter':
                period_end = current_date - timedelta(days=90*i)
                period_start = period_end - timedelta(days=90)
                period_label = f"Q{((period_end.month-1)//3)+1} {period_end.year}"
            else:  # year
                period_end = current_date - timedelta(days=365*i)
                period_start = period_end - timedelta(days=365)
                period_label = str(period_end.year)

            # Get income statement for this period
            income_stmt = self.generate_income_statement(
                period_start.strftime("%Y-%m-%d"),
                period_end.strftime("%Y-%m-%d")
            )

            # Get cash position at end of period
            balance_sheet = self.generate_balance_sheet(period_end.strftime("%Y-%m-%d"))
            cash_balance = sum(
                item['balance'] for item in balance_sheet['assets']['current_assets']
                if item['type'] in ['Cash', 'Checking Account']
            )

            trend_data['periods'].insert(0, period_label)
            trend_data['revenue_trend'].insert(0, income_stmt['revenue']['total_revenue'])
            trend_data['expense_trend'].insert(0, income_stmt['expenses']['total_expenses'])
            trend_data['net_income_trend'].insert(0, income_stmt['net_income'])
            trend_data['cash_trend'].insert(0, cash_balance)

        return trend_data
